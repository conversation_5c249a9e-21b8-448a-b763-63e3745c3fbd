<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <link rel="icon" href="/favicon.ico">
  <title>SmartDeer｜全球好工作，直接谈！</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" lang="ts">
    import { createApp } from 'vue'
    import App from '@/layouts/h5-layout.vue'
    import router from '@/router/h5-router'
    const app = createApp(App)
    app.use(router)
    app.mount('#app')
  </script>
</body>
</html>