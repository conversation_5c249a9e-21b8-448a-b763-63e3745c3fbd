@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body, 
#app {
  min-height: 100vh;
  margin: 0;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ant-drawer-header {
  padding: 16px 24px;
}

:where(.ant-drawer-body) {
  padding: 0;
}

.ant-table-pagination {
  padding: 0 16px;
}

.ant-back-top {
  right: 38px;
  bottom: 100px;
}

canvas { 
  display: block;
}

img {
  display: block;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  margin-block-start: 0;
  margin-block-end: 0;
}

pre {
  display: block;
  unicode-bidi: embed;
  white-space: pre-wrap;
  font-family: inherit;
  margin: 0 0;
}

@font-face {
  font-family: 'Bebas';
  src: url('@/fonts/BebasNeue-Regular.ttf');
}

