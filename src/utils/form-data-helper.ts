
interface areaItem {
  areaCode: string
  areaName: string
  areaNameEn: string
  id: number
  parentId: number
  sortWeight: number
}

// 将从dictionary中获取的area的dict数据，转换为ant-design-vue的tree-select的结构的数据
// 为什么这么做：
//  1. 实际使用的时，有时候是用ID来持久化，有时候使用code进行持久化，
//  2. 数据源是通过ID来描述父子关系的。
//  因此，需要将数据结构转换为tree-select使用的父子结构，并支持可以返回code的数据结构
export function areaDictToTreeData(data: areaItem[], withRemote:boolean = false) {
  // industy 
  const areaMap = new Map()
  const tempAreaList = [] as any[]

  data.forEach((item: areaItem, index: number) => {
    if (item.id === 99999999 && withRemote === false) return
    const targetObj = Object.assign({}, item, { title: item.areaName, children: [] })
    areaMap.set(item.id, targetObj)
    if (item.parentId === 0) tempAreaList.push(targetObj)
  })

  areaMap.forEach((item, key) => {
    if (item.parentId === 0) return
    const parent = areaMap.get(item.parentId)
    parent.children.push(item)
  })

  return [tempAreaList, areaMap]
}

interface industryItem {
  id: number
  industryCode: string
  industryName: string
  industryNameEn: string
  parentId: number
}

export function industryDictToTreeData(data: industryItem[]) {
  // industy 
  const dictIndustryMap = new Map()
  const tempIndustryList = [] as any[]

  data.forEach((item: industryItem, index: number) => {
    const targetObj = Object.assign({}, item, { title: item.industryName, children: [] })
    dictIndustryMap.set(item.id, targetObj)
    if (item.parentId === 0) tempIndustryList.push(targetObj)
  })

  dictIndustryMap.forEach((item, key) => {
    if (item.parentId === 0) return
    const parent = dictIndustryMap.get(item.parentId)
    parent.children.push(item)
  })
  return [tempIndustryList, dictIndustryMap]
}