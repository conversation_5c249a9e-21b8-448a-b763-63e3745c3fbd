import exp from "constants";
import dayjs from "dayjs";

export function replaceBlankString(
  string: string,
  replacement: string,
  suffix: string
) {
  if (suffix === undefined) {
    suffix = "";
  }
  if (string === "") {
    return replacement;
  }
  return string + suffix;
}

/**
 * 从生日数据中获取年龄
 */
export function getAgeFromBirthday(strDate: string) {
  if (!strDate) {
    return "年龄未知";
  } else {
    const dateArr = strDate.split("-");
    const currentYear = new Date().getFullYear();
    return currentYear - Number(dateArr[0]) + " 岁";
  }
}

/**
 * 从数据中获取差值
 */
export function getFromTimeBirthday(strDate: string) {
  if (!strDate) {
    return "年龄未知";
  } else {
    if (Number(strDate) === 0) return "年龄未知";

    const year = new Date(Number(strDate) * 1000).getFullYear();

    const currentYear = new Date().getFullYear();
    return currentYear - year + "岁";
  }
}

export function getFromYearMonthBirthday(strYearMonth: number) {
  if (!strYearMonth) {
    return "年龄未知"
  } else {
    const currentYear = Number(new Date().getFullYear());
    const year = parseInt(strYearMonth.toString().substring(0, 4));
    return currentYear - year + "岁"
  }
}

/**
 * 从数据中获取差值
 */
export function getFromTimeBeginWorkDate(strDate: string) {
  if (!strDate) {
    return "未知工作年限";
  } else {
    if (Number(strDate) === 0) return "未知工作年限";

    const year = new Date(Number(strDate) * 1000).getFullYear();

    const currentYear = new Date().getFullYear();
    return currentYear - year + "年工作年限";
  }
}

export function getShortDate(strDate: string) {
  if (!strDate) return "未知";
  else {
    const dateArray = strDate.split("-");
    if (dateArray.length >= 3) {
      dateArray.splice(2, 1);
      return dateArray.join(".");
    } else {
      return dateArray.join(".");
    }
  }
}

export function urlEncodeFormData(e: any) {
  let t = "";
  function n(e: string) {
    return encodeURIComponent(e).replace(/%20/g, "+");
  }
  for (const i of e.entries()) {
    "string" == typeof i[1] && (t += (t ? "&" : "") + n(i[0]) + "=" + n(i[1]));
  }
  return t;
}
