
const SALARY_UNIT_DICT = [
  {
    "name": "人民币",
    "id": 0,
    "key": "RMB"
  },
  {
    "name": "美元",
    "id": 1,
    "key": "USD"
  },
  {
    "name": "USDT",
    "id": 2,
    "key": "USDT"
  },
  {
    "name": "USDC",
    "id": 3,
    "key": "USDC"
  },
  {
    "name": "欧元",
    "id": 4,
    "key": "EUR"
  },
  {
    "name": "新加坡元",
    "id": 5,
    "key": "SGD"
  },
  {
    "name": "港币",
    "id": 6,
    "key": "HKD"
  },
  {
    "name": "英镑",
    "id": 7,
    "key": "GBP"
  },
  {
    "name": "印度卢比",
    "id": 8,
    "key": "INR"
  },
  {
    "name": "澳大利亚币",
    "id": 9,
    "key": "AUD"
  },
  {
    "name": "马来西亚林吉特",
    "id": 10,
    "key": "MYR"
  },
  {
    "name": "日元",
    "id": 11,
    "key": "JPY"
  },
  {
    "name": "俄罗斯卢布",
    "id": 12,
    "key": "RUB"
  },
  {
    "name": "加拿大元",
    "id": 13,
    "key": "CAD"
  },
  {
    "name": "新西兰币",
    "id": 14,
    "key": "NZD"
  },
  {
    "name": "瑞士法郎",
    "id": 15,
    "key": "CHF"
  },
  {
    "name": "南非兰特",
    "id": 16,
    "key": "ZAR"
  },
  {
    "name": "韩元",
    "id": 17,
    "key": "KRW"
  },
  {
    "name": "迪拉姆",
    "id": 18,
    "key": "AED"
  },
  {
    "name": "里亚尔",
    "id": 19,
    "key": "IRR"
  },
  {
    "name": "福林",
    "id": 20,
    "key": "HUF"
  },
  {
    "name": "兹罗提",
    "id": 21,
    "key": "PLN"
  },
  {
    "name": "丹麦克朗",
    "id": 22,
    "key": "DKK"
  },
  {
    "name": "瑞典克朗",
    "id": 23,
    "key": "SEK"
  },
  {
    "name": "挪威克朗",
    "id": 24,
    "key": "NOK"
  },
  {
    "name": "里拉",
    "id": 25,
    "key": "TRY"
  },
  {
    "name": "比索",
    "id": 26,
    "key": "CUP"
  },
  {
    "name": "泰铢",
    "id": 27,
    "key": "THB"
  }
]


enum SALARY_UNIT {
  CNY = 0,
  USD = 1
}

enum SALARY_CALC_UNIT {
  DAY = 1,
  MONTH = 0,
  YEAR = 2
}

type Salary = {
  // 薪资描述信息
  salaryTo: number
  salaryFrom: number
  salaryTime: number
  salaryTimeUnit: SALARY_CALC_UNIT
  salaryUnit: SALARY_UNIT
}

/**
 * [TODO] : 这里需要统一从接口来，具体怎么改动需要统一规划，目前使用的地方比较多。
 */
const SALARY_UNIT_MAP = new Map()
SALARY_UNIT_DICT.forEach((item, index) => {
  SALARY_UNIT_MAP.set(item.id, item.name)
})

export function formatSalary(salary: Salary) {
  const unit = SALARY_UNIT_MAP.get(salary.salaryUnit)

  if (salary.salaryTimeUnit === SALARY_CALC_UNIT.DAY) {
    return `${salary.salaryFrom.toFixed(2)} - ${salary.salaryTo.toFixed(2)} ${unit} 每日`
  } else if (salary.salaryTimeUnit === SALARY_CALC_UNIT.MONTH) {
    return `${(salary.salaryFrom / 1000).toFixed(1)}k - ${(salary.salaryTo / 1000).toFixed(1)}k ${unit} 每月`
  } else if (salary.salaryTimeUnit === SALARY_CALC_UNIT.YEAR) {
    return `${(salary.salaryFrom / 1000).toFixed(1)}k - ${(salary.salaryTo / 1000).toFixed(1)}k ${unit} 每年`
  } else {
    return ``
  }
}