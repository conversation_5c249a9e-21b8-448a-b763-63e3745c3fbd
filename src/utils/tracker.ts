import request from "@/utils/request"
import { v4 } from 'uuid'
import { trackEvent } from '@/api/track'
import storage from "store";
import cookies from "js-cookie"

const COMPANY_USER = "CompanyUserDto";
const companyUser = storage.get(COMPANY_USER) || {};

const EVENT_BASE = {
  clientTime: 0,
  app: 'itp-management-web',
  version: '1.0.0',
  os: '',
  osVersion: '',
  networkType: '',
  companyUserId: companyUser.id 
}

const TRACK_ID_COOKIE_KEY = 'trackid'

export class EventTracker {
  private _uuid: string
  constructor() {
    this._uuid = cookies.get(TRACK_ID_COOKIE_KEY) || v4()
    cookies.set(TRACK_ID_COOKIE_KEY, this._uuid)
  }

  private tack(event: ICB.TrackEvent) {
    trackEvent([event])
  } 

  event(eventId: string, data: any) {
    const trackData = Object.assign({}, data, {url: location.href})
    const event: ICB.TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_event',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }

  pageview(pagename:string, data?: any) {
    const trackData = Object.assign({}, data || {}, {url: location.href})
    const event: ICB.TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: pagename,
      eventType: 'page_view',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }

  exception(eventId: string, data: any) {
    data['url'] = location.href
    const trackData = Object.assign({}, data, {
      url: location.href,
    })
    const event: ICB.TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_exception',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }

  click(eventId: string, data: any) {
    const trackData = Object.assign({}, data, {url: location.href})
    const event: ICB.TrackEvent = Object.assign({}, EVENT_BASE, {
      eventId: eventId,
      eventType: 'page_click',
      eventContent: JSON.stringify(trackData),
      clientTime: new Date().getTime(),
      uuid: this._uuid
    })
    this.tack(event)
  }
}

const tracker = new EventTracker()

export default tracker