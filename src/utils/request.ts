import axios, { AxiosRequestConfig } from 'axios'
import { notification } from 'ant-design-vue'
import { useUserStore } from '@/store/user.store'
import tracker from '@/utils/tracker'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: import.meta.env.VITE_VUE_APP_API_BASE_URL,
  // 请求超时时间
  timeout: 50000
})

// 异常拦截处理器
const errorHandler = (error: { response: { data: any; status: any, config:any }, message: string, config: any }) => {
  if (error.response) {
    const data = error.response.data
    const status = error.response.status
    const config = error.config

    tracker.exception('response-error', {
      message: error.message,
      api: `${config.baseURL}${config.url}`,
      method: config.method,
      status: status,
      query: config.params,
      body: config.data
    })

    if (status === 401 && !(data.result && data.result.isLogin)) {
      notification.error({
        message: 'Unauthorized',
        description: 'Authorization verification failed'
      })

      const userStore = useUserStore()
      userStore.logout()
    }
  }
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use((config: AxiosRequestConfig<any>) => {
  const userStore = useUserStore()
  const token = userStore.token

  if (config.headers) {
    config.headers['Authorization'] = token
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
  } else {
    config.headers = {
      'Authorization': token,
      'Content-Type': 'application/json;charset=UTF-8'
    }
  }
  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use((response) => {
  // 如果返回的code不为0， 则表示该次请求失败。会以error抛出
  if (response.data && response.data.code !== 0) {
    const error = new Error(response.data.message)
    tracker.exception('response-failed', {
      message: response.data.message,
      api: response.config.url,
      code: response.data.code
    })
    return Promise.reject(Object.assign(error, { response }))
  } else {
    return response.data
  }
}, errorHandler)

export default request
