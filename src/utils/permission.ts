import { useUserStore } from '@/store/user.store'
import { debug } from 'console';

export const PERMISSIONS = {
  home: {
    code: 'home',
    title: '首页',
    children: {}
  },
  operation: {
    code: 'operation',
    title: '运营管理',
    children: {
      promotion: {
        code: 'operation:promotion',
        title: '推广管理',
      },
      leads: {
        code: 'operation:leads',
        title: '线索管理'
      },
      promotion_edit: {
        code: 'operation:promotion:edit',
        title: '推广编辑',
      },
      leads_edit: {
        code: 'operation:leads:eidt',
        title: '线索编辑'
      }
    }
  },
  performance: {
    code: 'performance',
    title: '业绩管理',
    children: {
      mine: {
        code: 'performance:mine',
        title: '我的业绩'
      },
      project: {
        code: 'performance:project',
        title: '项目业绩'
      }
    }
  },
  talent: {
    code: 'talent',
    title: '人才管理',
    children: {
      mine: {
        code: 'talent:mine',
        title: '我的人才'
      },
      company: {
        code: 'talent:company',
        title: '公司人才'
      },
      all: {
        code: 'talent:all',
        title: '全部人才'
      },
      add: {
        code: 'talent:add',
        title: '新增人才'
      },
      unite: {
        code: 'talent:unite',
        title: '全网搜'
      }
    }
  },
  project: {
    code: 'project',
    title: '职位管理',
    children: {
      mine: {
        code: 'project:mine',
        title: '我的项目'
      },
      company: {
        code: 'project:company',
        title: '全部项目'
      },
      platform: {
        code: 'project:platform',
        title: '项目广场'
      },
      add: {
        code: 'project:add',
        title: '新增项目'
      }
    }
  },
  customer: {
    code: 'customer',
    title: '客户管理',
    children: {
      mine: {
        code: 'customer:mine',
        title: '我的客户'
      },
      company: {
        code: 'customer:company',
        title: '全部客户'
      },
      add: {
        code: 'customer:company:edit',
        title: '新增客户'
      }
    }
  },
  statistic: {
    code: 'statistic',
    title: '数据统计',
    children: {
      ca: {
        code: 'statistic:ca',
        title: 'CA 统计表'
      },
      bd: {
        code: 'statistic:bd',
        title: 'BD 统计表'
      },
      pm: {
        code: 'statistic:pm',
        title: 'PM 统计表'
      },
      offer: {
        code: 'statistic:offer',
        title: 'Offer 统计表'
      },
      finance: {
        code: 'statistic:finance',
        title: '财务统计表'
      }
    }
  },
  finance: {
    code: 'finance',
    title: '财务管理',
    children: {
      mine: {
        code: 'finance:invoice:mine',
        title: '我的开票列表'
      },
      invoice: {
        code: 'finance:invoice',
        title: '开票列表'
      },
      revenue: {
        code: 'finance:revenue',
        title: '回款单列表'
      },
      claim: {
        code: 'finance:claim',
        title: '回款认领'
      },
    }
  },
  company: {
    code: 'company',
    title: '公司管理',
    children: {
      staff: {
        code: 'company:staff',
        title: '员工管理'
      },
      role: {
        code: 'company:role',
        title: '角色设置'
      },
      department: {
        code: 'company:department',
        title: '部门设置'
      },
      permission: {
        code: 'company:permission',
        title: '权限设置'
      },
      introduce: {
        code: 'company:introduce',
        title: '公司介绍'
      },
      performance: {
        code: 'performance:company',
        title: '公司业绩分配'
      }
    }
  },
  mini_program: {
    code: 'mini_program',
    title: '小程序管理',
    children: {
      industry: {
        code: 'mini_program:industry',
        title: '行业知识'
      }
    }
  }
}

export function hasPermission(permission: string) {
  const useStore = useUserStore();
  for (const permissionKey in useStore.permissions) {
    const permissionCode = useStore.permissions[permissionKey]
    const checkPermissionCode = 'companyId:' + useStore.companyId + ':' + permission
    const checkParentPermissionCode: string[] = permission.split(":");
    let checkAllPermission = []
    for (const key in checkParentPermissionCode) {
      checkAllPermission.push(checkParentPermissionCode[key] + ':*') // 主类目管理员
    }
    checkAllPermission.push('companyId:' + useStore.companyId + ":*") // 公司管理员
    checkAllPermission.push('*') // 超级管理员
    if (permissionCode === checkPermissionCode || checkAllPermission.indexOf(permissionCode) !== -1) {
      return true
    }
  }
  return false
}