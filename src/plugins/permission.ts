import { useUserStore } from '@/store/user.store'
import { App, inject } from 'vue'

const PERMISSION_KEY = 'itp_permission'

function check(key:string):boolean {
  const userStore = useUserStore()
  const permission = userStore.permissions

  // 这里是简单处理permission，目前暂时接受一个单字符串，后续进行扩展。
  return permission.includes(key)
}

const directive = {
  created(el:HTMLElement, binding:any) {
    if (!check(binding.value)) {
      // 删除自己
      el.parentNode!.removeChild(el)
    }
    return
  }
}

export default {
  install: (app:App) => {
    // 初始化自定义指令
    app.directive('can', directive)

    // 注入全局函数
    app.config.globalProperties.$can = (key:string) => {
      return check(key)
    }

    // 全局注入，以便后续可以进行 usePermission
    app.provide(PERMISSION_KEY, {can: check})
  }
}

export function usePermission() {
  return inject(PERMISSION_KEY)
}