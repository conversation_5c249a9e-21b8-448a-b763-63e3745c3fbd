<template lang="pug">

mixin linked-invoice-list
  .invoice-list
    .list-action
      a-button(@click="status.showInvoiceSelect = true" type="primary") 添加票据

    .list-data
      a-table(
        :columns="claimListColumn"
        :dataSource="invoiceList"
        :pagination="false"
      )
        template(#bodyCell="{ column, record, index }")
          template(v-if="column.key === 'grandTotal'")
            span {{ (record.invoice.grandTotal / 100).toFixed(2) }} {{ record.invoice.currencyTypeKey }}

          template(v-if="column.key === 'remainingAmount'")
            span {{ (record.offer.remainingAmount / 100).toFixed(2) }} {{ record.invoice.currencyTypeKey }}

          template(v-if="column.key === 'allocate'")
            a-input-number(v-model:value="statementDetails[index].claimAmount" :step="0.01" :max="record.offer.remainingAmount / 100" :min="0")
              template(#addonAfter)
                span {{ detail.currencyKey }}

        template(#summary)
          a-table-summary-row 
            a-table-summary-cell
            a-table-summary-cell
            a-table-summary-cell
            a-table-summary-cell
            a-table-summary-cell
            a-table-summary-cell
            a-table-summary-cell
              span 总计认领金额：
              span(v-if="totalClaim > 0") {{ totalClaim.toFixed(2) }} {{ detail.currencyKey }}
            a-table-summary-cell
              a-button(@click="handleClaimConfirm" ghost type="primary" :loading="status.claiming") 认领回款

mixin payment-detail
  a-spin(:spinning="status.loading")
    .payment-detail
      a-descriptions(title="回款信息" bordered size="middle")
        a-descriptions-item(label="付款主体" :labelStyle="{color: '#999'}")
          span {{ detail.payAccountName }}
        a-descriptions-item(label="收款主体" :labelStyle="{color: '#999'}")
          span {{ detail.companyFinanceAccount.companyFullName }}
        a-descriptions-item(label="收款银行" :labelStyle="{color: '#999'}")
          span {{ detail.companyFinanceAccount.bankFullName }}
        a-descriptions-item(label="收款方式" :labelStyle="{color: '#999'}")
          span {{ detail.payMethodStr }}
        a-descriptions-item(label="收款日期" :labelStyle="{color: '#999'}" span="2")
          span {{ detail.dueDate }}
        a-descriptions-item(label="收款金额" :labelStyle="{color: '#999'}")
          span {{ (detail.totalAmount / 100).toFixed(2) }} {{ detail.currencyKey }}
        a-descriptions-item(label="已认领金额" :labelStyle="{color: '#999'}")
          span {{ ((detail.totalAmount - detail.remainingAmount) / 100).toFixed(2) }} {{ detail.currencyKey }}
        a-descriptions-item(label="待认领金额" :labelStyle="{color: '#999'}")
          span {{ (detail.remainingAmount / 100).toFixed(2) }} {{ detail.currencyKey }}

.payment-invoice-link
  a-page-header(
    title="回款认领",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )

  +payment-detail
  +linked-invoice-list

  InvoiceSelector(
    v-if="status.showInvoiceSelect"
    v-model:open="status.showInvoiceSelect" 
    @select="handleInvoiceSelect"
    :params="{companyUserIds: userStore.isAdmin ? null : [userStore.id], finaceAccount: detail.companyFinanceAccount.id}"
  )
  
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef } from 'vue'
import InvoiceSelector from '@/components/app/invoice-selector.vue'
import { claimPayment, getPaymentClaimDetail } from '@/api/finance'
import { Modal, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/user.store'
import { useRouter } from 'vue-router'
import { computed } from 'vue'
import { onActivated } from 'vue'
const router = useRouter()

const props = defineProps<{ id: number }>()
const claimId = toRef(props, 'id')
const userStore = useUserStore()

const status = reactive({
  showInvoiceSelect: false,
  claiming: false,
  loading: false
})

const claimListColumn = [
  { title: "发票抬头", dataIndex: ['invoice', 'customerName'], key: "customerName", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  { title: "开票主体", dataIndex: ['invoice', 'vendorName'], key: "vendorName", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  { title: "开票金额", dataIndex: ['invoice', 'totalAmount'], key: "grandTotal", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  {
    title: "OFFER", key: 'offer', children: [
      { title: '职位', dataIndex: ['offer', 'positionTitle'], key: 'positionTitle' },
      { title: '姓名', dataIndex: ['offer', 'talentRealName'], key: 'talentRealName' },
      { title: '待收', dataIndex: ['offer', 'remainingAmount'], key: 'remainingAmount' },
      { title: '实收', key: 'allocate' },
    ]
  },
  { title: "发起人", dataIndex: ['invoice', 'applyUserRealName'], key: "applyUserRealName", width: 100, customCell: (cell: any) => ({ rowSpan: cell.span }) },
]

const invoiceList = ref<any>([])
const statementDetails = ref<any>([])
const detail = ref({
  companyFinanceAccount: {},
  remainingAmount: 0,
  totalAmount: 0
})

const totalClaim = computed(() => {
  let total = 0
  statementDetails.value.forEach((item: any) => {
    total += item.claimAmount
  })

  return total
})

async function processInvoiceList(list: any) {
  // [TODO] 重复添加票据后，需要去重
  invoiceList.value = []
  let total = detail.value.remainingAmount

  list.forEach((invoice: any) => {
    const { financeInvoiceDetails } = invoice

    financeInvoiceDetails.forEach((offer: any, index: number) => {
      const value = Math.min(total, offer.remainingAmount)
      invoiceList.value.push({
        offer: offer,
        invoice: invoice,
        span: index === 0 ? financeInvoiceDetails.length : 0
      })

      statementDetails.value.push({
        invoiceDetailId: offer.id,
        invoiceId: invoice.id,
        claimAmount: value / 100,
        remainingAmount: offer.remainingAmount,
      })

      total -= offer.remainingAmount
    })
  })
}
function handleInvoiceSelect(list: any[]) {
  invoiceList.value = []
  statementDetails.value = []
  processInvoiceList(list)
  status.showInvoiceSelect = false
}

async function claimPaymentConfirm() {
  status.claiming = true
  try {
    const params = statementDetails.value.map((item: any) => {
      return {
        claimAmount: item.claimAmount * 100,
        claimId: claimId.value,
        invoiceDetailId: item.invoiceDetailId,
        invoiceId: item.invoiceId
      }
    })
    const res = await claimPayment(claimId.value, params)
    message.success('认领成功')
    router.go(-1)

  } catch (err: any) {
    message.error(err.message)
  }
  status.claiming = false
}


async function handleClaimConfirm() {
  try {
    if (totalClaim.value <= 0) throw new Error('请选择至少一张票据')
    // 每一条，申领金额不能大于待领金额
    if ((totalClaim.value * 100) > detail.value.remainingAmount) throw new Error('认领金额不能大于待认领金额')
    statementDetails.value.forEach((item: any) => {
      if (item.claimAmount > item.remainingAmount) throw new Error('认领金额不能大于待认领金额')
    })

    Modal.confirm({
      title: '认领回款',
      content: '确认认领回款？',
      onOk: claimPaymentConfirm
    })

  } catch (err: any) {
    message.error(err.message)
  }
}

async function fetchPaymentClaimDetail(claimId: number) {
  status.loading = true
  try {
    const res = await getPaymentClaimDetail(claimId)
    detail.value = res.data
    console.log(detail.value)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

onActivated(() => {
  fetchPaymentClaimDetail(claimId.value)
  invoiceList.value = []
  statementDetails.value = []
})

</script>

<style lang="sass" scoped>
.payment-detail
  border-radius: 8px
  background-color: #fff
  padding: 16px
  margin-bottom: 16px

.invoice-list
  border-radius: 8px
  .list-action
    padding: 16px
    text-align: right
  
</style>