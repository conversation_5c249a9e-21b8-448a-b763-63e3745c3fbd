<template lang="pug">
mixin page-header
  a-page-header(
    title="回款单",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      //- a-button(type="primary" @click="() => status.showCreatePayment = true") 新增回款

mixin statement-filter
  .filter-section
    a-row(:gutter="[12, 12]")
      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            allow-clear
            show-search
            :filter-option="false"
            placeholder="客户"
            v-model:value="searchParams.customerId"
            :dropdownMatchSelectWidth="false"
            :options="options.customer"
            @search="fetchCustomerList"
            @change="() => fetchStatementList()"
          )
      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-range-picker(
            style="width: 100%"
            v-model:value="dataRange.paymentDateRange"
            :presets="options.ranges"
            :placeholder="['回款日期','回款日期']"
            @change="() => fetchStatementList()"
          )

      a-col(:xl="12" :lg="8" :md="0" :sm="0")

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="候选人姓名"
            v-model:value="searchParams.talentRealName"
            @search="() => fetchStatementList()"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="项目名称"
            v-model:value="searchParams.positionTitle"
            @search="() => fetchStatementList()"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="开票抬头"
            v-model:value="searchParams.payAccountName"
            @search="() => fetchStatementList()"
          ) 

mixin statement-list
  a-table(
    :columns="columns"
    :data-source="statementList"
    :pagination="pagination"
    :loading="status.searchLoading"
    @change="handlePageChange"
  )
    template(#bodyCell="{record, column}")
      template(v-if="column.key=== 'totalAmount'")
        span {{ (record.totalAmount/100).toFixed(2) }} {{ record.currencyKey }}

      template(v-if="column.key === 'offer'")
        div(v-for="(offer, index) in record.financeStatementDetails" :key="offer.id")
          a-space(:size="12")
            .offer-info
              label 职位: 
              span {{ offer.positionTitle }}
            .offer-info
              label 姓名: 
              span {{ offer.talentRealName }}
            .offer-info
              label 应收: 
              span {{ (offer.totalAmount / 100).toFixed(2) }} {{ record.currencyKey }}
            .offer-info()
              label 实收: 
              span {{ (offer.actualAmount / 100).toFixed(2) }} {{ record.currencyKey }}

.statement-list-page
  +page-header
  +statement-filter
  +statement-list
</template>

<script lang="ts" setup>
import { getStatementList } from '@/api/finance'
import { getDateRanges } from '@/utils/util'
import { message } from 'ant-design-vue'
import { reactive, ref } from 'vue'

import FilterItem from '@/components/ui/filter-item.vue'
import { getCustomerList } from '@/api/customer'
import { onActivated } from 'vue'
import { Dayjs } from 'dayjs'



const status = reactive({
  searchLoading: false
})
const statementList = ref<any[]>([])

const columns = [
  { title: '客户', dataIndex: ['customer', 'customerFullName'], key: 'customerFullName' },
  { title: '收款账户', dataIndex: ['companyFinanceAccount', 'companyFullName'], key: 'companyFullName' },
  { title: '收款金额', dataIndex: ['totalAmount'], key: 'totalAmount' },
  { title: '收款日期', dataIndex: 'payDate', key: 'payDate' },
  { title: 'OFFER', key: 'offer' },
]
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

const options = reactive({
  ranges: getDateRanges(),
  customer: [] as any[],
})

const searchParams = reactive({
  current: 0,
  size: 0,
  customerId: null,
  payAccountName: "",
  positionTitle: "",
  talentRealName: "",
  endDate: "",
  startDate: "",
})

async function handlePageChange(pageInfo:any) {
  pagination.current = pageInfo.current
  fetchStatementList()
}

async function fetchStatementList() {
  status.searchLoading = true
  try {
    const params = {
      customerId: searchParams.customerId ? [searchParams.customerId] : undefined,
      payAccountName: searchParams.payAccountName || undefined,
      positionTitle: searchParams.positionTitle || undefined,
      talentRealName: searchParams.talentRealName || undefined,
      endDate: dataRange.paymentDateRange ? dataRange.paymentDateRange[1].format('YYYY-MM-DD') : undefined,
      startDate: dataRange.paymentDateRange ? dataRange.paymentDateRange[0].format('YYYY-MM-DD') : undefined,
      current: pagination.current,
      size: pagination.pageSize
    }
    const res = await getStatementList(params)
    statementList.value = res.data.financeStatements
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.searchLoading = false
}

const dataRange = reactive({
  paymentDateRange: null as null | Dayjs[]
})

const fetchCustomerList = async (name: string = '') => {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    isSearchProject: true,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })

  options.customer = list
}

function handleDateRangeChange(key: string) {
  if (key === 'paymentDate') {
    if (!dataRange.paymentDateRange || dataRange.paymentDateRange.length === 0) {
      searchParams.startDate = ''
      searchParams.endDate = ''
    } else {
      searchParams.startDate = dataRange.paymentDateRange[0].format('YYYY-MM-DD')
      searchParams.endDate = dataRange.paymentDateRange[1].format('YYYY-MM-DD')
    }
  }

  searchParams.current = 1
  fetchStatementList()
}

onActivated(() => {
  fetchStatementList()
})

</script>

<style lang="sass" scoped>
.filter-section
  background-color: #fff
  padding: 16px
  border-radius: 8px
  margin-bottom: 16px

.offer-info
  label
    color: #999
</style>