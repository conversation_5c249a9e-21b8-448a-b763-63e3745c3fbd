

<template lang="pug">
.payment-claim-list-page
  a-page-header(
    title="回款认领单列表",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      a-button(type="primary" @click="() => status.showCreatePaymentClaim = true") 创建回款认领单

  section.search-filter
    a-form(:model="queryParams" ref="searchFormInstance")
      a-row(:gutter="[12, 12]")
        a-col(:xl="6" :lg="8" :md="12" :sm="24")
          FilterItem()
            a-range-picker(
              :placeholder="['收款时间', '收款时间']"
              v-model:value="dates.dateRange"
              :bordered="false"
              :presets="options.dateRanges"
              style="width: 100%"
              @change="fetchPaymentClaimList"
            )

        a-col(:xl="6" :lg="8" :md="12" :sm="24")
          FilterItem()
            a-select(
              style="width: 100%"
              option-label-prop="label"
              v-model:value="queryParams.financeAccountId"
              placeholder="请选择收款账户"
              allow-clear
              @change="fetchPaymentClaimList"
            )
              template(v-for="(item, index) in options.accountEntityList")
                a-select-option(
                  :value="item.id" 
                  :label="`${item.companyFullName} - ${item.bankFullName}`"
                )
                  .companyEntity {{ item.companyFullName }}
                  .compnayAccount {{ item.bankFullName }}

        a-col(:xl="6" :lg="8" :md="12" :sm="24")
          FilterItem(label="")
            a-input-search(
              placeholder="付款主体"
              v-model:value="queryParams.payAccountName"
              @search="fetchPaymentClaimList"
            )

  CreatePaymentClaimModal(v-if="status.showCreatePaymentClaim" v-model:open="status.showCreatePaymentClaim" @update="handleCreateClaimUpdate")

  a-table(
    :columns="columnsList" 
    :data-source="tableList" 
    :pagination="pagination"
    @change="(pagination) => {pageChange(pagination)}"
    rowClassName="clickable"
    rowKey="id"
    :loading="status.tableLoading"
  )
    template(#bodyCell="{ text, record, index, column }")
      template(v-if="column.key === 'actions'")
        a-space(:gutter="[12, 12]")
          a-button(v-if="record.remainingAmount >= 0" @click="() => handleInvoiceLink(record)" type="primary") 认领


      template(v-if="column.key=='financeAccountId'")
        span {{ record.companyFinanceAccount.companyFullName }}
        br
        span {{ record.companyFinanceAccount.bankFullName }}

      template(v-if="column.key=='totalAmount'")
        span {{ (record.totalAmount/100).toFixed(2) }} {{ record.currencyKey }}

      template(v-if="column.key === 'remainingAmount'")
        span {{ (record.remainingAmount/100).toFixed(2) }} {{ record.currencyKey }}

      template(v-if="column.key === 'status'")
        a-tag(v-if="record.remainingAmount == record.totalAmount" color="#ff9111") 待认领
        a-tag(v-if="(record.remainingAmount < record.totalAmount) && (record.remainingAmount > 0)" color="#108ee9") 部分认领
        a-tag(v-if="record.remainingAmount == 0" color="#87d068") 已认领
  
    template(#expandedRowRender="{ record }")
      template(v-for="item in record.claimDetails")
        p 申领人：{{ item.claimUserId }}, 申领金额: {{  (item.claimAmount / 100).toFixed() }}


</template>

<script lang="ts" setup>
import { ref, reactive, onActivated } from 'vue'
import { getStatementList, getStatementHeader, setConfirmPayment, getStatementDetails, getPaymentClaimList, getEntityList } from '@/api/finance'
import dayjs, { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'
import { getPayMethodList } from '@/api/dictionary'
import CreatePaymentForm from '@/components/app/create-payment-form.vue'
import router from '@/router'
import FilterItem from '@/components/ui/filter-item.vue'
import { getDateRanges } from '@/utils/util'
import { onMounted } from 'vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import CreatePaymentClaimModal from '@/components/app/create-payment-claim-modal.vue'

const status = reactive({
  tableLoading: false,
  showCreatePaymentClaim: false,
})

const queryParams = reactive({
  current: 0,
  size: 0,
  financeAccountId: null,
  payAccountName: '',
  endDueDate: '',
  startDueDate: ''
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const columnsList = [
  { title: '状态', key: 'status' },
  { title: '付款主体', dataIndex: 'payAccountName' },
  { title: '收款主体', dataIndex: 'financeAccountId', key: 'financeAccountId'},
  { title: '收款日期', dataIndex: 'dueDate' },
  { title: '金额', dataIndex: 'totalAmount', key: 'totalAmount'},
  { title: '待领认领金额', dataIndex: 'remainingAmount', key: 'remainingAmount' },
  { title: '操作', dataIndex: '', key: 'actions' },
]

const tableList = ref([] as any[])

const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize

  await fetchPaymentClaimList()
}

const handleChangeFilter = () => {
  pagination.current = 1
  fetchPaymentClaimList()
}

function handleInvoiceLink(paymentClaim: any) {
  console.log(paymentClaim)
  router.push(`/finance/payment/claim/${paymentClaim.id}/claim`)
}

const options = reactive({
  accountEntityList: [] as any[],
  dateRanges: getDateRanges(),
})


const dates = reactive({
  dateRange: [] as Dayjs[],
})

function handleCreateClaimUpdate() {
  status.showCreatePaymentClaim = false
  fetchPaymentClaimList()
}

async function initDict() {
  try {
    const [entityListRes] = await Promise.all([
      getEntityList(),
    ])
    options.accountEntityList = entityListRes.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function fetchPaymentClaimList() {
  status.tableLoading = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      financeAccountId: queryParams.financeAccountId ? queryParams.financeAccountId : undefined,
      payAccountName: queryParams.payAccountName ? queryParams.payAccountName : undefined,
      // isCompleted: true,
      startDueDate: dates.dateRange && dates.dateRange.length ? dates.dateRange[0].format('YYYY-MM-DD') : undefined,
      endDueDate: dates.dateRange && dates.dateRange.length ? dates.dateRange[1].format('YYYY-MM-DD') : undefined,
    }
    const res = await getPaymentClaimList(params)
    tableList.value = res.data.financeStatementClaims
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.tableLoading = false
}

onMounted(() => {
  initDict()
})

onActivated(()=>{
  fetchPaymentClaimList()
})

</script>

<style lang="scss" scoped>
.search-filter {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
}

.file {
  width: 100px;
}
</style>