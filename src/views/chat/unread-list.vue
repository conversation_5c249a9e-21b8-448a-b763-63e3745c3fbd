<template lang="pug">

mixin page-header
  a-page-header(
    title="未读消息列表",
    sub-title="",
    style="padding: 0 0 8px;"
    @back="()=> {$router.go(-1)}"
  )

mixin filter
  .filter-area

mixin talent-table-config
  template(v-if="column.key === 'talent'")
    .talent-info(v-if="record.talent.profileBasic" @click="() => handleShowTalentDetail(record.talent)")
      .talent-avatar
        a-avatar(:src="`${record.talent.profileBasic.formalPhotoUrl}?imageMogr2/crop/96x96/gravity/center`" :size="48")
      .talent-info-container
        .talent-name 
          .name {{ record.talent.profileBasic.realName }}
          a-tag {{ record.talent.profileBasic.gender == 1 ? '男' : '女' }}
          a-tag(v-if="!record.talent.imAccount || !record.talent.imAccount.imUserId == 'DEFAULT'" color="orange")  NO-IM
        .talent-info {{ getTalentBasicInfo(record.talent) }}

  template(v-if="column.key === 'work'")
    .exp-list(v-if="record.talent.profileExperience && Array.isArray(record.talent.profileExperience)")
      .exp-item(v-for="(item, index) in record.talent.profileExperience")
        .exp-item-date {{ item.startDate && dayjs(item.startDate).format('YYYY.MM') }} - {{ item.endDate == 'TO_PRESENT' ? '至今'  :  item.endDate && dayjs(item.endDate).format('YYYY.MM') }}
        .exp-item-info
          .exp-item-info-company {{ item.companyName }} · {{ item.positionTitle }}

  template(v-if="column.key === 'education'")
    .exp-list(v-if="record.talent.profileEducation && Array.isArray(record.talent.profileEducation)")
      .exp-item(v-for="(item, index) in record.talent.profileEducation")
        .exp-item-date {{ dayjs(item.startDate).format('YYYY.MM') }} - {{ item.endDate == 'TO_PRESENT' ? '至今'  : dayjs(item.endDate).format('YYYY.MM') }}
        .exp-item-info
          .exp-item-info-company {{ item.schoolName }} · {{ item.educationStr }}

  template(v-if="column.key === 'expectation'")
    .expectation(v-for="(intention, index) in record.talent.profileIntentions")
      .expect-job 
        em {{ intention.desiredPosition  }}
        span {{ ' · ' }}
        span {{ formatSalary(intention) }}
      .expect-info {{ intention.workTimeStr?.join('') }} · {{ intention.workPreferenceStr }} · {{ intention.desiredCityStr?.join(', ') }}

mixin double-chat-list-table
  a-table(
    :columns="columnsConfig" 
    :data-source="doubleChatTalentList" 
    :pagination="doubleChatListPagination"
    @change="(pagination)=>{handelDoubleChatListPageChange(pagination)}"
    :loading="status.doubleChatListLoading"
    :scroll="{ x: 1800 }"
  )
    template(#bodyCell="{ column, record }")
      +talent-table-config
      template(v-if="column.key==='action'")
        a-button(@click="()=>showChatBox(record.talent)") 聊天

mixin unread-message-list-table
  a-table(
    :columns="columnsConfig" 
    :data-source="myFollowedUnreadTalentList" 
    :pagination="myFollowedUnreadTalentPagination"
    @change="(pagination)=>{handelFollowedTalentUnreadListPageChange(pagination)}"
    :loading="status.followedTalentUnreadListLoading"
    :scroll="{ x: 1800 }"
  )
    template(#bodyCell="{ column, record }")
      +talent-table-config
      template(v-if="column.key==='action'")
        a-button(@click="()=>showChatBox(record.talent)") 聊天


mixin talent-list-tab
  a-tabs(v-model:activeKey="activeTab" @change="handleTabChange")
    a-tabPane(key="3" tab="双聊")
      +double-chat-list-table
    a-tabPane(key="4" tab="未读人才")

.talent-chat-list
  +page-header
  a-spin(:spinning="status.loading")
    +unread-message-list-table

  a-drawer(
    v-model:open="status.showChatBox",
    title="聊天",
    :destroyOnClose="true",
    :width="480"
    :bodyStyle="{padding: 0}"
  )
    ChatBox(:from="chatUsers.from" :to="chatUsers.to")


  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")


</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import dayjs from 'dayjs'
import { message} from 'ant-design-vue'
import ChatBox from '@/components/chat/chat-box.vue'
import { getUnReadMessageByImUserId } from '@/api/chat'
import { useRoute } from 'vue-router'
import { Item } from 'ant-design-vue/es/menu'
import { saveSmartDeerAppTalent } from '@/api/talent/talent'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'

// const FROM_USER = import.meta.env.VITE_VUE_APP_BUILD_ENV == 'production' ?  'smart:0:smartdeer' : 'test:0:smartdeer'
const FROM_USER = 'smart:0:smartdeer'

const route = useRoute()
const talentList = ref<any[]>([])
const activeTab = ref('1')

const status = reactive({
  latestTalentListLoading: false,
  followedTalentListLoading: false,
  followedTalentUnreadListLoading: false,
  doubleChatListLoading: false,
  loading: false,
  showTalentDetail: false,
  showChatBox: false,
  btnLoading: false,
  showWorkorderModal: false,
})

const queryParams = reactive({
  realName: '',
  accountId: ''
})

const columnsConfig = ref([
  { title: '人才', key: 'talent', fixed: 'left', with: 240 },
  { title: '求职期望', key: 'expectation', width: 400 },
  { title: '工作经历', key: 'work', width: 500 },
  { title: '教育经历', key: 'education', width: 500 },
  { title: '操作', key: 'action', fixed: 'right', width: 140 },
])

const selectImUserId = ref('')
const talentId = ref('')

// 最新人才
const latestTalentList = ref<{ talent: any }[]>([])
const latestTalentPagination = reactive<any>({
  current: 1,
  total: 0,
  pageSize: 10,
})

// 我的人才
const myFollowedUnreadTalentList = ref<any[]>([])
const myFollowedUnreadTalentPagination = reactive<any>({
  current: 1,
  total: 0,
  pageSize: 10,
})

async function handleShowTalentDetail(talent:any) {
  status.loading = true
  try {
    const res = await saveSmartDeerAppTalent(talent.profileBasic.accountId)
    talentId.value = res.data.talent.id
    status.showTalentDetail = true
  } catch (err:any) {
    message.error(err.message)
  }
  status.loading = false
}

async function getFollowedTalentUnreadList() {
  status.followedTalentUnreadListLoading = true
  try {
    const { data } = await getUnReadMessageByImUserId(FROM_USER, myFollowedUnreadTalentPagination)
    myFollowedUnreadTalentList.value = data.dataInfo.filter((item:any) => item.individualProfile).map((item: any) => {
      if (!item.individualProfile) console.log(item)
      return { leadsId: item.leadsId, talent: item.individualProfile || {} }
    })
    myFollowedUnreadTalentPagination.total = data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.followedTalentUnreadListLoading = false
}
async function handelFollowedTalentUnreadListPageChange(page: any) {
  myFollowedUnreadTalentPagination.current = page.current
  myFollowedUnreadTalentPagination.pageSize = page.pageSize
  await getFollowedTalentUnreadList()
}

const doubleChatTalentList = ref<any[]>([])
const doubleChatListPagination = reactive<any>({
  current: 1,
  total: 0,
  pageSize: 10,
})

// async function getDoubleChatList() {
//   status.doubleChatListLoading = true
//   try {
//     const { data } = await API.talent.getDoubleChatList(doubleChatListPagination)
//     doubleChatTalentList.value = data.dataInfo.map((item:any)=>({leadsId: item.leadsId, talent: item.profileVo}))
//     doubleChatListPagination.total = data.total
//   } catch (err: any) {
//     message.error(err.message)
//   }
//   status.doubleChatListLoading = false
// }

// async function handelDoubleChatListPageChange(page:any) {
//   doubleChatListPagination.current = page.current
//   doubleChatListPagination.pageSize = page.pageSize
//   await getDoubleChatList()
// }

async function handleTabChange(key: string) {
  switch (key) {
    case '4':
      myFollowedUnreadTalentPagination.current = 1
      myFollowedUnreadTalentPagination.pageSize = 10
      await getFollowedTalentUnreadList()
      break
    case '3':
      doubleChatListPagination.current = 1
      doubleChatListPagination.pageSize = 10
      // await getDoubleChatList()
      break
  }
}

const chatUsers = reactive({
  from: {},
  to: {}
})

function showChatBox(talent: any) {

  chatUsers.to = {
    imUserId: talent.imAccount.imUserId,
    nick: talent.profileBasic.realName,
    avatar: talent.profileBasic.formalPhotoUrl
  }
  chatUsers.from = {
    imUserId: FROM_USER,
    nick: 'Jobs（乔布斯）',
    avatar: 'https://global-image.smartdeer.work/p/images/0x47b67a41f6324d2ea85c03270fe0064d.jpeg_median'
  }

  status.showChatBox = true
}

enum TabType {
  latest = 0,
  followed = 1,
  unread = 2
}

function getWorkExpirence(talent: any) {
  if (!talent.profileExperience || !Array.isArray(talent.profileExperience)) return 0
  const profileExperience = talent.profileExperience
  // find the oldest experience
  const oldestExperience = profileExperience.reduce((prev: any, current: any) => {
    if (prev.startDate < current.startDate) {
      return prev
    }
    return current
  }, profileExperience[0])
  if (!oldestExperience) return 0
  return dayjs().diff(oldestExperience.startDate, 'year')
}

function getTalentBasicInfo(talent: any) {
  return `${getAge(talent.profileBasic.birthday)}岁 · ${getWorkExpirence(talent)}年经验 · ${talent.profileBasic.educationStr} · ${talent.profileBasic.locationStr || '未知'} `
}

function getAge(birthday: any) {
  return dayjs().diff(birthday, 'year')
}

function formatSalary(params: any) {
  if (params.salaryFrom && params.salaryTo)
    return `${params.salaryFrom?.toLocaleString()} ~ ${params.salaryTo.toLocaleString()} ${params.salaryUnitStr} / ${params.salaryTimeUnitStr}`
}

onMounted(() => {
  getFollowedTalentUnreadList()
})

</script>

<style lang="sass" scoped>
.search-filter 
  background: white
  padding: 16px

.talent-info
  display: flex
  align-items: center
  cursor: pointer

  .talent-info-container
    white-space: nowrap
    .talent-name
      display: flex
      margin-bottom: 4px
      .name
        font-weight: bold
        margin-right: 8px

  .talent-avatar
    flex: 0 0 auto
    margin-right: 8px
    width: 48px
    height: 48px
    border-radius: 50%
    overflow: hidden

.exp-list, .edu-list
  .exp-item
    // white-space: nowrap
    display: flex
    .exp-item-date
      width: 132px
      white-space: nowrap
      color: #777
      margin-right: 8px

.expectation
  margin: 12px 0

  .expect-job
    color: #999
    em
      font-weight: bold
      color: #333
      font-style: normal

  .expect-info
    color: #999

.position-list
  display: flex
  align-items: center
  overflow-x: scroll
  align-items: stretch


  .position-item
    padding: 8px 12px
    flex: 0 0 auto
    cursor: pointer

    &.active
      background-color: RGBA(251,241,237,1)	
    .position-title
      margin-bottom: 8px
      font-weight: bold

    .recruiter
      align-items: center
      display: flex

      .info
        margin-left: 8px
        font-size: 12px
        color: RGBA(0,0,0,.6)

</style>