<template lang="pug">
.user-dashboard
  Wellcome 
  a-row(:gutter="[16, 16]")
    a-col(:span="24")
      RecommendJobs
      //FinanceStatistic(@number-click="handleFinanceStatisticClick")
      PipelineStatistic(@number-click="handlePipelineStatisticClick")
      JobRequirementStatistic(@number-click="handleJobRequirementStatisticClick" v-if="isPlatformAdmin" )
      PerformanceStatistics(@number-click="handlePreformanceDetailClick")
        template(#action)
          a-button(@click="handlePreformanceDetailClick") 详情
</template>

<script lang="ts" setup>
import PerformanceStatistics from '@/components/user-home/performance-statistic.vue'
import Wellcome from '@/components/user-home/wellcome.vue'
import RecommendJobs from '@/components/user-home/recommend/index.vue'
import { useRouter } from 'vue-router'
import PipelineStatistic from '@/components/user-home/pipeline-statistic.vue'
import { useUserStore } from '@/store/user.store'
import JobRequirementStatistic from '@/components/user-home/job-requirement-statistic.vue'
import { getPipelineStatsDetail } from '@/api/stat'
import FinanceStatistic from '@/components/user-home/finance-statistic.vue'

const router = useRouter()
const userStorage = useUserStore()
const isPlatformAdmin = userStorage.userType === 2
const isCompanyAdmin = userStorage.isAdmin

function handlePreformanceDetailClick() {
  router.push('/performance/me')
}

async function handlePipelineStatisticClick(taskDefinitionKey: any, abroad: any, employeeType: any, fromDate: any, toDate: any) {
  console.log(taskDefinitionKey, abroad, employeeType, fromDate, toDate)
  const res = await getPipelineStatsDetail({
    taskDefinitionKey: taskDefinitionKey,
    fromDate: fromDate,
    toDate: toDate,
    abroad: abroad,
    employeeType: employeeType,
    offset: 9999999,
    size: 20
  }, userStorage.companyId)
}

function handleJobRequirementStatisticClick(payload: any) {
  router.push('/job/list' + payload)
}

function handleFinanceStatisticClick() {

}

</script>

<style lang="sass" scoped>
.user-dashboard
  & > * 
    margin-bottom: 16px
</style>