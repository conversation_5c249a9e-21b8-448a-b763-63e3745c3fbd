<template lang="pug">
.spin-container
  Spin(:indicator="indicator")

//- .talent-touch
//-   .logo
//-     img(alt="smartdeer-logo" border="0" src="https://static.smartdeer.com/global/seo/_next/static/media/logo-dark.a52e85a7.svg" style="height: 32px;")

//-   template(v-if="!status.success")
//-     h1 欢迎您！
//-     p 很高兴能够与您建立联系。

//-     .label 请留下您的联系方式，我将会尽快与您联系。
//-     Form(:model="form" ref="formInstance")
//-       FormItem(name="mobile" :rules="[{type: 'string', required: true, pattern: /^[\\d]+$/, message:'请填写正确的手机号码'}]")
//-         Input(v-model:value="form.mobile" placeholder="请输入您的手机号码" size="large" :maxlength="11")

//-     Button(block size="large" type="primary" @click="handleSubmit" :loading="status.loading") 提交

//-     .notice 
//-       InfoCircleOutlined.icon
//-       span 我们非常重视您的隐私，将严格保密您的个人信息，不会泄露给任何第三方。

//-   template(v-else)
//-     h1 感谢您的信任！
//-     p 我们已经收到您的联系方式，我们会尽快与您联系。
</template>

<script lang="ts" setup>
import { h, onMounted, ref } from 'vue'
import axios from 'axios'
import { reactive } from 'vue'
import { Form, FormItem, Input, Button, message, Spin } from 'ant-design-vue'
import { InfoCircleOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { useRoute } from 'vue-router'
import { toRef } from 'vue'

const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '32px',
  },
  spin: true,
})

const API_BASE = import.meta.env.VITE_VUE_APP_API_BASE_URL

const props = defineProps<{
  talentId: number,
  jobId: number,
  uuid: string,
}>()

const talentId = toRef(props, 'talentId')
const jobId = toRef(props, 'jobId')
const uuid = toRef(props, 'uuid')

const status = reactive({
  success: false,
  loading: false,
})

const form = reactive({
  name: '',
  email: '',
  mobile: ''
})

const request = axios.create({
  baseURL: import.meta.env.VITE_VUE_APP_API_BASE_URL + '/company/1'
})

function sendMobileNotification() {
  const messages:string[] = []
  if (emailInfo.value) {
    messages.push(`用户 ${emailInfo.value.realName} 对这个职位感兴趣，并提交了联系方式: ${form.mobile}`)
    messages.push(`职位: ${emailInfo.value.positionTitle}.`)
    messages.push(`客户: ${emailInfo.value.customerFullName}.`)
    messages.push(`PM: ${getJobProperty(emailInfo.value.jobRequirement, 'pm').join(', ')}`)
    messages.push(`CA: ${getJobProperty(emailInfo.value.jobRequirement, 'ca').join(', ')}`)
  } else {
    messages.push(`有用户提交了联系方式,手机号:${form.mobile}`)
  }
  sendNotification(messages, jobId.value, talentId.value)
}

function getJobProperty(job:any, key: string):string[] {
  if (!job) return []
  const value:string[] = []
  job.properties.forEach((property:any) => {
    if (property.key === key) {
      value.push(property.valueName)
    }
  })
  return value
}

async function sendNotification(messages: string[], positionId: number, talentId: number) {
  const notificationHeadr = {
    "template": "blue",
    "title": { "content": "Biu!邮件响应通知", "tag": "plain_text" }
  }

  const actions = [
    { title: '查看人才', url: `https://web.itp.smartdeer.work/talent/${talentId}/detail` },
    { title: '查看项目', url: `https://web.itp.smartdeer.work/job/${positionId}/detail` }
  ]
  const notificationActions = {
    "tag": "action",
    "actions": actions.map(action => ({
      "tag": "button",
      "text": { "content": action.title, "tag": "plain_text" },
      "url": action.url
    }))
  }

  const notificationMessages = messages.map(message => ({
    "tag": "div",
    "text": { "content": message, "tag": "plain_text" }
  }))

  await axios.post('https://open.feishu.cn/open-apis/bot/v2/hook/1bcf396d-c2cd-4efa-9ad8-7b94923befc6', {
    msg_type: "interactive",
    card: {
      "header": notificationHeadr,
      "elements": [
        ...notificationMessages,
        notificationActions
      ],
    }
  })
  await axios.post('https://open.feishu.cn/open-apis/bot/v2/hook/f8c15e6e-58be-4ecc-8d96-171a91681012', {
    msg_type: "interactive",
    card: {
      "header": notificationHeadr,
      "elements": [
        ...notificationMessages,
        notificationActions
      ],
    }
  })
}

async function sendOpenNotification() {
  const messages:string[] = []
  if (emailInfo.value) {
    messages.push(`用户 ${emailInfo.value.realName} 打开了邀请邮件的‘接受邀请按钮’`)
    messages.push(`职位: ${emailInfo.value.positionTitle}`)
    messages.push(`客户: ${emailInfo.value.customerFullName}`)
    messages.push(`PM: ${getJobProperty(emailInfo.value.jobRequirement, 'pm').join(', ')}`)
    messages.push(`CA: ${getJobProperty(emailInfo.value.jobRequirement, 'ca').join(', ')}`)
  } else {
    messages.push(`有用户打开了'接受邀请按钮'`)
  }
  await sendNotification(messages, jobId.value, talentId.value)
}

const formInstance = ref<any>(null)

async function handleSubmit() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await sendMobileNotification()
    status.success = true
  } catch (err: any) {
    console.log(err)
  }
  status.loading = false
}

const emailInfo = ref<{
  salaryRange: string
  companyUserEmail: string
  positionTitle: string
  functionName: string
  trackingUrl: string
  companyUserWechat: string
  positionAreaName: string
  emailSubject: string
  uuid: string
  companyUserMobile: string
  educationRequired: string
  realName: string
  experienceRequired: string
  talentEmail: string
  companyUserName: string
  acceptInviteUrl: string,
  jobRequirement: any,
  customerFullName: string,
} | null>()
async function fetchEmailInfo(uuid: string) {
  try {
    const { data } = await axios.get(`${API_BASE}/talent/invite/email/uuid/${uuid}`)
    emailInfo.value = data.data
  } catch (err: any) {
    emailInfo.value = null
    console.log(err)
  }
}

onMounted(async () => {
  await fetchEmailInfo(uuid.value)
  await sendOpenNotification()
  window.location.href = `https://www.smartdeer.com/app?channelCode=itpemail&eid=${encodeURIComponent(uuid.value)}`
})

</script>

<style lang="sass" scoped>

.spin-container
  display: flex
  justify-content: center
  align-items: center
  height: 100vh

.talent-touch
  padding: 24px
  font-size: 16px
  .logo
    margin-bottom: 80px

  .label
    font-size: 16px
    margin-bottom: 32px

  .notice 
    margin-top: 24px
    color: #999
    font-size: 13px
    line-height: 20px

    .icon
      margin-right: 8px
</style>