<template lang="pug">
.position-detail-page
  .position-summary
    .head
      h2.title {{ position.positionTitle}}
      .salary 
        .range {{ salaryRange }}
        .unit {{  salaryUnit }}
    .basic-info

      template(v-if="position.canRemote") 
        img.icon(src="@/assets/h5/icon-remote.svg")
        span 可远程

      template(v-if="position.areaStr")
        img.icon(src="@/assets/h5/icon-location.svg")
        span {{ position.areaStr }}

      template(v-if="position.requireWorkYearsStr && position.requireWorkYearsStr !=='未知'")
        img.icon(src="@/assets/h5/icon-time.svg")
        span {{ position.requireWorkYearsStr }}

      template(v-if="position.requireDgreeStr")
        img.icon(src="@/assets/h5/icon-education.svg")
        span {{ position.requireDgreeStr }}

      template(v-if="position.isManager")
        img.icon(src="@/assets/h5/icon-manager.svg")
        span 管理岗

  .spliter

  .company(v-if="position.customer")
    .logo
      span {{ position.customer?.customerFullName[0] }}
      img(:src="position.customer?.customerLogo")
    .title {{ position.customer?.customerFullName }}
    .desc {{ position.customer?.industryStr}} · {{ position.customer?.companyScaleStr }} · {{ position.customer?.investStatusStr }}

  .spliter

  .position-description
    .title 职位详情
    pre {{ position.workDetail }}

</template>

<script lang="ts" setup>
import axios from 'axios'
import { computed, onMounted, reactive, ref } from 'vue'
import { salaryTimeUnit, currencyUnit } from './dict'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'

const props = defineProps<{ id: string }>()

const request = axios.create({
  baseURL: import.meta.env.VITE_VUE_APP_API_BASE_URL + '/company/1',
})

const status = reactive({
  loading: true,
})

const position = ref<any>({})

async function getPositionDetail(positionId: number | string) {
  status.loading = true
  try {
    const res = await request.get(`public/position/${positionId}`, {
      params: { positionId },
    })
    position.value = res.data.data
  } catch (err: any) {
  }
}

async function getCompanyDetail(companyId: string) {
  try {
    const res = await request.get(`/customer/${companyId}`)
    position.value.customer = res.data.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const salaryRange = computed(() => {
  if (position.value.salaryFrom <= 0) {
    return '薪资面议'
  } else {
    return position.value.salaryFrom?.toLocaleString() + '-' + position.value.salaryTo?.toLocaleString()
  }
})

const salaryUnit = computed(() => {
  if (position.value.salaryFrom <= 0) {
    return ''
  } else {
    return `${currencyUnit.get(position.value.salaryUnit)} / ${salaryTimeUnit.get(position.value.salaryTimeUnit)}`
  }
})

onMounted(async () => {
  const route = useRoute()
  const positionId = route.params.id as string
  await getPositionDetail(positionId)
})

</script>

<style lang="sass">
html, body
  padding: 0
  margin: 0
</style>

<style lang="sass" scoped>
.position-detail-page
  padding-bottom: 40px
  --primary-color: #FF6C00

  .position-summary
    padding: 24px 20px
    font-size: 12px
    background-color: #282f40
    color: #fff
    position: sticky
    top: 0
    z-index: 1

    .head
      line-height: 1.4
      .title
        font-size: 24px
        font-weight: bold
        word-break: break-all
        padding: 0
        margin: 0
        margin-bottom: 12px
        
      .salary
        font-size: 16px
        color: var(--primary-color)
        line-height: 1
        margin-bottom: 16px

        .range
          font-size: 16px
          font-weight: bold
          margin-right: 8px
          vertical-align: middle
          display: inline-block
        .unit
          color: RGBA(255, 255, 255, 0.6)
          font-size: 12px
          display: inline-block
          vertical-align: middle

    .basic-info
      line-height: 20px
      margin: -4px 0

      img.icon
        display: inline-block
        margin-right: 4px
        vertical-align: middle
        height: 14px
        width: 14px
      span
        vertical-align: middle
        margin-right: 12px

  .company
    padding: 20px
    position: relative
    padding-left: 80px
    display: flex
    flex-direction: column
    justify-content: center
    min-height: 48px
    .logo
      position: absolute
      left: 20px
      height: 48px
      width: 48px
      background: RGBA(0,0,0,.2)
      border-radius: 12px
      display: flex
      align-items: center
      justify-content: center
      color: #fff
      font-size: 20px

    .title
      font-size: 18px
      font-weight: bold
      margin-bottom: 4px

    .desc
      font-size: 12px
      line-height: 1.4

  .spliter
    margin: 0px 20px
    background-color: #d8d8d8
    height: 0.5px

  .position-description
    padding: 20px
    font-size: 14px
    .title
      font-size: 18px
      margin-bottom: 12px
      font-weight: bold

    pre
      white-space: pre-wrap
      font-family: inherite
      line-height: 1.62
      font-size: 14px
</style>