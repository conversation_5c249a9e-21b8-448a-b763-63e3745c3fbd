<template lang="pug">
mixin page-header
  a-page-header(
    title="业绩分配",
    sub-title="",
    @back="()=>$router.go(-1)",
    style="padding: 0 0 8px"
  )
    template(#extra)
      a-alert(message="预期业绩金额仅为参考，实际金额以实际分配为准" type="info" show-icon)

mixin offer-config
  .offer-config
    CommissionAllocator(:processInstanceId="processInstanceId")

mixin job-summary
  a-spin(:spinning="status.jobInfoLoading")
    .job-summary
      InfoSection(title="项目信息" :editable="false" @edit="()=> {status.showJobUpdate = true}")
        .job-summary__head
          .job-summary__item
            a-space(:size="8")
              JobPriority(:priority="jobRequirement.priority")
              strong {{ positionDetail?.positionTitle || '-' }}
          .job-summary__item
            a-space(:size="24") 
              span 状态: {{ getJobStatusStr(jobRequirement.status) }}
              span HC: {{ positionDetail?.quantityRequired }} 个
              span 工作地点: {{ positionDetail?.areaStr }}
              span 税前薪资: {{formatSalary(positionDetail)}}
          .job-summary__item
            a-space(:size="24") 
              span BD: {{ getUsers('bd') }}
              span PM: {{ getUsers('pm') }}
              span CA: {{ getUsers('ca') }}
          .job-summary__item(v-if="positionDetail?.autoTags")
            a-space
              a-tag(v-if="positionDetail.tags.length" v-for="(tag, index) in positionDetail.tags" :key="`t_${index}`") {{tag.name}}

mixin talent-summary
  .talent-summary
    InfoSection(title="人才信息")
      span 阿斯顿发斯柯达积分

mixin job-logs
  InfoSection(title="流程记录")
    JobLog(:talentId="talentId" :processInstanceId="processInstanceId" :jobRequirementId="jobId" :showAction="false")

mixin offer-detail
  .offer-detail
    +job-summary
    //- +talent-summary
    +job-logs

.offer-commission-config
  +page-header
  .page-body
    a-row(:gutter="[16, 16]")
      a-col(:span="16")
        +offer-config
      a-col(:span="8")
        +offer-detail
</template>

<script lang="ts" setup>
import { onActivated, reactive, ref, toRef } from 'vue'
import CommissionAllocator from '@/components/app/commission-allocator.vue'
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'
import { getJobStatus } from '@/api/dictionary'
import { formatSalary } from '@/utils/salary-helper'
import InfoSection from '@/components/info-section/info-section.vue'
import { getCompanyPerformanceConfig, getTaskContributors } from '@/api/performance'
import JobLog from '@/components/app/job-log.vue'
import JobPriority from '@/components/app/job-priority.vue'

const props = defineProps<{ processInstanceId: string, jobId: string }>()
const processInstanceId = toRef(props, 'processInstanceId')
const jobId = toRef(props, 'jobId')

const status = reactive({
  jobInfoLoading: false,
})

const jobRequirement = ref<any>({})

const positionDetail = ref<any>({ tags: [] })
async function fetchPositionDetail(positionId: number) {
  try {
    const res = await getPositionDetail(positionId)
    positionDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function fetchJobRequirementDetail(jobId: string) {
  status.jobInfoLoading = true
  try {
    const res = await getJobRequirementDetail(jobId)
    jobRequirement.value = res.data
    fetchPositionDetail(res.data.positionId)
  } catch (err: any) {
    message.error(err.message)
  }
  status.jobInfoLoading = false
}

function getUsers(type: 'bd' | 'pm' | 'ca') {
  const users: any[] = []
  jobRequirement.value.properties?.forEach((item: any, index: number) => {
    if (item.key?.toLowerCase() === type) {
      users.push(item.valueName)
    }
  })
  return users.join(' / ')
}

let jobStatusDict = [] as any[]
function getJobStatusStr(status: number) {
  const statusDict = jobStatusDict.find((element) => {
    return element.value === status
  })
  if (statusDict) return statusDict.label
  else return ''
}

async function getCommissionConfig() {

}

const performancePointMap = new Map()

onActivated(async () => {
  const statusRes = await getJobStatus()
  jobStatusDict = statusRes.data
  fetchJobRequirementDetail(props.jobId)
})

</script>

<style lang="sass" scoped>
.summary
  margin-bottom: 16px

.offer-detail
  background-color: #fff
  border-radius: 8px

.job-summary 
  background: #FFFFFF
  border-radius: 4px

  &__head 
    position: relative

  &__offen
    position: absolute
    right: 26px
    top: 32px
    height: 20px
    line-height: 20px
    cursor: pointer
    user-select: none
    z-index: 9

    .anticon 
      margin-left: 8px
      color: #BFBFBF
      font-size: 12px

  &__item 
    line-height: 32px

    strong 
      font-size: 20px

    &:last-child 
      margin-bottom: 0

</style>