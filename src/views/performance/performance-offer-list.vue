<template lang="pug">
mixin page-header
  a-page-header(
    title="项目业绩",
    sub-title="",
    @back="()=>$router.go(-1)",
    style="padding: 0 0 8px"
  )

mixin filter-section
  .search-filter
    FilterItem(label="时间")
      a-range-picker(
        v-model:value="queryParams.date"
        :bordered="false"
        :presets="filter.ranges"
        @change="(value:any) => { handleFilterChange('dateType', value) }"
      )

mixin statistics
  .offer-statistics
    //-列表
    a-spin(:spinning="status.searchLoading")
      .position-search
        a-table(
          :columns="columnsList" 
          :data-source="dataList" 
          :pagination="false"
          :scroll="{y: winHeightStore.value - 284 }" 
        )
          template(#bodyCell="{ text, record, index, column }")
            template(v-if="column.key === 'job'")
              .job-info(@click="showJobDetail(record.jobRequirement.id)")
                .priorty
                  JobPriority(:priority="record.jobRequirement.priority")
                .info
                  .job-name {{ record.jobRequirement.processName }}
                  .customer-info {{ record.customer.customerFullName }}

            template(v-if="column.key==='talent'")
              .talent-info(@click="showTalentDetail(record.talent.id)")
                .avatar
                  a-avatar(:src="record.talent.photo") {{ record.talent.realName[0] }}
                .content
                  .name {{ record.talent.realName }}
                  .info {{ getTalentBasicInfo(record.talent).join(' · ') }}

            template(v-if="column.key === 'offer'")
              .offer
                span.salary {{ parseInt(record.salary).toLocaleString() }} 
                span.unit {{ record.currencyType }} · {{ record.salaryUnit }}
                //- .expect-onboard {{ `预期入职: ${record.expectOnboardingDate}` }}
                //- .onboard(v-if="record.onboardingDate") {{ `实际入职: ${record.onboardingDate}` }}

            template(v-if="column.key === 'commission'")
              .commission
                span.amount {{ parseInt(record.expectCommission).toLocaleString() }}
                span.unit {{ record.commissionCurrencyType }}

            template(v-if="column.key==='status'")
              .job-status
                template(v-if="record.onboardingDate == '未入职'")
                  .status
                    a-tag 未入职
                    span 预期时间: {{ record.expectOnboardingDate }}
                template(v-else)
                  .status
                    a-tag(color="#03E3B0") 已入职
                    span 入职时间:{{ record.onboardingDate }}
                template(v-if="record.onboardingDate != '未入职'")

                  template(v-if="record.overInsuranceDate == '未过保'")
                    .status 
                      a-tag 未过保
                  template(v-else)
                    .status
                      a-tag(color="#2665FC") 已过保 
                      span 过保时间:{{ record.overInsuranceDate }}


            template(v-if="column.key === 'action'")
              a(@click="handleConfigClick(record)") 业绩分配


.preformance-offer-list
  +page-header

  .page-body
    +filter-section
    +statistics

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" @close="status.showTalentDetail = null")
    TalentDetailModal(:talentId="status.showTalentDetail" @close="status.showTalentDetail = false")

  //- a-drawer(v-model:open="status.showOfferPerformanceConfig" :destroyOnClose="true" :width="640" title="业绩分配" :bodyStyle="{padding: 0}")
  //-   OfferPerformanceConfig(@cancel="status.showOfferPerformanceConfig = null")

  //- a-drawer(v-model:open="status.showOfferPerformanceDetail" :destroyOnClose="true" :width="480" title="业绩详情" :bodyStyle="{padding: 0}")
  //-   OfferPerformanceDetail()

</template>

<script setup lang="ts">
import { ref, reactive, onActivated } from 'vue'
import { getOffer, getOfferHeader } from '@/api/stat'
import dayjs from 'dayjs'
import { getDateRanges } from '@/utils/util'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import FilterItem from '@/components/ui/filter-item.vue'
import { message } from 'ant-design-vue'
import { useWinHeightStore } from '@/store/winHeight.store'
import JobPriority from '@/components/app/job-priority.vue'
import { useRouter } from 'vue-router'

const winHeightStore = useWinHeightStore()

const queryParams = reactive({
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  dateType: 1
})
const filter = reactive({
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,
  showTalentDetail: null as null | number,
  showOfferPerformanceConfig: null as null | number,
  showOfferPerformanceDetail: null as null | number
})

function handleConfigClick(offer:any) {
  router.push(`/performance/job/${offer.jobRequirement.id}/task/${offer.task.processInstanceId}/commission/config`)
}

function handleDetailClick() {
  status.showOfferPerformanceDetail = 1
}

function getTalentBasicInfo(talent: any) {
  const infoList = []
  if (talent.genderStr) infoList.push(talent.genderStr)

  if (talent.age) infoList.push(`${talent.age}岁`)
  else infoList.push('年龄未知')

  if (talent.workYears) infoList.push(`${talent.workYears}年经验`)

  if (talent.areaId) infoList.push(`${talent.areaStr}`)

  if (talent.latestDegreeStr == '未知') infoList.push(`学历未知`)
  else infoList.push(talent.latestDegreeStr)

  return infoList
}

const columnsList = ref([
  { title: '项目', key: 'job' },
  { title: '候选人', key: 'talent' },
  { title: 'Offer', key: 'offer' },
  { title: '预期佣金', key: 'commission' },
  { title: '当前状态', key: 'status' },
  { title: '操作', key: 'action' }
])
const dataList = ref([])
const dataListSummary = reactive({
  isCompleteDesc: '汇总',
  monthly: '',
  yearly: '',
  expectCommission: ''
})

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}

function showTalentDetail(talentId: number) {
  status.showTalentDetail = talentId
}

const router = useRouter()
function showJobDetail(jobId: number) {
  router.push(`/job/${jobId}/detail`)
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const res = await getOffer({ endTime, startTime, ids: [] })
    dataList.value = res.data.objects
    status.searchLoading = false
  } catch (err: any) {
    message.error(err.message)
    status.searchLoading = false
  }
}

onActivated(() => {
  getList()
})
</script>

<style lang="sass" scoped>

.job-info
  display: flex
  align-items: center
  cursor: pointer
  .info
    .job-name
      font-weight: bold
    .customer-info
      color: #999

.talent-info
  display: flex
  align-items: center
  cursor: pointer

  .avatar
    margin-right: 8px

  .content
    .name
      font-weight: bold

    .info
      color: #999

.offer
  .salary
    font-weight: bold
    color: #ff9111
    margin-right: 8px

.job-status
  margin: -4px 0
  .status
    margin: 4px 0

.commission
  .amount
    font-weight: bold
    color: #ff9111
    margin-right: 8px

.page-body
  border-radius: 8px
  overflow: hidden

.search-filter
  padding: 16px
  background-color: #fff
</style>