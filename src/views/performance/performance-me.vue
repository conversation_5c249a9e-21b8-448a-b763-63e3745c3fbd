<template lang="pug">
mixin page-header
  a-page-header(
    title="我的业绩",
    sub-title="",
    @back="()=>$router.go(-1)",
    style="padding: 0 0 8px"
  )

mixin filter-section
  .time-filter
    a-row()
      a-col(:span="12")
        a-space(:size="8")
          a-radio-group(v-model:value="timeRangeKey" button-style="solid")
            a-radio-button(value="day" @click="handleTimeChange('day')") 今天
            a-radio-button(value="week" @click="handleTimeChange('week')") 本周
            a-radio-button(value="month" @click="handleTimeChange('month')") 本月
            a-radio-button(value="year" @click="handleTimeChange('year')") 本年度

mixin performance-list
  a-spin(:spinning="status.listLoading")
    .performance-list
      a-tabs.performance-tab(v-model:activeKey="filter.type" type="line" @change="handleTabClick")
        a-tab-pane(v-for="(item, index) in tabConfig" :key="item.key" :tab="item.title")

      .performance-list
        a-table(
          :columns="tableColumnConfig"
          :data-source="performanceList"
          :pagination="false"
        )
          template(#bodyCell="{ column, record }")
            template(v-if="column.key === 'job'")
              .job-info(@click="showJobPerformanceDetail(record)")
                .priorty
                  JobPriority(:priority="record.jobRequirement.priority")
                .info
                  .job-name {{ record.jobRequirement.processName }}
                  .customer-info {{ record.jobRequirement.customerName }}

            template(v-if="column.key==='talent'")
              .talent-info(@click="showTalentDetail(record)")
                .avatar
                  a-avatar(:src="record.talent.photo") {{ record.talent.realName[0] }}
                .content
                  .name {{ record.talent.realName }}
                  .info {{ getTalentBasicInfo(record.talent).join(' · ') }}

            template(v-if="column.key === 'performance'")
              span.performance-number ${{ (record.performanceAmount/100).toLocaleString() }}

mixin preformance-statistic
  .preformance-statistic
    a-row.header(:gutter="8")
      a-col(:span="8")
        h3.title 业绩统计
      a-col(:span="16" align="right")

    a-row.statistics(:gutter="[8, 24]")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.offerPerformance" title="Offer业绩" :compareValue="performanceStastic.offerPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.onboardPerformance" title="入职业绩" :compareValue="performanceStastic.onboardPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.invoicePerformance" title="开票业绩" :compareValue="performanceStastic.invoicePerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.paidPerformance" title="回款业绩" :compareValue="performanceStastic.paidPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")

.preformance-offer-list
  +page-header
  a-spin(:spinning="status.loading")
    +filter-section
    +preformance-statistic
    +performance-list

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" @close="status.showTalentDetail = null")
    TalentDetailModal(:talentId="talentDetailParams.talentId" :jobId="talentDetailParams.jobId" @close="status.showTalentDetail = false" )

  //- a-modal(v-model:open="status.showJobPerformanceDetail" title="业绩明细" :destroyOnClose="true" :footer="false" width="60%" @close="status.showJobPerformanceDetail = null")
  //-   JobPerformanceDetail(:processInstanceId="status.showJobPerformanceDetail" :type="filter.type")

</template>

<script lang="ts" setup>
import { getStaffPerformanceList, getStaffPerformanceStatistics } from '@/api/performance'
import StatisticItem from '@/components/user-home/performance-statistic-item.vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import JobPriority from '@/components/app/job-priority.vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { reactive, ref, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { onActivated } from 'vue'

const status = reactive({
  loading: false,
  listLoading: false,
  showTalentDetail: false,
  showJobPerformanceDetail: null as string | null
})

const props = defineProps<{timeRange:string}>()
const timeRangeKey = ref('month')

const talentDetailParams = reactive({
  talentId: null as null | number,
  jobId: null as null | string,
})
function showTalentDetail(record: any) {
  status.showTalentDetail = true
  talentDetailParams.talentId = record.talent.id
  talentDetailParams.jobId = record.jobRequirement.id
}

const router = useRouter()
function showJobDetail(jobId: number) {
  router.push(`/job/${jobId}/detail`)
}

function showJobPerformanceDetail(record:any) {
  router.push(`/performance/job/${record.jobRequirement.id}/process/${record.processInstanceId}/detail`)
}

function getTalentBasicInfo(talent: any) {
  const infoList = []
  if (talent.genderStr) infoList.push(talent.genderStr)

  if (talent.age) infoList.push(`${talent.age}岁`)
  else infoList.push('年龄未知')

  if (talent.workYears) infoList.push(`${talent.workYears}年经验`)

  if (talent.areaId) infoList.push(`${talent.areaStr}`)

  if (talent.latestDegreeStr == '未知') infoList.push(`学历未知`)
  else infoList.push(talent.latestDegreeStr)

  return infoList
}

const tabConfig = [
  { key: 1, title: 'Offer业绩' },
  { key: 2, title: '入职业绩' },
  { key: 3, title: '开票业绩' },
  { key: 4, title: '回款业绩' },
]

const filter = reactive({
  start: '',
  end: '',
  type: 1
})

const performanceList = ref<any[]>([])
const performanceStastic = ref<any>({
  offerPerformance: 0,
  onboardPerformance: 0,
  invoicePerformance: 0,
  paidPerformance: 0
})

const tableColumnConfig = [
  { key: 'job', title: '项目' },
  { key: 'talent', title: '人才' },
  { key: 'date', dataIndex:'allocatedDate', title: '时间' },
  { key: 'performance', title: '业绩' },
]

async function getStaffPerformanceStastic(start: string, end: string) {
  status.loading = true
  try {
    const res = await getStaffPerformanceStatistics(start, end)
    performanceStastic.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function getPerformanceList(start: string, end: string, type: number) {
  status.listLoading = true
  try {
    const res = await getStaffPerformanceList(start, end, type)
    performanceList.value = res.data.performanceItems
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

function getTimeRange(timeRangeKey: string) {
  if (['week', 'month', 'year', 'day'].includes(timeRangeKey)) {
    const now = dayjs()
    const type = timeRangeKey as any
    const start = dayjs().startOf(type).format('YYYY-MM-DD')
    const end = dayjs().endOf(type).format('YYYY-MM-DD')
    return { start, end }
  }

  return {
    start: '',
    end: ''
  }
}

async function handleTabClick(tab: any) {
  filter.type = tab
  getPerformanceList(filter.start, filter.end, filter.type)
}

async function handleTimeChange(timeRangeKey: any) {
  const { start, end } = getTimeRange(timeRangeKey)
  filter.start = start
  filter.end = end
  getStaffPerformanceStastic(start, end)
  getPerformanceList(start, end, filter.type)
}

onMounted(()=>{
  timeRangeKey.value = props.timeRange || 'month'
})

onActivated(() => {
  const { start, end } = getTimeRange(timeRangeKey.value)
  filter.start = start
  filter.end = end

  getStaffPerformanceStastic(filter.start, filter.end)
  getPerformanceList(filter.start, filter.end, filter.type)
})

</script>

<style lang="sass" scoped>

.time-filter
  padding: 20px
  background-color: #fff
  border-radius: 8px
  margin-bottom: 16px
.preformance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px
  margin-bottom: 16px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)

  .time-range
    margin-bottom: 24px

.performance-list
  background-color: #fff
  border-radius: 8px

  .performance-number
    font-family: 'Bebas'
    font-size: 20px

  .performance-tab
    :deep(.ant-tabs-nav)
      margin-bottom: 0

    :deep(.ant-tabs-tab)
      padding: 16px
      font-size: 16px
      margin: 0 8px


.job-info
  display: flex
  align-items: center
  cursor: pointer
  .info
    .job-name
      font-weight: bold
    .customer-info
      color: #999

.talent-info
  display: flex
  align-items: center
  cursor: pointer

  .avatar
    margin-right: 8px

  .content
    .name
      font-weight: bold

    .info
      color: #999
</style>