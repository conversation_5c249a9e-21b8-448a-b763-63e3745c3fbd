<template lang="pug">
mixin page-header
  a-page-header(
    title="项目业绩详情",
    sub-title="",
    @back="()=>$router.go(-1)",
    style="padding: 0 0 8px"
  )

mixin preformance-statistic
  .preformance-statistic
    a-row.header(:gutter="8")
      a-col(:span="8")
        h3.title 项目业绩统计
      a-col(:span="16" align="right")

    a-row.statistics(:gutter="[8, 24]")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.offerPerformance" title="Offer业绩" :compareValue="performanceStastic.offerPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.onboardPerformance" title="入职业绩" :compareValue="performanceStastic.onboardPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.invoicePerformance" title="开票业绩" :compareValue="performanceStastic.invoicePerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")
      a-col(:span="6")
        StatisticItem(:value="performanceStastic.paidPerformance" title="回款业绩" :compareValue="performanceStastic.paidPerformanceLastPeriod" :compareTitle="performanceStastic.lastPeriodName")

mixin performance-list
  a-spin(:spinning="status.listLoading")
    .performance-list
      a-tabs.performance-tab(v-model:activeKey="filter.type" type="line" @change="handleTabClick")
        a-tab-pane(v-for="(item, index) in tabConfig" :key="item.key" :tab="item.title")

      a-table(
        :columns="tableColumnConfig"
        :pagination="false"
        :data-source="performanceList"
      )
        template(#bodyCell="{ column, record }")
          template(v-if="column.key === 'rate'")
            span {{ record.pointRate }}%

          template(v-if="column.key === 'performace'")
            span.performace-value ${{ (record.performanceValue/100).toFixed(2).toLocaleString() }}

mixin job-summary
  a-spin(:spinning="status.jobInfoLoading")
    .job-summary
      InfoSection(title="项目信息" :editable="false" @edit="()=> {status.showJobUpdate = true}")
        .job-summary-head
          .job-summary-item
            a-space(:size="8")
              JobPriority(:priority="jobRequirement.priority")
              h3 {{ positionDetail?.positionTitle || '-' }}
          .job-summary-item
            a-space(:size="[24, 8]" wrap) 
              span 状态: {{ getJobStatusStr(jobRequirement.status) }}
              span HC: {{ positionDetail?.quantityRequired }} 个
              span 工作地点: {{ positionDetail?.areaStr }}
              span 税前薪资: {{formatSalary(positionDetail)}}
          .job-summary-item
            a-space(:size="[24, 8]" wrap) 
              span BD: {{ getUsers('bd') }}
              span PM: {{ getUsers('pm') }}
              span CA: {{ getUsers('ca') }}
          .job-summary__item(v-if="positionDetail?.autoTags")
            a-space
              a-tag(v-if="positionDetail.tags.length" v-for="(tag, index) in positionDetail.tags" :key="`t_${index}`") {{tag.name}}

mixin talent-summary
  InfoSection(title="候选人")
    .talent-summary(@click="handleShowTalentDetail")
      .talent-avatar
        a-avatar(:size="48") {{ talent.realName }}
      .talent-info
        .talent-name {{ talent.realName }}
        .info {{ talentInfos.join(' · ') }}

mixin job-logs
  InfoSection(title="流程记录")
    JobLog(:talentId="talentId" :processInstanceId="processInstanceId" :jobRequirementId="jobId" :showAction="false")

mixin offer-detail
  .offer-detail
    +job-summary
    +talent-summary
    +job-logs

.offer-commission-config
  +page-header
  .page-body
    a-row(:gutter="[16, 16]")
      a-col(:span="16")
        +preformance-statistic
        +performance-list
      a-col(:span="8")
        +offer-detail


a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%")
  TalentDetailModal(:talentId="talentModalParams.talentId" :jobId="talentModalParams.jobRequirementId" @close="talentModalParams.showTalentDetail = false")

</template>

<script lang="ts" setup>
import { onActivated, reactive, ref, toRef } from 'vue'
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'
import { getJobStatus } from '@/api/dictionary'
import { formatSalary } from '@/utils/salary-helper'
import { getCompanyPerformanceConfig, getJobPerformanceDetail, getTaskContributors } from '@/api/performance'
import InfoSection from '@/components/info-section/info-section.vue'
import JobLog from '@/components/app/job-log.vue'
import JobPriority from '@/components/app/job-priority.vue'
import CommissionAllocator from '@/components/app/commission-allocator.vue'
import StatisticItem from '@/components/user-home/performance-statistic-item.vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import dayjs from 'dayjs'
import { computed } from 'vue'

const props = defineProps<{ processInstanceId: string, jobId: number }>()
const processInstanceId = toRef(props, 'processInstanceId')
const jobId = toRef(props, 'jobId')

const status = reactive({
  jobInfoLoading: false,
  listLoading: false,
  showTalentDetail: false,
})

const tableColumnConfig = [
  { key: 'role', title: '角色', dataIndex: ['performancePoint', 'roleName'] },
  { key: 'action', title: '关键动作', dataIndex: ['performancePoint', 'actionName'] },
  { key: 'date', title: '发生时间', dataIndex: 'performanceDate' },
  { key: 'type', title: '业绩类型', dataIndex: 'performanceTypeStr' },
  { key: 'staff', title: '员工', dataIndex: ['companyUser', 'realName'] },
  { key: 'rate', title: '业绩占比', dataIndex: 'pointRate' },
  { key: 'performace', title: '业绩', dataIndex: 'performanceValue' },
]

const tabConfig = [
  { key: 1, title: 'Offer业绩' },
  { key: 2, title: '入职业绩' },
  { key: 3, title: '开票业绩' },
  { key: 4, title: '回款业绩' },
]

const filter = reactive({
  start: '',
  end: '',
  type: 1
})

const jobRequirement = ref<any>({})
const positionDetail = ref<any>({ tags: [] })

async function fetchPositionDetail(positionId: number) {
  try {
    const res = await getPositionDetail(positionId)
    positionDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const talentModalParams = reactive({
  talentId: 0,
  jobRequirementId: 0
})

function handleShowTalentDetail(talentId:number) {
  talentModalParams.talentId = talent.value.id
  talentModalParams.jobRequirementId = jobId.value
  status.showTalentDetail = true
}

const performanceStastic = reactive({
  offerPerformance: 0,
  onboardPerformance: 0,
  invoicePerformance: 0,
  paidPerformance: 0
})

const performanceList = ref([])
const talent = ref<any>({})
const process = ref({})

async function handleTabClick(tabKey: number) {
  fetchJobPerformanceDetail(props.processInstanceId, tabKey)
}

async function fetchJobPerformanceDetail(processInstanceId: string, type: number) {
  status.listLoading = true
  try {
    const res = await getJobPerformanceDetail(processInstanceId, type)

    performanceStastic.offerPerformance = res.data.offerPerformance
    performanceStastic.onboardPerformance = res.data.onboardPerformance
    performanceStastic.invoicePerformance = res.data.invoicePerformance
    performanceStastic.paidPerformance = res.data.paidPerformance
    performanceList.value = res.data.statisticDetails

    talent.value = res.data.talent
    process.value = res.data.pipelineProcessWrapper
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

function getYearDiff(time: string) {
  const now = dayjs()
  const day = dayjs(time)
  return now.diff(day, 'year')
}

function getGender(gender:number) {
  if (gender == 0) return '性别未知'
  if (gender == 1) return '男'
  if (gender == 2) return '女'
}

const talentInfos = computed(() => {
  if (!talent.value) return []

  const infos = []
  const age = `${getYearDiff(talent.value.birthday)}岁`
  const workYears = `${getYearDiff(talent.value.workStartDate)}年经验`
  const gender = getGender(talent.value.gender)
  infos.push(gender, age, workYears)
  return infos
})

async function fetchJobRequirementDetail(jobId: number) {
  status.jobInfoLoading = true
  try {
    const res = await getJobRequirementDetail(jobId)
    jobRequirement.value = res.data
    await fetchPositionDetail(res.data.positionId)
  } catch (err: any) {
    message.error(err.message)
  }
  status.jobInfoLoading = false
}

function getUsers(type: 'bd' | 'pm' | 'ca') {
  const users: any[] = []
  jobRequirement.value.properties?.forEach((item: any, index: number) => {
    if (item.key?.toLowerCase() === type) {
      users.push(item.valueName)
    }
  })
  return users.join(' / ')
}

let jobStatusDict = [] as any[]
function getJobStatusStr(status: number) {
  const statusDict = jobStatusDict.find((element) => {
    return element.value === status
  })
  if (statusDict) return statusDict.label
  else return ''
}

onActivated(async () => {
  const statusRes = await getJobStatus()
  jobStatusDict = statusRes.data
  fetchJobPerformanceDetail(props.processInstanceId, 1)
  fetchJobRequirementDetail(props.jobId)
})
</script>

<style lang="sass" scoped>
.offer-detail
  background-color: #fff
  border-radius: 8px

.job-summary
  .job-summary-head
    .job-summary-item
      margin: 16px 0

.talent-summary
  display: flex
  align-items: center
  .talent-avatar
    flex: 0 0 60px
  .talent-info
    flex: 1 1 100%

    .talent-name
      font-size: 16px
      font-weight: bold
      margin-bottom: 4px


.performance-list
  background-color: #fff
  border-radius: 8px

  .performace-value
    font-family: 'Bebas'
    font-size: 18px

  .performance-tab
    :deep(.ant-tabs-nav)
      margin-bottom: 0

    :deep(.ant-tabs-tab)
      padding: 16px
      font-size: 16px
      margin: 0 8px

.preformance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px
  margin-bottom: 16px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)
</style>