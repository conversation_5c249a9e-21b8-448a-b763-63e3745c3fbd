<template lang="pug">
.role-list-page
  a-page-header(
    title="角色管理"
    sub-title=""
    @back="()=>{$router.go(-1)}"
    style="padding: 0 0 8px;"
  )

  .role-list-page-body
    .role-list
      a-spin(:spinning="status.rolesListLoading")
        .role-item(
          v-for="(role, index) in roleList",
          @click="() => { handleSelectRole(role); }",
          :class="{ active: role.id === selectedRole.id }"
          :key="index"
        )
          .role-item-name 
            a-space(:size="12")
              .name-text {{ role.roleCnName }}
          .role-item-action
            a-dropdown
              MoreOutlined
              template(#overlay)
                a-menu 
                  a-menu-item 修改名称
                  a-menu-item
                    router-link(:to='"/system/roles/" + role.id + "/permission"') 权限设置
                  a-menu-item
                    a-popconfirm(title="确认要删除该角色?" @confirm="() => { deleteRole(role) }")
                      span 删除
      .role-list-action
        a-button(type="primary", ghost, block, @click="handleAddRole") 添加角色

    .staff-list
      .staff-list-actions
        .staff-list-actions__left
          a-input(@change="")
            template(#prefix)
              SearchOutlined

        .staff-list-actions__right
          a-space
            a-popconfirm(
              title="确定要批量移除员工么?"
              ok-text="批量移除"
              cancel-text="取消"
              :disabled="!selectedStaffKeys.length"
              @confirm="()=>{ removeUser(selectedRole.id, selectedStaffKeys) }"
            )
              a-button(:disabled="!selectedStaffKeys.length") 移除员工
                template(#icon)
                  //- UserAddOutlined
            a-button(type="primary", ghost, @click="() => { handleAddStaff() }" :disabled="!selectedRole.id") 添加员工
              template(#icon)
                UserAddOutlined

      a-spin(:spinning="status.staffListLoading")
        .table-wrapper
          a-table(:columns="tableColumnConfig", :dataSource="staffList" :row-selection="roleTableRowSelect" :pagination="pagination" @change="handleStaffListPageChange")
            template(#bodyCell="{ column, record }")
              //- 头像部分
              template(v-if="column.dataIndex === 'name'")
                .staff-summary
                  .avatar
                    a-avatar(:src="record.formalPhoto") {{ record.realName[0] }}
                  .staff-info
                    .staff-name 
                      span {{ record.realName }}
                    .staff-role 
                      //- span {{ record.roles.map((item: any) => item.roleName).join(', ') }}

              template(v-if="column.dataIndex === 'roles'") 
                span(v-if="record.roles.length > 0") {{ record.roles }}
                span(v-else) 无角色

              template(v-if="column.key == 'type'")
                a-tag(v-if="record.isAdministrator" color="#FF9111") 管理员
                a-tag(v-if="record.employeeTypeStr == '兼职'" color="#2db7f5") {{ record.employeeTypeStr }}
                a-tag(v-if="record.status==0" color="#2665FC") 已离职


              template(v-if="column.dataIndex === 'gender'") 
                span(v-if="record.gender === 1") 男
                span(v-else-if="record.gender === 2") 女
                span(v-else-if="record.gender === 3") 其他
                span(v-else) 未知

              template(v-if="column.key === 'operation'")
                a-space(:size="12")
                  a-popconfirm(
                    title="确定要移除该员工么?"
                    ok-text="移除"
                    cancel-text="取消"
                    @confirm="()=>{ removeUser(selectedRole.id, [record.id]) }"
                  )
                    a() 移除

  a-drawer(
    v-model:open="drawer.show",
    placement="right",
    :title="drawer.title",
    :destroyOnClose="true",
    :width="480"
    :bodyStyle="{padding: 0}"
  )
    component(
      :is="drawer.component",
      v-if="drawer.component",
      :props="drawer.props",
      @close="() => { drawer.show = false }"
      @update:success="() => { drawer.show = false; getRolesList() }"
    )

  staff-selector(v-model:visible="status.showStaffSelector" multi @select="onStaffSelect")

</template>

<script lang="ts" setup>
import { reactive, ref, shallowReactive } from 'vue'
import { UserOutlined, UserAddOutlined, SearchOutlined, MoreOutlined } from '@ant-design/icons-vue'

import addRoleComponent from '@/components/app/role-edit.vue'
import staffSelector from '@/components/app/staff-selector.vue'

import { getCompanyRoles, getCompanyUserByRoleId, deleteCompanyRole, addUserToCompanyRole, removeUserFromCompanyRole } from '@/api/system/roles'
import { message } from 'ant-design-vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'

const router = useRouter()

const pagination = reactive({
  current: 0,
  pageSize: 20,
  total: 0,
})

const selectedStaffKeys = ref<any[]>([])
const roleTableRowSelect = {
  selectedRowKeys: selectedStaffKeys,
  onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
    selectedStaffKeys.value = selectedRowKeys
  }
}

const selectedRole = ref({} as any)
async function removeUser(roleId: number, userIds: number[]) {
  status.staffListLoading = true
  try {
    const res = await removeUserFromCompanyRole(roleId, userIds)
    getStaff(roleId)
    message.success('员工移除成功！')
  } catch (err: any) {
    message.success(err.message)
  }
  status.staffListLoading = false
}

const drawer = shallowReactive({
  title: '',
  show: false,
  component: null as any,
  props: {} as any
})

const status = reactive({
  rolesListLoading: false,
  staffListLoading: false,
  showStaffSelector: false,
})

// 在不需要响应式的情况下，可以直接返回固定的值
const tableColumnConfig = [
  { title: '员工', key: 'name', dataIndex: 'name' },
  { title: '类型', key: 'type' },
  { title: '性别', key: 'gender', dataIndex: 'gender' },

  // { title: '部门', key: 'department', dataIndex: 'deptId' },
  // { title: '职位', key: 'position', dataIndex: 'positionTitle' },
  { title: '操作', key: 'operation' },
]

const roleList = ref([] as any[])
const staffList = ref([] as any[])

async function getRolesList() {
  status.rolesListLoading = true
  try {
    const res = await getCompanyRoles()
    roleList.value = res.data
  } catch (err) {
    message.error('角色信息加载失败，请重试')
    console.log(err)
  }
  status.rolesListLoading = false
}

function handleAddRole() {
  drawer.title = '修改角色/权限'
  drawer.show = true
  drawer.component = addRoleComponent
  drawer.props = {}
}

async function getStaff(roleId: number) {
  status.staffListLoading = true
  try {
    const res = await getCompanyUserByRoleId(roleId, { current: pagination.current, size: pagination.pageSize })

    staffList.value = res.data.users.map((item: any, index: number) => {
      return { ...item, key: item.id }
    })

    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.staffListLoading = false
}

function handleSelectRole(role: any) {
  selectedRole.value = role
  selectedStaffKeys.value = []
  pagination.current = 1
  // selectedStaffRows.value = []
  getStaff(role.id)
}

function handleAddStaff() {
  status.showStaffSelector = true
}

async function deleteRole(role: any) {
  try {
    const res = await deleteCompanyRole(role.id)
    message.success(`角色[${role.roleCnName}]已删除。`)
    getRolesList()
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleStaffListPageChange(page: any) {
  pagination.current = page.current
  getStaff(selectedRole.value.id)
}

async function onStaffSelect(staffList: any[]) {
  const roleId = selectedRole.value.id
  const userIds = staffList.map(item => item.id)
  try {
    const res = await addUserToCompanyRole(roleId, userIds)
    getStaff(roleId)
    status.showStaffSelector = false
    message.success('添加员工成功')
  } catch (err: any) {
    message.error(err.message)
  }
}

getRolesList()

onBeforeRouteLeave(() => {
  drawer.show = false
  status.showStaffSelector = false
})
</script>

<style lang="scss" scoped>
.staff-list-page {
  border-radius: 6px;
  overflow: hidden;
}

.staff-summary {
  display: flex;
  align-items: center;

  .avatar {
    margin-right: 8px;
  }

  .gender {
    margin-right: 8px;
  }
}

.staff-item {
  &__name {
    padding: 0 12px;
    display: inline;
  }

  &__tag {
    margin-left: 8px;
  }
}

.table-wrapper {
  min-height: 36px;
}

.role-list-page-body {
  display: flex;
  background-color: #fff;
  border-radius: 8px;

  .role-list {
    padding: 16px;
    width: 320px;
    border-right: 1px solid #e8e8e8;

    .role-item {
      display: flex;
      line-height: 16px;
      cursor: pointer;
      border-radius: 2px;

      &:hover {
        background-color: RGBA(0, 0, 0, .05);
      }

      .role-item-name {
        flex: 1 1 auto;
        padding: 12px 16px;
      }

      .role-item-action {
        padding: 12px 16px;
        text-align: right;
        flex: 0 0 auto;
        font-weight: bold;
      }
    }

    .active {
      background-color: RGBA(255, 145, 17, .1);
      color: RGBA(255, 145, 17, 1);
      transition: all .2s;

      &:hover {
        background-color: RGBA(255, 145, 17, .2);
        transition: all .2s;
      }
    }

    .role-list-action {
      padding: 12px 0;
    }
  }

  .staff-list {
    flex: 1 1 auto;
    padding: 0 16px;

    .staff-list-actions {
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>