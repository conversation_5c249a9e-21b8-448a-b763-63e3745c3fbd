<template lang="pug">

.permission-page()
  a-page-header(
    :title="pageHeadTitle",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="padding: 0 0 8px;")

  a-spin(:spinning="pageLoading")
    a-row(:gutter="24")
      a-col(:span="24" v-for="(item, index) in permissionGroups")
        .permission-section()
          .section-title
            a-checkbox(
              v-model:checked="item.checked"
              :indeterminate="item.indeterminate"
              @change="(val) => {handleCheckChange(val, index)}"
            ) {{ item.label }}
          .section-list
            a-checkbox-group(
              v-model:value="item.childrenChecked"
              :options="item.children"
              @change="(val) => {handleCheckBoxChange(val, index)}"
            )
    .permission-submit
      a-button(type="primary" @click="updateRolePermissions") 确定
</template>

<script lang="ts" setup>
import { getCompanyPermissions, getCompanyRolePermissions, updateRolePermission } from '@/api/permission'
import { getCompanyRule } from '@/api/system/roles'
import { useRoute, useRouter } from 'vue-router'
import { onMounted, ref, toRef } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { onActivated } from 'vue'
import { computed } from 'vue'

const router = useRouter()
const props = defineProps<{ id: number }>()
const roleId = toRef(props, 'id')

// const roleId = Number(route.params.roleId)
const roleDetail = ref<any>()
const permissionGroups = ref([] as any[])

const companyPermissionList = ref<any>()
const permissions = ref([] as number[])

const pageLoading = ref(true)

async function getRoleDetail(roleId: number) {
  try {
    const res = await getCompanyRule(roleId)
    roleDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function getRolePermissions(roleId: number) {
  pageLoading.value = true

  try {
    permissions.value = []
    const companyPermissions = await getCompanyPermissions()
    const rolePermissions = await getCompanyRolePermissions(roleId)
    companyPermissionList.value = companyPermissions.data

    permissions.value = rolePermissions.data.map((item:any) => item.id)

    updateCheckState()
  } catch (err: any) {
    message.error(err.message)
  }
  pageLoading.value = false
}

function updateCheckState() {
  permissionGroups.value = []
  companyPermissionList.value.forEach((item: any) => {
    let indeterminate = false
    let checked = false

    let checkedNumber = 0
    let children: any[] = []
    let childrenChecked: number[] = []
    item.children.forEach((child: any) => {
      const childPermission = {
        value: child.id,
        label: child.title,
        checked: false
      }
      if (permissions.value.indexOf(child.id) !== -1) {
        childPermission.checked = true
        childrenChecked.push(child.id)
        checkedNumber++
      }
      children.push(childPermission)
    })

    if (checkedNumber > 0 && checkedNumber < item.children.length) {
      indeterminate = true
    }
    if (checkedNumber > 0 && checkedNumber === item.children.length) {
      checked = true
    }

    if (permissions.value.indexOf(item.id) === -1 && (indeterminate || checked)) {
      permissions.value.push(item.id)
    }
    if (permissions.value.indexOf(item.id) !== -1 && !indeterminate && !checked) {
      permissions.value.splice(permissions.value.indexOf(item.id), 1)
    }

    const permission = {
      value: item.id,
      label: item.title,
      indeterminate: indeterminate,
      checked: checked,
      children: children,
      childrenChecked: childrenChecked
    }
    permissionGroups.value.push(permission)
  })
}

async function updateRolePermissions() {
  Modal.confirm({
    title: '确认要修改' + roleDetail.value.roleCnName + "的权限吗？",
    content: '权限修改后，要使新的权限生效，需要用户重新登录 ITP',
    async onOk() {
      const res = await updateRolePermission(roleId.value, permissions.value)
      message.info("权限修改成功")
      getRolePermissions(roleId.value)

    },
    onCancel() { }
  })
}

const pageHeadTitle = computed(() => {
  return '权限管理 - ' + roleDetail.value?.roleCnName
})

function handleCheckChange(e: any, index: number) {
  permissionGroups.value[index].children.forEach((item: any) => {
    if (e.target.checked) {
      if (permissions.value.indexOf(item.value) === -1) {
        permissions.value.push(item.value)
      }
    } else {
      let permissionIndex = permissions.value.indexOf(item.value)
      if (permissionIndex !== -1) {
        permissions.value.splice(permissionIndex, 1)
      }
    }
  })
  updateCheckState()
}

function handleCheckBoxChange(value: any, index: number) {
  permissionGroups.value[index].children.forEach((item: any, index: number) => {
    if (value.indexOf(item.value) === -1) {
      // 要删除的
      let permissionIndex = permissions.value.indexOf(item.value)
      if (permissionIndex !== -1) {
        permissions.value.splice(permissionIndex, 1)
      }
    } else {
      // 要保留、增加的
      let permissionIndex = permissions.value.indexOf(item.value)
      if (permissionIndex === -1) {
        permissions.value.push(item.value)
      }
    }
  })
  updateCheckState()
}

onActivated(async () => {
  permissionGroups.value = []
  await getRoleDetail(roleId.value)
  await getRolePermissions(roleId.value)
})

</script>

<style lang="scss" scoped>
.permission-page {}

.permission-filter {
  // background: #fff;
  margin-bottom: 20px;
  border-radius: 8px;
  // border-top: 1px solid #f0f0f0;
}

.permission-section {
  margin: 12px 0;
  border-radius: 8px;
  background-color: #fff;

  .section-title {
    background-color: #f9f9f9;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.section-title {
  font-weight: bold;

  span {
    font-size: 16px;
  }
}

.section-list {
  padding: 12px 20px;
}

.permission-submit {
  margin: 20px 0;
  text-align: right;
}
</style>