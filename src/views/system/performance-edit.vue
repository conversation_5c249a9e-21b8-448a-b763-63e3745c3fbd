<template lang="pug">
mixin page-header
  a-page-header(
    title="业绩分配配置",
    sub-title="",
    @back="() => {$router.go(-1)}",
    style="padding: 0 0 16px"
  )

mixin config-table
  a-spin(:spinning="status.loading")
    table
      thead
        tr
          th 角色
          th 主要工作说明
          th 分配比例
          th 说明
          th 过程
      tbody
        template(v-for="item in performanceList" :key="item.key")
          template(v-for="(point, index) in item.list")
            template(v-if="index == 0")
              tr
                td {{ point.performancePoint.roleName}}
                td 
                  .action {{ point.performancePoint.actionName}}
                  .remark(v-if="point.performancePoint.duties")
                    InfoCircleOutlined
                    span.text {{ point.performancePoint.duties }}
                td
                  a-input-number(v-model:value="point.pointRate" :formatter="value => `${value}%`" :min="0" :max="10")
                td {{ point.performancePoint.remark }}
                td(:rowspan="item.list.length") {{ item.key }}

            template(v-else)
              tr
                td {{ point.performancePoint.roleName}}
                td 
                  .action {{ point.performancePoint.actionName}}
                  .remark(v-if="point.performancePoint.duties")
                    InfoCircleOutlined
                    span.text {{ point.performancePoint.duties }}
                td
                  a-input-number(v-model:value="point.pointRate" :formatter="value => `${value}%`" :min="0" :max="10")
                td {{ point.performancePoint.remark }}

    .config-action
      a-button(type="primary" @click="handleSubmit" :loading="status.saving") 保存 

.performance-edit
  +page-header
  +config-table

</template>

<script lang="ts" setup>
import { onActivated, reactive, ref } from 'vue'
import { getCompanyPerformanceConfig, updateCompanyPerformanceConfig } from '@/api/performance'
import { message } from 'ant-design-vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

const performanceList = ref<any[]>()
const status = reactive({
  loading: false,
  saving: false
})

async function getPerformanceConfig() {
  status.loading = true
  try {
    const res = await getCompanyPerformanceConfig()
    const processList = new Map<string, { key: string, list: any[] }>()

    res.data.forEach((point: any, index: number) => {
      const item = processList.get(point.performancePoint.progressName)
      if (item) {
        item.list.push(point)
      }
      else processList.set(point.performancePoint.progressName, { key: point.performancePoint.progressName, list: [point] })
    })
    performanceList.value = Array.from(processList.values())

  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleSubmit() {
  const form: any[] = []
  performanceList.value?.forEach((item: any) => {
    item.list.forEach((point: any) => {
      form.push({
        pointId: point.pointId,
        pointRate: point.pointRate,
        id: point.id
      })
    })
  })

  status.saving = true
  try {
    const res = await updateCompanyPerformanceConfig(form)
    await getPerformanceConfig()
    message.success('绩效分配配置，保存成功！')
  } catch (err: any) {
    message.error(err.message)
  }
  status.saving = false
}

onActivated(() => {
  getPerformanceConfig()
})

</script>

<style lang="sass" scoped>
.config-action
  position: sticky
  padding: 12px
  background-color: #fff
  margin: 12px 0
  border: 1px solid #f0f0f0
  border-radius: 8px
  bottom: 0
  text-align: right
  width: 100%

table
  width: 100%
  border-collapse: collapse
  border: 1px solid #f0f0f0
  background: #fff
  border-radius: 8px
  overflow: hidden

  tr
    border-bottom: 1px solid #f0f0f0
    &::hover
      background-color: #fafafa

  th
    background-color: #fafafa
    text-align: left

  th, td
    border: 1px solid #f0f0f0
    padding: 8px 12px

    .remark
      color: RGBA(0,0,0,.4)
      font-size: 13px
      .text
        margin-left: 4px

</style>