<template lang="pug">
.department-list-page
  a-page-header(
    title="部门管理",
    sub-title="",
    @back="() => {$router.go(-1)}",
    style="padding: 0 0 8px"
  )

  a-spin(:spinning="status.loading")
    .department-list-page-body
      .department-list
        .department-list-container(v-if="departmentTreeData.length > 0")
          .root-department(@click="() => { handleSelectDepartment(0) }")
            .root-department__title(:class="{ active: selectedDepartmentIds.includes(0) }")
              HomeOutlined.root-department__icon
              span.root-department__title-text {{ userStore.companyName }}
          .children-department
            itp-department(:data="departmentTreeData" @select="handleSelectDepartment" @remove="handleRemoveDeparment" :selected="selectedDepartmentIds")

        .department-action-container
          a-button(@click="handleAddDepartment", type="primary", ghost, block) 添加部门

      .staff-list
        .staff-list-actions
          .staff-list-actions__left
            span.department-name {{ selectedDepartmentName  }}

          .staff-list-actions__right
            a-space
              a-button(@click="handleSetLeaderClick" :disabled="!selectedDepartmentId") 设置部门主管
                template(#icon)
                  UserAddOutlined
              a-button(@click="() => handleClickShowDetpSelector(null)", :disabled="!selectedStaffRows.length") 变更部门
                template(#icon)
                  RetweetOutlined
              a-button(type="primary", ghost, @click="handleAddStaffClick") 加入员工
                template(#icon)
                  UserAddOutlined


        a-table(:columns="tableColumnConfig", :dataSource="staffList" :row-selection="roleTableRowSelect" :pagination="pagination" @change="handlePageChange")
          template(#bodyCell="{ column, record }")

            template(v-if="column.dataIndex === 'department'")
              span {{ record.departments.map((item: any) => item.name).join(', ') }}

            template(v-if="column.dataIndex === 'name'")
              .staff-summary
                .avatar
                  a-avatar(:src="record.formalPhoto") {{ record.realName[0] }}
                .staff-info
                  .staff-name
                    a-space(:size="8")
                      span.name {{ record.realName }}
                      a-tag(v-if="record.isLeader" color="orange" :bordered="false" ) LEADER
                  .staff-role 
                    span {{ record.roles.map((item: any) => item.roleName).join(', ') }}


            template(v-if="column.dataIndex === 'roles'") 
              span(v-if="record.roles.length > 0") {{ record.roles.map((item: any) => item.roleName).join(', ') }}
              span(v-else) 无角色


            template(v-if="column.key == 'type'")
              a-tag(v-if="record.isAdministrator" color="#FF9111") 管理员
              a-tag(v-if="record.employeeTypeStr == '兼职'" color="#2db7f5") {{ record.employeeTypeStr }}
              a-tag(v-if="record.status==0" color="#2665FC") 已离职

            template(v-if="column.dataIndex === 'gender'") 
              span {{ getGender(record.gender) }}

            template(v-if="column.dataIndex === 'operation'") 
              a.pointer(@click="() => handleClickShowDetpSelector(record)") 变更部门

  a-drawer(
    v-model:open="drawer.show",
    placement="right",
    :title="drawer.title",
    :destroyOnClose="true",
    :width="480"
    :bodyStyle="{padding: 0}"
  )
    component(
      :is="drawer.component",
      v-if="drawer.component",
      :props="drawer.props",
      @close="drawer.show = false"
      @refresh="fetchCompanyDepartment"
    )

  StaffSelector(
    v-model:visible="status.showStaffSelector" 
    :multi="true"
    @select="handleStaffSelect"
    filter=""
  )

  StaffSelector(
    v-model:visible="status.showLeaderSelector" 
    :multi="staffSelectorParams.multi"
    @select="handleLeaderSelect"
    :filter="staffSelectorParams.filter"
  )

  DepartmentSelector(
    v-model:visible="status.showDetpSelector"
    :list="staffDepartmentIds"
    @select="handleDeptSelect"
  )
  
</template>

<script lang="ts" setup>
import { reactive, ref, shallowReactive } from 'vue'
import { UserAddOutlined, SearchOutlined, HomeOutlined, RetweetOutlined } from '@ant-design/icons-vue'
import StaffSelector from '@/components/app/staff-selector.vue'
import DepartmentSelector from '@/components/app/department-selector.vue'
import addDepartmentComponent from '@/components/app/department-edit.vue'
import { message } from 'ant-design-vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'

import { getCompanyDepartment, addCompanyDepartmentUsers, moveUserToDepartment, deleteCompanyDepartment, updateDepartmentLeader } from '@/api/system/department'
import { getCompanyUserList, getDepartmentUserList } from '@/api/system/users'
import { useUserStore } from '@/store/user.store'
import { onActivated } from 'vue'
import { onMounted } from 'vue'

interface Department {
  id: number,
  companyId: number,
  parentId: number,
  deptName: string,
  userId: number,
  children?: Department[],
  title?: string,
  key: number,
}

const selectedDepartmentId = ref<number | null>(null)
const selectedDepartmentIds = ref<number[]>([])
const selectedDepartmentName = ref('')

const staffId = ref<number | null>(null)
const staffDepartmentIds = ref<number[]>([])

const staffSelectorParams = reactive({
  multi: true,
  filter: ''
})

const router = useRouter()
const userStore = useUserStore()
const staffList = ref([] as any[])
const departmentTreeData = ref([] as any[])
let departmentMap = new Map<number, Department>()
const selectedStaffRows = ref<any[]>([])
const pagination = reactive({
  current: 0,
  pageSize: 20,
  total: 0,
})

const drawer = shallowReactive({
  title: '',
  show: false,
  component: null as null | any,
  props: {} as any
})

const status = reactive({
  loading: false,
  showStaffSelector: false,
  showLeaderSelector: false,
  showDetpSelector: false,
})

// const genderMap = ['男', '女', '其他']

const genderMap = reactive<any>({
  '1': '男',
  '2': '女',
  '3': '其他'
})
function getGender(gender: number) {
  if (!gender) return '未知'
  else return genderMap[gender]
}

const tableColumnConfig = [
  { title: '员工', key: 'name', dataIndex: 'name' },
  { title: '性别', key: 'gender', dataIndex: 'gender' },
  { title: '类型', key: 'type' },
  { title: '部门', key: 'department', dataIndex: 'department' },
  // { title: '职位', key: 'position', dataIndex: 'positionTitle' },
  // { title: '角色', key: 'roles', dataIndex: 'roles' },
  { title: '操作', key: 'operation', dataIndex: 'operation' },
]

function handleAddDepartment() {
  drawer.title = '新增部门'
  drawer.show = true
  drawer.component = addDepartmentComponent
  drawer.props = {}
}

function goBack() {
  router.go(-1)
}

function handleAddStaffClick() {
  staffSelectorParams.multi = true
  staffSelectorParams.filter = ''
  status.showStaffSelector = true
}

function handleSetLeaderClick() {
  staffSelectorParams.multi = false
  staffSelectorParams.filter = `department:${JSON.stringify([selectedDepartmentId.value])}`
  status.showLeaderSelector = true
}

async function handleSelectDepartment(departmentId: number | null) {
  if (departmentId === null) return

  pagination.current = 1
  pagination.pageSize = 20

  if (departmentId === 0) {
    selectedDepartmentName.value = userStore.companyName
    fetchCompanyUserList()
  } else {
    selectedDepartmentName.value = departmentMap.get(departmentId)?.deptName || ''
    fetchDeparmentUserList(departmentId, { current: 1, size: pagination.pageSize })
  }
  selectedDepartmentId.value = departmentId
  selectedDepartmentIds.value = [departmentId]
}

async function fetchCompanyUserList() {
  status.loading = true
  try {
    const res = await getCompanyUserList()
    staffList.value = res.data.map((item: any, index: number) => { return { ...item, key: item.id } })
    pagination.pageSize = staffList.value.length
    pagination.total = staffList.value.length
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function fetchDeparmentUserList(departmentId: number, params: { current: number, size: number }) {
  status.loading = true
  try {
    const res = await getDepartmentUserList(departmentId, { current: pagination.current, size: pagination.pageSize })
    staffList.value = res.data.users.map((item: any, index: number) => { return { ...item, key: item.id } })
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handlePageChange(page: any) {
  pagination.current = page.current
  fetchDeparmentUserList(selectedDepartmentId.value!, { current: pagination.current, size: pagination.pageSize })
}

async function fetchCompanyDepartment() {
  status.loading = true
  try {
    const res = await getCompanyDepartment()
    const { treeData, mapData } = _processDepartmentData(res.data)
    departmentTreeData.value = treeData
    departmentMap = mapData

  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

function _processDepartmentData(departments: Department[]) {
  const deptMap = new Map<number, Department>()
  const deptTree = new Array<Department>()

  // 初始化数据结构，并更具id创建索引
  departments.forEach((dept) => {
    dept.children = []
    dept.title = dept.deptName
    dept.key = dept.id
    deptMap.set(dept.id, dept)

    // 如果父级部门ID是0， 则表示上级无企业
    if (dept.parentId === 0) deptTree.push(dept)
  })

  // 组装父子关系
  deptMap.forEach((dept, key) => {
    if (dept.parentId) {
      const parent = deptMap.get(dept.parentId)
      parent?.children?.push(dept)
    }
  })

  return { treeData: deptTree, mapData: deptMap }
}

async function handleStaffSelect(staffList: any[]) {
  const users = staffList.map(item => item.id)
  const departmentId = selectedDepartmentId.value
  try {
    const res = await addCompanyDepartmentUsers({
      departmentId,
      users
    })
    message.success('添加员工成功。')
    handleSelectDepartment(selectedDepartmentId.value)
    status.showStaffSelector = false
  } catch (err: any) {
    message.error(`添加用户失败！[${err.message}]`)
  }
}

async function handleLeaderSelect(leaderList: any[]) {
  status.showLeaderSelector = false
  status.loading = true
  try {
    const res = await updateDepartmentLeader(selectedDepartmentId.value!, leaderList[0].id)
    fetchDeparmentUserList(
      selectedDepartmentId.value!,
      { current: pagination.current, size: pagination.pageSize }
    )
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleDeptSelect(departmentIds: number[]) {
  status.loading = true

  let staffIds: any[] = []
  if (staffId.value) {
    staffIds = [staffId.value]
  } else {
    staffIds = selectedStaffRows.value.map(item => item.id)
  }

  try {
    const res = await moveUserToDepartment(staffIds, departmentIds)
    handleSelectDepartment(selectedDepartmentId.value)
    status.showDetpSelector = false
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleRemoveDeparment(deptId: number) {
  status.loading = true
  try {
    const res = await deleteCompanyDepartment(deptId)
    await fetchCompanyDepartment()
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const roleTableRowSelect = {
  onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
    selectedStaffRows.value = selectedRows
  }
}

const handleClickShowDetpSelector = (record: any) => {
  if (!record) {
    staffId.value = null
    staffDepartmentIds.value = []
    status.showDetpSelector = true
    return
  }
  staffId.value = record.id
  staffDepartmentIds.value = record.departments.map((item: any) => item.id)
  status.showDetpSelector = true
}

onMounted(() => {
  fetchCompanyDepartment()
})

onActivated(() => {
  if (selectedDepartmentId.value) {
    fetchDeparmentUserList(
      selectedDepartmentId.value!,
      { current: pagination.current, size: pagination.pageSize }
    )
  }
})

onBeforeRouteLeave(() => {
  drawer.show = false
  status.showStaffSelector = false
  status.showDetpSelector = false
})

</script>

<style lang="scss" scoped>
.department-list-page {
  border-radius: 6px;
  overflow: hidden;
}

.staff-item {
  &__name {
    padding: 0 12px;
    display: inline;
  }

  &__tag {
    margin-left: 8px;
  }
}

.department-list-page-body {
  display: flex;
  background-color: #fff;
  border-radius: 8px;

  .staff-list {
    flex: 1 1 auto;
    padding: 0 16px;


    .staff-summary {
      display: flex;
      align-items: center;

      .name {
        font-weight: bold;
      }

      .avatar {
        margin-right: 8px;
      }

      .gender {
        margin-right: 8px;
      }
    }

    .staff-list-actions {
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .department-name {
        font-size: 16px;
      }
    }
  }

  .department-list {
    padding: 16px;
    width: 320px;
    border-right: 1px solid #e8e8e8;

    .department-list-container {
      border-radius: 8px;
      margin-bottom: 12px;

      .root-department {
        &__title {
          padding: 8px 12px;
          border-radius: 4px;

          &:hover {
            background-color: #f9f9f9;
            cursor: pointer;
          }
        }

        &__icon {
          margin-right: 8px;
        }
      }

      .active {
        background-color: RGBA(255, 145, 17, .1);
        color: RGBA(255, 145, 17, 1);
        transition: all .2s;

        &:hover {
          background-color: RGBA(255, 145, 17, .2);
          transition: all .2s;
        }
      }
    }
  }
}

.pointer {
  cursor: pointer;
  // white-space: ;
  white-space: nowrap;
}

.children-department {
  padding-left: 24px;
}
</style>