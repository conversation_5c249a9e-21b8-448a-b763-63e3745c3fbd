<script setup lang="ts">
import { getAreaList, getJobStatus, dictionary, getAllFunctionList } from "@/api/dictionary"
// import { reactive } from "@vue/reactivity"
import { ref, onMounted, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from "vue-router"
import { getJobRequireHeaderIndex, getJobRequireSearch, getMyJobRequireSearch, POSITION_STATUS, QUERY_ACTION } from '@/api/job'
import { getCompanyRoles, getCompanyOnboardUsers } from '@/api/system/roles'
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { getCustomerList } from "@/api/customer"
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import { getCustomerDetail } from '@/api/customer'
import { formatSalary } from '@/utils/salary-helper'

import FilterItem from '@/components/ui/filter-item.vue'
import JobPriority from '@/components/app/job-priority.vue'

interface OAny {
  [propName: string]: any;
}

const router = useRouter()

// 检索条件
const queryParams = reactive({
  customerId: '',
  name: '',
  status: POSITION_STATUS.ONGOING,
  locationIds: [] as number[],
  operators: [] as any[],
  priority: null,
  functionId: ''
})

// 用于渲染filter
const filter = reactive({
  customer: [] as any,
  areas: [] as any,
  areaMap: null as any,
  roles: [] as any[],
  jobStatus: [] as any,
  function: [] as any,
})

const status = reactive({
  initFilter: false,
  searchLoading: false,
  sharePopoverVisible: {} as Record<number, boolean> // 记录每个项目的分享弹窗状态
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const dataList = ref([]);

// 分享功能相关数据
const shareData = reactive({
  content: {} as Record<number, string>, // 存储每个项目的分享文案
  jobDetails: {} as Record<number, any>, // 缓存项目详情数据
  positionDetails: {} as Record<number, any>, // 缓存职位详情数据
  customerDetails: {} as Record<number, any>, // 缓存客户详情数据
})





// 生成RCN项目链接
const generateRcnLink = (jobId: number) => {
  return `https://service.smartdeerhr.com/n/icb/app/rcn/find-projects?jobRequirementId=${jobId}`
}

// 生成默认分享文案
const generateDefaultShareContent = (jobId: number) => {
  const jobDetail = shareData.jobDetails[jobId]
  const positionDetail = shareData.positionDetails[jobId]
  const customerDetail = shareData.customerDetails[jobId]

  if (!jobDetail || !positionDetail || !customerDetail) {
    return '正在加载项目信息...'
  }

  // 1. 新岗位+急招岗位
  const isNewProject = jobDetail.isNewProject === 0; // 假设 isNewProject 为 0 表示新项目
  const isUrgentProject = jobDetail.priority === 0; // P0算着急岗位
  let line1 = '';
  if (isNewProject && isUrgentProject) {
    line1 = '新岗位+急招岗位';
  } else if (isNewProject) {
    line1 = '新岗位';
  } else if (isUrgentProject) {
    line1 = '急招岗位';
  }

  // 2. 客户信息
  const isNewCustomer = customerDetail?.isNewCustomer; // 假设字段存在
  const hasPaymentHistory = customerDetail?.hasPaymentHistory; // 假设字段存在
  const isUrgentRequirement = jobDetail.priority === 0;

  let line2 = '';
  if (isNewCustomer) {
    line2 += '新客户';
  } else {
    line2 += '老客户';
  }
  if (hasPaymentHistory) {
    line2 += '有回款';
  }
  if (isUrgentRequirement) {
    line2 += '急需求';
  } else {
    line2 += '新需求';
  }
  if (isNewCustomer) {
    line2 += '流程快, HR配合';
  }

  const customerName = jobDetail?.customerName
  customerName ? line2 += `「${customerName}」` : '';

  // 3. base地
  const workLocation = positionDetail?.areaStr || '';
  const line3 = workLocation ? `base${workLocation.replace(/,/g, '、')}` : '';

  // 4. 薪资
  let line4 = '薪资Open, 优秀者薪资可谈';
  if (positionDetail?.salaryFrom && positionDetail?.salaryTo) {
    line4 = `薪资${formatSalary(positionDetail)}, 优秀者薪资可谈`;
  }

  // 5. 岗位名称
  const positionName = positionDetail?.positionTitle || '';
  const isManager = positionDetail?.isManager;
  let line5 = '岗位: ' + positionName;
  if (isManager) {
    line5 += '(管理岗)';
  }

  // 6. JD关键词
  const keywords = positionDetail?.tags || [];
  let line6 = '';
  if (keywords.length > 0) {
    line6 = '职位JD关键词: ' + keywords.slice(0, 5).map((k: any) => k.name || k).join(',');
  }

  // 7. RCN链接
  const rcnLink = generateRcnLink(jobId);

  // 8. PM信息
  const pmUsers = jobDetail.properties?.filter((p: any) => p.key?.toLowerCase() === 'pm').map((p: any) => p.valueName) || [];
  const pmNames = pmUsers.join('、');
  let line8 = '或交付群联系 PM 了解更多详情; ';
  if (pmNames) {
    line8 = `或交付群联系 PM${pmNames} 了解更多详情; `;
  }

  // 拼接所有行
  const lines = [line1, line2, line3, line4, line5, line6].filter(line => line);
  const numberedLines = lines.map((line, index) => `${index + 1}、${line}`);
  return `${numberedLines.join('\n')}\n有兴趣的查看链接: \n${rcnLink}\n${line8}`;
}

onMounted(() => {
  initFilter()
  initTableColumnConfig()
})

// 初始化Filter
const initFilter = async () => {
  status.initFilter = true
  try {
    // role selector init
    const roleList = ['BD', 'PM', 'CA']
    const roleListMap = new Map()
    const roleStaffRequestList: Promise<any>[] = []

    let index = 0
    let roles: any[] = []
    roleList.forEach((role: any) => {
      if (['bd', 'ca', 'pm'].includes(role.toLowerCase())) {
        const roleItem = Object.assign({}, { name: role }, { staffList: [] as any[], value: null as any | null })
        // 初始化form item
        queryParams.operators.push(roleItem)
        roleListMap.set(index++, roleItem)
        roles.push(roleItem)
        roleStaffRequestList.push(getCompanyOnboardUsers(role))
      }
    })
    filter.roles = roles

    const staffResponseList = await Promise.all(roleStaffRequestList)
    staffResponseList.forEach((staffRes, index) => {
      const role = roleListMap.get(index)
      role.staffList = staffRes.data
    })

    // area list init
    const areaDictRes = await dictionary.getAllAreaList()
    const [areaFormData, areaMap] = areaDictToTreeData(areaDictRes.data, true)
    filter.areas = areaFormData
    filter.areaMap = areaMap

    const dictJobStatus = await getJobStatus()
    filter.jobStatus = dictJobStatus.data

    fetchCustomerList()

  } catch (err) {
    message.error('初始化筛选器失败，请重试。')
  }
  status.initFilter = false
}

const _getAllFunctionList = async () => {
  const res = await getAllFunctionList()

  const list = res.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id
    }
  })
  
  filter.function = list
}
_getAllFunctionList()

const fetchCustomerList = async (name: string = '') => {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })

  filter.customer = list
}


const filterOption = (input: string, option: any) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}


const columnsList = ref([]);
const initTableColumnConfig = async () => {
  const { data } = await getJobRequireHeaderIndex()

  const nData = data.filter((item: any) => {
    return !['pm', 'bd', 'ca'].includes(item.key)
  })

  const list = nData.map((item: any, index: number) => {
    const key = item.key ? item.key.toLocaleLowerCase() : `key_${index}`
    const column = {
      ...item,
      title: item.name,
      key: key,
      dataIndex: key
    }
    if (item.key === 'jobTitle') return Object.assign({}, column, { fixed: 'left', width: 200 })
    if (item.key === 'hireCount') return Object.assign({}, column, { width: 90 })
    else return column
  })
  
  list.splice(2, 0, {title: '开始时间', key: 'key_startDate', dataIndex: 'positionStartDate', width: 120})
  list.push({
    title: '操作',
    key: 'key_action',
    dataIndex: 'key_action',
    fixed: 'right',
    width: 150
  })
  list.unshift({ title: '', key: 'key_priority', dataIndex: 'key_priority', fixed: 'left', width: 60 })
  columnsList.value = list
}

// 获取职位列表 
async function getMyJobList(queryParams: any) {
  status.searchLoading = true
  try {
    const { data } = await getMyJobRequireSearch(Object.assign({}, queryParams, { current: pagination.current, size: pagination.pageSize }))
    pagination.total = data.total
    const list = data.jobRequirements.map((item: any) => {
      let o: any = {}
      item.properties.forEach((row: any) => {
        const key = row.key.toLocaleLowerCase()
        if (o[key] === undefined) {
          o[key] = row.valueName
        } else {
          o[key] = `${o[key]},${row.valueName}`
        }
      })
      o['customerName'] = item.customerName
      o['key_action'] = '查看'
      o['id'] = item.id
      o['priority'] = item.priority
      o['positionStartDate'] = item.positionStartDate
      return o
    })

    dataList.value = list
  } catch (err: any) {
    message.error(`查询错误，请稍后重试 [${err.message}]`)
  }
  status.searchLoading = false
}

function assembleQueryActions() {
  const queryInfo = []
  if (queryParams.locationIds.length) {
    queryInfo.push({
      key: 'locationId',
      action: QUERY_ACTION.IN,
      valueType: 'list',
      value: queryParams.locationIds.join(',')
    })
  }

  queryParams.operators.forEach((item) => {
    if (['pm', 'bd', 'ca'].includes(item.name.toLowerCase())) {
      if (item.value) {
        queryInfo.push({
          valueType: 'long',
          key: item.name.toLowerCase(),
          action: QUERY_ACTION.EQ,
          value: item.value
        })
      }
    }
  })

  return {
    name: queryParams.name,
    status: queryParams.status,
    priority: queryParams.priority,
    queries: queryInfo,
    customerId: queryParams.customerId,
    functionId: queryParams.functionId
  }
}

// Filter 切换 
function handleFilterChange(type: any, value: any) {
  switch (type) {
    case 'areas':
      const areaItem = filter.areaMap.get(value)
      if (areaItem) queryParams.locationIds = [areaItem.id].concat(areaItem.children.map((item: any) => item.id))
      else queryParams.locationIds = []
      break
    case 'status':
      break
  }
  pagination.current = 1
  getMyJobList(assembleQueryActions())
}



function pageChange(pageInfo: any) {
  pagination.current = pageInfo.current
  getMyJobList(assembleQueryActions())
}



// 跳转到详情页
const handleClickToDetail = (job: any) => {
  router.push({
    path: `/job/${job.id}/detail`
  })
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      router.push({
        path: `/job/${record.id}/detail`
      })
    }
  }
}

// 获取项目详情数据（用于分享功能）
const fetchJobDetailsForShare = async (jobId: number) => {
  try {
    // 获取项目详情
    if (!shareData.jobDetails[jobId]) {
      const jobRes = await getJobRequirementDetail(jobId)
      shareData.jobDetails[jobId] = jobRes.data
    }

    const jobDetail = shareData.jobDetails[jobId]

    // 获取职位详情
    if (!shareData.positionDetails[jobId] && jobDetail.positionId) {
      const positionRes = await getPositionDetail(jobDetail.positionId)
      shareData.positionDetails[jobId] = positionRes.data
    }

    // 获取客户详情
    if (!shareData.customerDetails[jobId] && jobDetail.customerId) {
      const customerRes = await getCustomerDetail(jobDetail.customerId)
      shareData.customerDetails[jobId] = customerRes.data
    }

    // 初始化分享文案
    if (!shareData.content[jobId]) {
      shareData.content[jobId] = generateDefaultShareContent(jobId)
    }
  } catch (err: any) {
    console.error('获取项目详情失败:', err)
    message.error('获取项目详情失败')
  }
}

// 复制到剪贴板功能
const copyToClipboard = async (text: string) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    message.success('复制成功')
  } catch (err) {
    console.error('复制失败:', err)
    message.error('复制失败，请手动复制')
  }
}



// 处理RCN链接复制
const handleCopyRcnLink = (jobId: number) => {
  const content = shareData.content[jobId] || generateDefaultShareContent(jobId)
  const link = generateRcnLink(jobId)
  const fullContent = `${content}`
  copyToClipboard(fullContent)
}

// 处理分享弹窗显示
const handleSharePopoverOpen = async (jobId: number, open: boolean) => {
  status.sharePopoverVisible[jobId] = open
  if (open) {
    await fetchJobDetailsForShare(jobId)
  }
}


onMounted(() => {
  getMyJobList(assembleQueryActions())
})
</script>

<template lang="pug">
mixin page-header-section
  a-page-header(
    title="我相关的项目",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="padding: 0 0 8px;"
  )

mixin search-filter-section
  section.job-search-filter
    .search-input
      a-input-search(placeholder="职位名称搜索", enter-button, v-model:value="queryParams.name", @search="handleFilterChange('name')")
    
    
    a-row.search-filter(:gutter="[12, 12]")
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="客户")
          a-select(
            allow-clear
            show-search
            :filter-option="false"
            v-model:value="queryParams.customerId"
            :dropdownMatchSelectWidth="false"
            :options="filter.customer"
            @search="fetchCustomerList"
            @change="(value:any) => { handleFilterChange('customer', value) }"
          )

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="项目状态")

          a-select(
            allow-clear,
            v-model:value="queryParams.status",
            :dropdownMatchSelectWidth="false",
            :options="filter.jobStatus",
            @change="(value:any) => { handleFilterChange('status', value) }"
          )
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="优先级")
          a-select(
            @change="(value) => { handleFilterChange('priority', value)} ",
            allow-clear,
            :dropdownMatchSelectWidth="false",
            v-model:value="queryParams.priority",
          )
            a-select-option(:value="10") 高优先级(P0)
            a-select-option(:value="5") 常规(P1)
            a-select-option(:value="1") 低优先级(P2)

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="工作地")
          a-tree-select(
            @change="(value) => { handleFilterChange('areas', value)} ",
            :tree-data="filter.areas",
            :fieldNames="{ label: 'title', value: 'id' }",
            allow-clear,
            show-search,
            filter-option: "filterOption",
            :dropdownMatchSelectWidth="false"
          )
          
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="职能")
          a-select(
            allow-clear,
            v-model:value="queryParams.functionId",
            :dropdownMatchSelectWidth="false",
            :options="filter.function",
            @change="(value:any) => { handleFilterChange('function', value) }"
          )

      

mixin position-search-list
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        v-if="columnsList.length" 
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1200 }" 
        :pagination="pagination"
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
        rowKey="id"
      )
        template(#bodyCell="{ column, record }")
          template(v-if="column.dataIndex === 'key_action'")
            a-space(:size="8")
              a(@click.stop="handleClickToDetail(record)") 查看
              a-popover(
                :open="status.sharePopoverVisible[record.id] || false"
                title="分享链接"
                trigger="hover"
                placement="bottomRight"
                :overlayStyle="{ width: '400px' }"
                @openChange="(open) => handleSharePopoverOpen(record.id, open)"
              )
                template(#content)
                  .share-popover-content
                    .share-text-section
                      a-textarea(
                        v-model:value="shareData.content[record.id]"
                        :placeholder="generateDefaultShareContent(record.id)"
                        :rows="12"
                        style="margin-bottom: 16px;"
                      )
                    .share-buttons-section
                      a-space(:size="12")
                        
                        a-button(
                          type="primary"
                          @click="handleCopyRcnLink(record.id)"
                        ) RCN项目链接
                a(@click.stop) 分享

          template(v-if="column.dataIndex === 'jobtitle'")
            .position-title {{ record.jobtitle }}
            .customer-name {{ record.customerName }}

          template(v-if="column.dataIndex === 'key_priority'")
            JobPriority(:priority="record.priority")
          
        template(#expandedRowRender="{ record }")
          .expanded
            p 项目ID: {{ record.id }}
            p BD: {{ record.bd }}
            p PM: {{ record.pm }}
            p CA: {{ record.ca }}

.position-list-page
  +page-header-section
  +search-filter-section
  +position-search-list
</template>


<style lang="scss" scoped>
.position-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }

  :deep(.clickable) {
    cursor: pointer;
  }

  .expanded {
    padding-left: 49px;
  }
}

.position-filter {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;

  &-item {
    display: flex;
    margin-bottom: 10px;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      padding: 0px 0;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 10px;
      line-height: 32px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }

    .active {
      color: #FF9111;
    }
  }
}

.job-search-filter {
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;

  .search-input {
    margin-bottom: 12px;
  }

  .search-filter {
    // display: flex;
    // margin: 0 -8px;
    // justify-content: flex-start;

    // >* {
    //   padding: 0 8px;
    //   width: 20%;
    // }

    :deep(.ant-select-selector) {
      background-color: #f9f9f9;
      border-color: transparent;
    }

    :deep(.ant-select-selection-item) {
      color: #FF9111;
    }

    :deep(label) {
      background-color: #f9f9f9;
      padding-left: 8px;
    }
  }
}

.position-search {
  background-color: #fff;
  border-radius: 6px;


  .position-title {
    font-weight: bold;
  }

  .customer-name {
    color: #aaa;
    font-size: 12px;
  }

  &-head {
    height: 74px;
    // border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
  }
}

// 分享弹窗样式
.share-popover-content {
  .share-text-section {
    margin-bottom: 16px;

    .share-label {
      font-weight: 500;
      margin-bottom: 8px;
      color: #262626;
    }
  }

  .share-buttons-section {
    display: flex;
    justify-content: center;

    .ant-btn {
      min-width: 120px;
      height: 40px;
      border-radius: 8px;
      font-weight: 500;
    }
  }
}
</style>