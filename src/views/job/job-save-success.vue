<template lang="pug">
.job-save-success-page
  a-result(title="职位新增成功！" status="success")
    template(#extra)
      a-button(type="primary" @click="() => { goDetail(jobRequirementId) }") 查看职位详情
      a-button(type="primary" ghost @click="() => { goList() }") 职位列表
</template>

<script lang="ts" setup>
import { toRef } from 'vue';
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps<{id:number}>()
const jobRequirementId = toRef(props, 'id')

const router = useRouter()
function goDetail(jobRequirementId: number) {
  router.push(`/job/${jobRequirementId}/detail`)
}

function goList() {
  router.push(`/job/list`)
}

</script>

<style lang="scss" scoped>
.job-save-success-page {
  background-color: #fff;
  border-radius: 8px;
}
</style>