<template lang="pug">
mixin page-head
  .talent-page-header
    a-page-header(
      title="全部人才",
      sub-title="",
      @back="()=> {$router.go(-1)}",
      style="padding: 0 0 8px;"
    )
      template(#extra)
        a-button(type="primary" @click="() => {goto('/talent/create')}") 新增人才
          template(#icon)
            UserAddOutlined

mixin keyword-search-section
  .keyword-search
    a-input-search(
      v-model:value="queryParams.keyword",
      enter-button,
      @search="handleKeywordSearch",
      style="width: 400px"
    )

mixin filter-section
  .talent-filter
    a-spin(:spinning="status.initFilter")
      .talent-filter-item
        .talent-filter-item__title 学历
        .talent-filter-item__selector
          .talent-filter-item__option(
            :class="{ active: queryParams.degree === null }",
            @click="handleFilterChange('degree', null)"
          ) 不限
          .talent-filter-item__option(
            v-for="(item, index) in filter.degrees",
            @click="handleFilterChange('degree', index)",
            :key="index",
            :class="{ active: queryParams.degree === index }"
          ) {{ item }}

      .talent-filter-item
        .talent-filter-item__title 城市
        .talent-filter-item__selector
          .talent-filter-item__option(
            @click="handleFilterChange('areas', null)",
            :class="{ active: queryParams.areaCode === null }"
          ) 不限
          .talent-filter-item__option(
            v-for="(item, index) in filter.areas",
            @click="handleFilterChange('areas', item.id)",
            :key="index",
            :class="{ active: queryParams.areaCode === item.id }"
          ) {{ item.areaName }}

      .talent-filter-item
        .talent-filter-item__title 工作年限
        .talent-filter-item__selector
          .talent-filter-item__option(
            @click="handleFilterChange('workYears', null)",
            :class="{ active: queryParams.workYears === null }"
          ) 不限
          .talent-filter-item__option(
            v-for="(item, index) in filter.workYears",
            @click="handleFilterChange('workYears', index)",
            :key="index",
            :class="{ active: queryParams.workYears === index }"
          ) {{ item }}

      .talent-filter-item
        .talent-filter-item__title 性别偏好
        .talent-filter-item__selector
          .talent-filter-item__option(
            @click="handleFilterChange('gender', null)",
            :class="{ active: queryParams.gender === null }"
          ) 不限
          .talent-filter-item__option(
            v-for="(item, index) in filter.genders",
            @click="handleFilterChange('gender', index)",
            :key="index",
            :class="{ active: queryParams.gender === index }"
          ) {{ item }}
          //- .talent-filter-item__title(style="margin-left: 80px") 年龄范围
          //- .talent-filter-item__option
          //-   a-space(:size="8")
          //-     a-input-number(style="width: 60px" min="0" max="99" v-model:value="ageRange.from")
          //-     span -
          //-     a-input-number(style="width: 60px" :min="ageRange.from" max="99" v-model:value="ageRange.to")
          //-     a-button(type="primary" @click="handleFilterChange('ageRange')") 确定
          //-     a-button(v-if="ageRange.from || ageRange.to" @click="resetAgeRange") 重置

      .talent-filter-item
        .talent-filter-item__title 来源
        .talent-filter-item__selector
          .talent-filter-item__option(
            @click="handleFilterChange('sourceType', null)",
            :class="{ active: queryParams.sourceType === null }",
            :key="null"
          ) 不限
          .talent-filter-item__option(
            v-for="(item, index) in filter.sourceType",
            @click="handleFilterChange('sourceType', index)",
            :key="index",
            :class="{ active: queryParams.sourceType === index }"
          ) {{ item }}

          .talent-filter-item__title(style="margin-left: 80px") 来源渠道ID
          .talent-filter-item__option
            a-space(:size="8")
              a-input-number(width="60px" v-model:value="queryParams.sourceId")
              a-button(type="primary" @click="handleFilterChange('sourceId')") 确定
              a-button(@click="resetChannleId" v-if="queryParams.sourceId") 重置
      .talent-filter-item
        .talent-filter-item__title 姓名
        .talent-filter-item__selector
          .talent-filter-item__option
            a-space(:size="8")
              a-input(width="60px" v-model:value="queryParams.realName")
              a-button(type="primary" @click="handleFilterChange('realName')") 确定

mixin talent-search-items
  template(v-if="searchResult.length > 0")
    .talent-search-item(v-for="(item, index) in searchResult", :key="index")
      .talent-head-img
        a-avatar(:size="80", shape="square", :src="item.talentBase.photoUrl") {{ item.talentBase.realName }}
      .talent-info-name {{ item.talentBase.realName }}
      .talent-info
        .talent-info-wrapper(@click="() => { showTalentDetail(item.talentBase) }")
          .talent-person-info
            .talent-person-basic
              .talent-person-basic-info
              | {{ getAgeFromBirthday(item.talentBase.birthday) }} |
              | {{ replaceBlankString(item.talentBase.workYears, '-', '年') }} |
              | {{ replaceBlankString(item.talentBase.latestDegreeStr, '-') }} |
              | {{ replaceBlankString(item.talentBase.employeeStatusStr, '-') }}
              .talent-person-expect 求职期望：{{ item.talentDemand ? item.talentDemand.positionDemand : '未知' }}
            .talent-person-intro {{ item.talentBase.selfEvaluation }}

          .talent-experience-info
            .talent-career-info(
              v-if="item.talentExperiences && item.talentExperiences.length > 0"
            )
              .talent-career-info-item(
                v-for="index in (item.talentExperiences.length > 2 ? 2 : item.talentExperiences.length)"
              )
                .info-time {{ getShortDate(item.talentExperiences[index - 1].fromDate) }} - {{ getShortDate(item.talentExperiences[index - 1].toDate) }}
                .info-text {{ item.talentExperiences[index - 1].companyName }} - {{ item.talentExperiences[index - 1].position }}
            .talent-education-info(
              v-if="item.talentEducations && item.talentEducations.length > 0"
            )
              .talent-education-info-item(
                v-for="index in item.talentEducations.length > 2 ? 2 : item.talentEducations.length"
              )
                .info-time {{ getShortDate(item.talentEducations[index - 1].fromDate) }} - {{ getShortDate(item.talentEducations[index - 1].toDate) }}
                .info-text {{ item.talentEducations[index - 1].schoolName }} - {{ item.talentEducations[index - 1].major }}

  .talent-search-empty(v-else)
    a-empty

mixin talent-search-list
  a-table(
    :pagination="pagination" 
    :data-source="searchResult" 
    :loading="status.searchLoading" 
    :columns="columns"
    @change="pageChange"
  )
    template(#bodyCell="{ column, record }")
      template(v-if="column.key === 'talent'")
        TableTalentInfo(:talent="record" @click="() => showTalentDetail(record.talentBase.id)" )

      template(v-if="column.key === 'exp'")
        TableTalentExp(:talent="record")

.talent-list-page
  +page-head
  +keyword-search-section
  +filter-section
  +talent-search-list

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")

</template>

<script lang="ts" setup>
import { getCompanyTalentList } from "@/api/talent/talent"
import { getWorkYearList, getAreaList, getDegreeList } from "@/api/dictionary"
import { replaceBlankString, getAgeFromBirthday, getShortDate } from "@/utils/string"
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { UserAddOutlined } from '@ant-design/icons-vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import TableTalentInfo from "@/components/app/table-talent-info.vue"
import TableTalentExp from "@/components/app/table-talent-exp.vue"

const queryParams = reactive<any>({
  keyword: '',
  areaCode: null,
  degree: null,
  workYears: null,
  mobileNumber: '',
  sourceId: null,
  sourceType: null,
  gender: null
})

const filter = reactive({
  areas: [],
  workYears: [],
  degrees: [],
  sourceType: {
    0: 'ITP',
    1: '谷露',
    2: '灵鹿聘',
    3: '灵鹿推',
    22: 'RCN'
  },
  genders: { 1: '男', 2: '女' }
})

const columns = ref([
  { key: 'talent', title: '人才'},
  { key: 'exp', title: '履历'},
])

const status = reactive({
  initFilter: false,
  searchLoading: false,
  // showSmartDeerWorkModal: false,
  showTalentDetail: false,
})

const searchResult = ref([])

const router = useRouter()
function goto(path: string) {
  router.push(path)
}

const pagination = reactive({
  pageSize: 20,
  current: 0,
  total: 0
})

async function initFilter() {
  status.initFilter = true
  Promise.all([getWorkYearList(), getAreaList(), getDegreeList()]).then(([workYearList, areaList, degreeList]: any[]) => {
    filter.workYears = workYearList.data
    filter.areas = areaList.data
    filter.degrees = degreeList.data
  }).catch(err => {
    console.log(err)
  }).finally(() => {
    status.initFilter = false
  })
}

async function getTalentSearchList() {
  status.searchLoading = true
  try {
    const talentList = await getCompanyTalentList(queryParams, pagination)
    const listData = talentList.data
    searchResult.value = listData.records
    pagination.current = listData.current
    pagination.total = listData.total
  } catch (err) {
    message.error('搜索人才失败，请重试。')
    console.log(err)
  }
  status.searchLoading = false
}

const talentId = ref<number>()
function showTalentDetail(tid: number) {
  talentId.value = tid
  status.showTalentDetail = true
}


const ageRange = reactive({
  from: 0 as number | null,
  to: 0 as number | null,
})

function handleFilterChange(type: string, value?: any) {
  switch (type) {
    case 'areas':
      queryParams.areaCode = value
      break
    case 'workYears':
      queryParams.workYears = value
      break
    case 'degree':
      queryParams.degree = value
      break
    case 'gender':
      queryParams.gender = value
      break
    case 'sourceType':
      queryParams.sourceType = value
      queryParams.sourceId = null
      break
    case 'sourceId':
      queryParams.sourceType = null
  }
  pagination.current = 1

  getTalentSearchList()
}

function pageChange(value: any) {
  pagination.current = value.current
  pagination.pageSize = value.pageChange
  getTalentSearchList()
}

const selectedTalent = ref({} as any)

function handleKeywordSearch(value: string) {
  queryParams.keyword = value
  queryParams.mobileNumber = ''
  getTalentSearchList()
}

function resetChannleId() {
  queryParams.sourceType = null
  queryParams.sourceId = null
  getTalentSearchList()
}

initFilter()
getTalentSearchList()

onMounted(() => {
  getTalentSearchList()
})


</script>

<style lang="scss" scoped>
.talent-page-header {
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  overflow: hidden;
}

.keyword-search {
  margin-bottom: 8px;
}

.talent-filter {
  background: #fff;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;

  &-item {
    display: flex;
    margin: 12px 0;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .active {
      color: #ff9111;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 5px;
      cursor: pointer;
      transition: all 0.2s;
      line-height: 32px;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }
  }
}

// .talent-search {
//   background-color: #fff;
//   border-radius: 8px;

//   h3 {
//     font-size: 14px;
//     margin: 0;
//   }

//   &-head {
//     height: 70px;
//     border-bottom: 1px solid #f0f0f0;
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding: 24px;
//   }

//   &-item {
//     padding: 24px;
//     border-bottom: 1px solid #f0f0f0;
//   }

//   &-empty {
//     padding: 40px;
//   }

//   &-pagination {
//     padding: 24px;
//     display: flex;
//     justify-content: flex-end;
//   }
// }

// .talent-search-item {
//   padding: 24px;
//   padding-left: 124px;
//   position: relative;
//   display: flex;
//   flex-direction: column;
//   cursor: pointer;
//   transition: all 0.2s;

//   &:hover {
//     background-color: #fafafa;
//     transition: all 0.2s;
//   }

//   .talent-head-img {
//     position: absolute;
//     left: 24px;
//     top: 24px;
//   }

//   .talent-info-name {
//     font-size: 16px;
//     font-weight: bold;
//   }

//   .talent-info {
//     display: flex;

//     .talent-info-wrapper {
//       flex: 1 1 auto;
//       display: flex;

//       .talent-person-info {
//         flex: 0 0 auto;
//         width: 50%;
//         padding-right: 2%;
//         line-height: 24px;

//         .talent-person-basic {
//           margin-bottom: 12px;
//           height: 48px;

//           .talent-person-basic-info {
//             font-size: 14px;
//           }

//           .talent-person-expect {
//             white-space: nowrap;
//             overflow: hidden;
//             text-overflow: ellipsis;
//           }
//         }

//         .talent-person-intro {
//           display: -webkit-box;
//           -webkit-line-clamp: 2;
//           overflow: hidden;
//           text-overflow: ellipsis;
//           height: 48px;
//         }
//       }

//       .talent-experience-info {
//         width: 50%;
//         flex: 0 0 auto;
//         padding-right: 2%;
//         line-height: 24px;

//         .talent-career-info {
//           margin-bottom: 12px;
//           height: 48px;
//         }

//         .talent-education-info {
//           height: 48px;
//         }

//         .talent-career-info-item {
//           display: flex;
//           position: relative;
//           padding-left: 36px;

//           &::before {
//             content: "";
//             background-image: url("@/assets/icons/icon-company.svg");
//             height: 24px;
//             width: 24px;
//             left: 0;
//             top: 0;
//             background-size: cover;
//             position: absolute;
//           }
//         }

//         .talent-education-info-item {
//           display: flex;
//           position: relative;
//           padding-left: 36px;

//           &::before {
//             content: "";
//             background-image: url("@/assets/icons/icon-education.svg");
//             height: 24px;
//             width: 24px;
//             left: 0;
//             top: 0;
//             background-size: cover;
//             position: absolute;
//           }
//         }

//         .info-time {
//           width: 140px;
//           flex: 0 0 auto;
//           color: #999;
//         }

//         .info-text {
//           flex: 1 1 auto;
//           white-space: nowrap;
//           overflow: hidden;
//           text-overflow: ellipsis;
//         }
//       }
//     }

//     .talent-actions {
//       width: 120px;
//       flex: 0 0 auto;
//       text-align: center;
//     }
//   }
// }
</style>