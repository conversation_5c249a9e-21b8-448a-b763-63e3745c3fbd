<template lang="pug">
mixin resume-section
  .resume-preview(v-if="resumeFile.fileAbsolutePath")
    //- VuePdfEmbed.pdf-resume(:source="resumeFile.fileAbsolutePath" :disableTextLayer="true")
    FilePreViewer(:url="resumeFile.fileAbsolutePath" :mime="resumeFile.mimeType")
  .resume-upload(v-else)
    //- :before-upload="beforeUpload",
    a-upload-dragger(
      name="file",
      :multiple="false",
      :showUploadList="false",
      :action="uploadRequestInfo.action",
      :headers="uploadRequestInfo.headers",
      @change="handleResumeUpload"
    )
      p.upload-drag-icon
        ContainerOutlined
      p.upload-drag-text 点击或将文件拖拽到这里上传
      p.upload-drag-hint 支持{{ supportExtension.join('、') }}格式，单个简历大小不超过6MB。

mixin form-basic-info
  .form-group
    h4 基本信息

    a-row(:gutter="[12, 0]")
      a-col(:span="4") 基本信息
      a-col(:span="10")
        a-form-item(
          label="姓名",
          :colon="false",
          :name="['talent', 'realName']",
          :rules="[{ required: true, message: '姓名不能为空', trigger: ['change'] }]"
        )
          a-input(v-model:value="talentForm.talent.realName" placeholder="请填写人才的真实姓名" @blur="handleDuplicateTalentCheck")

      a-col(:span="10")
        a-form-item(
          label="人才渠道", 
          name=['talent', 'sourceType'],
          :rules="[{ required: false, message: '人才渠道不能为空', trigger: ['change'] }]"
        )
          a-select(v-model:value="talentForm.talent.sourceType" :options="dict.talentSource" placeholder="请选择人才来源的渠道")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")

      a-col(:span="10")
        a-form-item(
          label="性别",
          :name="['talent', 'gender']",
          :rules="[{ required: false, message: '请选择性别' }]"
        )
          a-select(v-model:value="talentForm.talent.gender" placeholder="请选择人才性别")
            a-select-option(:value="1") 男
            a-select-option(:value="2") 女
            a-select-option(:value="3") 保密

      a-col(:span="10")
        a-form-item(
          label="所在城市",
          :name="['talent', 'areaId']",
          :rules="[{ type: 'number', message: '请选择人才所在城市', required: false, trigger: ['change', 'blur'] }]"
        )
          a-tree-select(
            :fieldNames="{ label: 'title', value: 'id' }",
            :tree-data="dict.area",
            placeholder="请选择人才当前所在城市",
            v-model:value="talentForm.talent.areaId"
          )

    a-row(:gutter="[12, 0]")
      a-col(:span="4")

      a-col(:span="10")
        a-form-item(
          label="生日",
          :name="['talent', 'birthday']",
          :rules="[{ required: false, message: '生日' }]"
        )
          a-date-picker(
            style="width: 100%",
            :allow-clear="false",
            :value="formatDatePickerValue(talentForm.talent.birthday)"
            @change="(value: any) => handleBirthdayChange(value)"
          )

      a-col(:span="10")
        a-form-item(
          label="婚姻状况",
          :name="['talent', 'marriage']",
          :rules="[{ required: false, message: '请选择婚姻状况' }]"
        )
          a-select(v-model:value="talentForm.talent.marriage")
            a-select-option(:value="0") 未知
            a-select-option(:value="1") 已婚
            a-select-option(:value="2") 未婚
            a-select-option(:value="3") 离异

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="10")
        a-form-item(label="工作年限", :name="['talent', 'workYears']")
          a-input-number(
            v-model:value="talentForm.talent.workYears",
            style="width:100%",
            :min="0",
            :max="60"
            placeholder="请选择人才工作年限",
          )

    a-row(:gutter="[12, 0]")
      a-col(:span="4") 联系方式
      a-col(:span="20")
        //- 手机号校验，请注意反斜杠需要进行转义
        a-form-item(
          label="电话",
          :name="['talent', 'mobileNumber']",
          :rules="[{ type: 'string', pattern: /^[\\d]*$/, required: true, validator: validatorEmailOrMobileNumber, message: '请输入正确的手机号码', trigger: ['chane', 'blur'] }]"
        )
          a-input(:maxlength="11" v-model:value="talentForm.talent.mobileNumber" placeholder="请输入人才的手机号码" @blur="handleDuplicateTalentCheck")
            template(#addonBefore)
              a-select(style="width: 140px;", :options="dict.mobileArea", show-search option-filter-prop="label" v-model:value="talentForm.talent.mobileArea")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="10")
        //- 邮箱验证
        a-form-item(
          label="邮箱",
          :colon="false",
          :name="['talent', 'email']",
          :rules="[{ type: 'email', required: true, validator: validatorEmailOrMobileNumber , message: '请输入正确的邮箱', trigger: ['change'] }]"
        )
          a-input(v-model:value="talentForm.talent.email" placeholder="请输入人才常用邮箱地址" @blur="handleDuplicateTalentCheck")

      a-col(:span="10")
        a-form-item(
          label="微信号",
          :colon="false"
        )
          a-input(v-model:value="talentForm.talent.wechatNumber" placeholder="请输入人才的微信账户")

    a-row(:gutter="[12, 0]")
      a-col(:span="4") 目前薪资
      a-col(:span="10")
        a-form-item(label="现金",)
          a-input(v-model:value="talentForm.talentDemand.currentCash" placeholder="请输入人才当前的薪资现金部分")
      a-col(:span="10")
        a-form-item(label="年终奖金")
          a-input(v-model:value="talentForm.talentDemand.currentYearlyBonus" placeholder="请输入人才的年终奖金额")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="10")
        a-form-item(label="五险一金")
          a-input(v-model:value="talentForm.talentDemand.currentInsurance")
      a-col(:span="10")
        a-form-item(label="其他福利")
          a-input(v-model:value="talentForm.talentDemand.currentIncentive")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="10")
        a-form-item(label="期权数量")
          a-input(v-model:value="talentForm.talentDemand.currentOptionQty")
      a-col(:span="10")
        a-form-item(label="发放时间")
          a-input(v-model:value="talentForm.talentDemand.currentOptionTime")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="10")
        a-form-item(label="发放价值")
          a-input(v-model:value="talentForm.talentDemand.currentOptionValue")

      a-col(:span="10")
        a-form-item(label="行权比例")
          a-input(v-model:value="talentForm.talentDemand.currentOptionRatio")

    a-row(:gutter="[12, 0]")
      a-col(:span="4")
      a-col(:span="20")
        a-space(:size="12")
          a-form-item
            a-checkbox-group(@change="(value:any) => handleSingleCheckBoxChange(value, 'ext_medical')")
              a-checkbox(value="1") 补充医疗保险
          a-form-item
            a-checkbox-group(@change="(value:any) => handleSingleCheckBoxChange(value, 'catastrophic_medical')")
              a-checkbox(value="1") 大额医疗保险

mixin form-demand-info
  .form-group
    h4 需求信息
    a-row
      a-col(:span="4") 求职意向
      a-col(:span="20")
        a-form-item(
          label="意向城市",
          :name="['talentDemand', 'areaDemand']",
          :rules="[{ type: 'array', message: '请选择人才期望城市', required: false, trigger: ['change', 'blur'] }]"
        )
          a-tree-select(
            :fieldNames="{ label: 'title', value: 'id' }",
            multiple="",
            placeholder="请选择人才求职意向城市（可多选）"
            treeNodeFilterProp="title",
            :tree-data="dict.area",
            v-model:value="talentForm.talentDemand.areaDemand"
            @change="handleDemondChange()"
          )

    a-row
      a-col(:span="4")
      a-col(:span="20")
        a-form-item(
          label="求职状态"
          :name="['talent', 'employeeStatus']",
          :rules="[{ required: false, message: '请选择求职状态' }]"
        )
          a-select(
            placeholder="请在下拉列表中选择当前求职状态",
            v-model:value="talentForm.talent.employeeStatus",
            :options="dict.employeeStatus",
          )

    a-row
      a-col(:span="4") 
      a-col(:span="20")
        a-form-item(label="期望行业")
          a-tree-select(
            multiple,
            allow-clear="",
            :fieldNames="{ label: 'title', value: 'id' }",
            placeholder="请选择期望行业（可多选）",
            :tree-data="dict.industry",
            treeNodeFilterProp="title",
            v-model:value="talentForm.talentDemand.currentIndustryTypes"
          )

    a-row
      a-col(:span="4") 
      a-col(:span="20")
        a-form-item(label="期望薪资")
          a-input(v-model:value="talentForm.talentDemand.targetTotalCash")

mixin form-education-info
  .form-group
    h4 教育信息
      .right_tools
        div(@click="addEducation")
          PlusOutlined
          span 添加
    .education_item(v-for="(educationItem, index) in talentForm.talentEducations", :key="'edu_' + index" )
      a-row(:gutter="[12, 0]")
        a-col(:span="4") 时间
        a-col(:span="10")
          a-form-item(
            label="起始时间",
            :name="['talentEducations', index, 'fromDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(educationItem.fromDate)"
              @change="(value: any) => handleStartDateChange(educationItem, value)"
            )

        a-col(:span="10")
          a-form-item(
            label="截止时间",
            :name="['talentEducations', index, 'toDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              v-if="educationItem.toDate !== null",
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(educationItem.toDate)"
              @change="(value: any) => handleEndDateChange(educationItem, value)"
            )
              template(#renderExtraFooter)
                .date-pikcer-footer
                  a-button(type="text" @click="()=>{educationItem.toDate = null}") 至今
            a-checkbox(
              v-else,
              :default-checked="educationItem.toDate === null",
              @change="(value: any) => {educationItem.toDate = '2000-1-1'}"
            ) 至今

      a-row(:gutter="[12, 0]")
        a-col(:span="4") 学校
        a-col(:span="10")
          a-form-item(
            label="学校名称",
            :name="['talentEducations', index, 'schoolId']",
            :rules="[{ required: false, message: '请选择学校' }]"
          )
            a-select(
              v-model:value="educationItem.schoolId",
              show-search,
              placeholder="请输入关键字并从下拉列表选择",
              :default-active-first-option="false",
              :show-arrow="false",
              :filter-option="false",
              :options="schoolList",
              @search="debouncedSchoolSearch"
            )
              template(#notFoundContent)
                p 您输入的学校不存在，是否添加？
                a-button(type="primary" ghost @click="showAddShool(educationItem)") 新增学校

        a-col(:span="10")
          a-form-item(
            label="专业"
            :name="['talentEducations', index, 'major']",
            :rules="[{ required: false, message: '请选择专业' }]"
          )
            a-input.long(v-model:value="educationItem.major")

      a-row(:gutter="[12, 0]")
        a-col(:span="4")
        a-col(:span="10")
          a-form-item(
            label="学历"
            :name="['talentEducations', index, 'degree']",
            :rules="[{ required: false, message: '请选择学历' }]"
          )
            a-select(
              placeholder="请选择学历",
              v-model:value="educationItem.degree",
              :options="dict.degree"
            )
        a-col(:span="10")
          a-form-item(label=" ")
            a-checkbox-group(
              @change="(value: any) => handleSingleCheckBoxChange(value, 'edu_unified', index)"
            )
              a-checkbox(value="1") 统招
      a-row
        a-col(:span="4") 专业描述
        a-col(:span="20")
          a-form-item()
            a-textarea(
              v-model:value="educationItem.majorDesc",
              :rows="4"
            )

      a-row
        a-col(:span="24" style="text-align:right;")
          a-popconfirm(
            title="确定要删除本条教育信息吗？",
            placement="left",
            @confirm="delEducation(index)"
          )
            MinusOutlined
            span 删除

mixin form-expirence-info
  .form-group
    h4 工作信息
      .right_tools
        div(@click="addExperience")
          PlusOutlined
          span 添加
    .experience_item(v-for="(experienceItem, index) in talentForm.talentExperiences",:key="'exp_' + index")
      a-row(:gutter="[12, 0]")
        a-col(:span="4") 时间
        a-col(:span="10")
          a-form-item(
            label="起始时间",
            :name="['talentExperiences', index, 'fromDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(experienceItem.fromDate)"
              @change="(value: any) => handleStartDateChange(experienceItem, value)"
            )

        a-col(:span="10")
          a-form-item(
            label="截止时间",
            :name="['talentExperiences', index, 'toDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              v-if="experienceItem.toDate !== null",
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(experienceItem.toDate)"
              @change="(value: any) => handleEndDateChange(experienceItem, value)"
            )
              template(#renderExtraFooter)
                .date-pikcer-footer
                  a-button(type="text", @click="()=>{experienceItem.toDate = null}") 至今
            a-checkbox(
              v-else,
              :default-checked="experienceItem.toDate === null",
              @change="(value: any) => {experienceItem.toDate = '2000-1-1'}"
            ) 至今

      a-row
        a-col(:span="4") 公司名称
        a-col(:span="20")
          a-form-item(
            :name="['talentExperiences', index, 'companyName']",
            :rules="[{ required: false, message: '请输入公司名称' }]"
          )
            a-input(v-model:value="experienceItem.companyName")
      a-row
        a-col(:span="4") 职位
        a-col(:span="20")
          a-form-item(
            :name="['talentExperiences', index, 'position']",
            :rules="[{ required: false, message: '请输入所在公司岗位' }]"
          )
            a-input(v-model:value="experienceItem.position")
      a-row
        a-col(:span="4") 工作内容
        a-col(:span="20")
          a-form-item
            a-textarea(
              v-model:value="experienceItem.jobDesc",
              :rows="4"
            )
      a-row
        a-col(:span="4") 职责
        a-col(:span="20")
          a-form-item
            a-textarea(
              v-model:value="experienceItem.duties",
              :rows="4"
            )

      a-row(:gutter="[12, 0]")
        a-col(:span="4") 公司信息
        a-col(:span="20")
          a-form-item(label="行业",)
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'industryCode' }",
              show-search,
              treeNodeFilterProp="title",
              :tree-data="dict.industry",
              v-model:value="experienceItem.industryCode"
            )

      a-row(:gutter="[12, 0]")
        a-col(:span="4")
        a-col(:span="10")
          a-form-item(label="规模",)
            a-select(
              placeholder="请选择公司规模",
              v-model:value="experienceItem.companyScale",
              :options="dict.companyScale"
            )
        a-col(:span="10")
          a-form-item(label="性质",)
            a-select(
              placeholder="请选择公司性质",
              v-model:value="experienceItem.companyType",
              :options="dict.companyType"
            )
      a-row
        a-col(:span="4")
        a-col(:span="20")
          a-form-item(label="介绍",)
            a-textarea(
              v-model:value="experienceItem.companyInfo",
              :rows="4"
            )

      a-row(:gutter="[12, 0]")
        a-col(:span="4") 职位信息
        a-col(:span="10")
          a-form-item(label="汇报人")
            a-input(v-model:value="experienceItem.reporter")
        a-col(:span="10")
          a-form-item(label=" ")
            a-checkbox-group(@change="(value: any) => handleSingleCheckBoxChange(value, 'exp_manage', index)")
              a-checkbox(value="1") 管理岗

      a-row(v-if="experienceItem.isManager")
        a-col(:span="4")
        a-col(:span="10")
          a-form-item(label="下属人数",)
            a-input-number(v-model:value="experienceItem.juniorNumber" style="width:100%" :min="0")

      a-row(justify="end")
        a-col(:span="24" style="text-align:right;")
          a-popconfirm(
            title="确定要删除本条工作经历吗？",
            placement="left",
            @confirm="delExperience(index)"
          )
            MinusOutlined
            span 删除

mixin form-project-info
  .form-group
    h4 项目信息
      .right_tools(@click="addProject")
        PlusOutlined
        span 添加
    .project_item(v-for="(projectItem, index) in talentForm.talentProjects", :key="index")
      a-row(:gutter="[12, 0]")
        a-col(:span="4") 时间
        a-col(:span="10")
          a-form-item(
            label="起始时间",
            :name="['talentProjects', index, 'fromDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(projectItem.fromDate)"
              @change="(value: any) => handleStartDateChange(projectItem, value)"
            )

        a-col(:span="10")
          a-form-item(
            label="截止时间",
            :name="['talentProjects', index, 'toDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              v-if="projectItem.toDate !== null",
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(projectItem.toDate)"
              @change="(value: any) => handleEndDateChange(projectItem, value)"
            )
              template(#renderExtraFooter)
                span(@click="()=>{projectItem.toDate = null}") 至今
            a-checkbox(
              v-else,
              :default-checked="projectItem.toDate === null",
              @change="(value: any) => {projectItem.toDate = '2000-1-1'}"
            ) 至今
      a-row
        a-col(:span="4") 项目名称
        a-col(:span="20")
          a-form-item
            a-input(v-model:value="projectItem.projectName")
      a-row
        a-col(:span="4") 所属公司
        a-col(:span="20")
          a-form-item
            a-input(v-model:value="projectItem.companyName")
      a-row
        a-col(:span="4") 项目职能
        a-col(:span="20")
          a-form-item
            a-input(v-model:value="projectItem.duties")
      a-row
        a-col(:span="4") 项目目标
        a-col(:span="20")
          a-form-item
            a-textarea(
              v-model:value="projectItem.projectWorkScope",
              :rows="4"
            )
      a-row
        a-col(:span="4") 项目信息
        a-col(:span="20")
          a-form-item( label="人数")
            a-input-number(v-model:value="projectItem.projectPeoples" :min="0" style="width: 100%")
      a-row
        a-col(:span="4")
        a-col(:span="20")
          a-form-item(
            label="介绍",
          )
            a-textarea(
              v-model:value="projectItem.projectDesc",
              :rows="4"
            )
      a-row(justify="end")
        a-col.right_tools.alert_color(:span="24")
          a-popconfirm(
            title="确定要删除本条项目经历吗？",
            placement="left",
            @confirm="delProject(index)"
          )
            MinusOutlined
            span 删除

mixin form-skill-info
  .form-group
    h4 技能信息
      .right_tools(@click="addSkill")
        PlusOutlined
        span 添加
    .skill_item(
      v-for="(skillItem, index) in talentForm.talentSkills",
      :key="index"
    )
      a-row(:gutter="[12,0]")
        a-col(:span="4") 技能信息
        a-col(:span="20")
          a-form-item(
            label="技能",
          )
            a-input(v-model:value="skillItem.skillType" block)

      a-row(:gutter="[12,0]")
        a-col(:span="4") 
        a-col(:span="10")
          a-form-item(
            label="使用时长",
          )
            a-input(v-model:value="skillItem.timeOfUse" block)

        a-col(:span="10")
          a-form-item(
            label="技能水平",
          )
            a-input(v-model:value="skillItem.competencyLevel" block)

      a-row(justify="end")
        a-col(:span="24" style="text-align:right")
          a-popconfirm(
            title="确定要删除本条技能信息吗？",
            placement="left",
            @confirm="delSkill(index)"
          )
            MinusOutlined
            span 删除

mixin form-extra-info
  .form-group
    h4 自我评价
    a-form-item()
      a-textarea(
        v-model:value="talentForm.talent.selfEvaluation",
        :rows="4"
      )

  .form-group
    h4 顾问推荐理由
    a-form-item()
      a-textarea(v-model:value="talentForm.talent.counselorEvaluation",:rows="4")

mixin add-school-form
  a-form(:model="addSchoolForm" ref="addSchoolFormInstance")
    a-form-item(
      label="地区"
      :label-col="{ span: 4 }",
      :wrappercol="{ span: 20 }"
      name="areaId"
      :rules="[{ type: 'number', message: '请选择学校所在地区', required: true, trigger: ['change', 'blur'] }]"
    )
      a-tree-select(
        :fieldNames="{ label: 'title', value: 'id' }",
        placeholder="请在下拉列表中选择城市",
        :tree-data="dict.area",
        v-model:value="addSchoolForm.areaId"
      )

    a-form-item(
      label="英文名称"
      labelAlign="left",
      :label-col="{ span: 4 }",
      :wrappercol="{ span: 20 }"
      name="ename"
      :rules="[{message: '请输入学校的英文名称', required: true, trigger: ['change', 'blur'] }]"
    )
      a-input(v-model:value="addSchoolForm.ename")

    a-form-item(
      label="中文名称"
      labelAlign="left",
      :label-col="{ span: 4 }",
      :wrappercol="{ span: 20 }"
      name="name"
      :rules="[{required: true, message:'请输入学校的中文名称', trigger:['change', 'blur']}]"
    )
      a-input(v-model:value="addSchoolForm.name")

    a-form-item(
      label="学校类型"
      labelAlign="left",
      :label-col="{ span: 4 }",
      :wrappercol="{ span: 20 }"
      name="kinds"
    )
      a-select(
        mode="multiple"
        :options="dict.schoolKind"
        v-model:value="addSchoolForm.kinds"
      )

    a-form-item(
      label="学校级别"
      labelAlign="left",
      :label-col="{ span: 4 }",
      :wrappercol="{ span: 20 }"
      name="level"
      :rules="[{required: true, message:'请选择学校的级别', trigger:['change', 'blur']}]"
    )
      a-select(
        :options="dict.schoolLevel"
        v-model:value="addSchoolForm.level"
      )

mixin duplicate-talent-selector
  .duplicate-talent-selector
    p 
      strong 当前人才
    //- .duplicate-talent(v-for="(talent, index) in duplicateTalents" @click="()=>{targetCombineTalent = talent}" :class="{selected: targetCombineTalent.talent.id === talent.talent.id}")
    .duplicate-talent()
      div(v-for="(row, index) in getTalentSummary(talentForm)")
        em {{ `${row[0]} ` }}
        span | {{ row.slice(1).join(' | ')}}

    p 
      strong 重复人才
    .duplicate-talent(v-for="(talent, index) in duplicateTalents" @click="()=>{targetCombineTalent = talent}" :class="{selected: targetCombineTalent.talent.id === talent.talent.id}")
      div(v-for="(row, index) in getTalentSummary(talent)")
        em {{ `${row[0]} ` }}
        span | {{ row.slice(1).join(' | ')}}

mixin form-section
  a-form(:model="talentForm", label-align="left", ref="talentFormInstance", :scrollToFirstError="true", layout="vertical")
    +form-basic-info
    +form-demand-info
    +form-education-info
    +form-expirence-info
    +form-project-info
    +form-skill-info
    +form-extra-info

.talent-edit-page
  a-page-header( title="新增人才", sub-title="", @back="()=>{$router.go(-1)}", style="padding: 0 0 8px;" )
    template(#extra)
      a-button(type="primary" @click="reset()" v-if="resumeFile.fileAbsolutePath") 重新上传

  a-spin(:spinning="status.loading")
    .talent-edit-body(v-if="!status.showSuccess")
      a-row(:gutter="[24, 0]")
        a-col(:span="12")
          +resume-section
        a-col.main-form(:span="12")
          +form-section
          .submit_panel(v-if="!status.hasDuplicateTalent")
            a-button(type="primary", @click="submit") 保存人才信息
          .duplicate_panel(v-else)
            div 发现重复人才，是否合并人才？
            a-button(type="primary", @click="status.showDuplicateCombine = true") 查看重复人才

    a-result(v-else title="人才创建成功！" status="success")
      template(#extra)
        a-button(type="primary" @click="() => { $router.go(-1) }") 返回
        a-button(type="primary" ghost @click="() => { $router.push('/talent/list/me') }") 我的人才
        a-button(type="primary" ghost @click="() => { $router.push(`/talent/${talentForm.talent.id}/detail`) }") 查看人才详情
        a-button(type="primary" ghost @click="reset()") 继续添加人才

  a-modal(title="添加学校" v-model:open="status.showAddSchool" :confirmLoading="status.addSchoolLoading" @ok="addSchool")
    +add-school-form

  a-modal(title="合并人才" v-model:open="status.showDuplicateCombine")
    +duplicate-talent-selector
    template(#footer)
      a-space(:size="12")
        a-button(@click="()=> {status.showDuplicateCombine = false}") 取消
        a-button(type="primary" :disabled="!targetCombineTalent.talent.id", :loading="status.combineTalent", @click="combineTalents()") 确定合并

</template>

<script lang="ts" setup>
import VuePdfEmbed from 'vue-pdf-embed'
import dayjs from 'dayjs'
import querystring from 'query-string'

import { onMounted, reactive, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { getTalentDetail, createOrUpdateTalent, checkTalentDuplicate, getMergedTalent } from '@/api/talent/talent'
import {
  getCompanyTypeList, getCompanyScaleList,
  getAllIndustryList, getAllFunctionList, getSchoolList,
  createSchool, getSchoolKind, getSchoolLevel, getTalentSource, dictionary
} from '@/api/dictionary'
import { LoadingOutlined, PlusOutlined, MinusOutlined, ContainerOutlined, ConsoleSqlOutlined } from '@ant-design/icons-vue'
import { API_URL } from '@/api/talent/talent'
import { useUserStore } from '@/store/user.store'
import { useMemoStore } from '@/store/memo.store'
import { debounce } from '@/utils/util'
import { areaDictToTreeData, industryDictToTreeData } from '@/utils/form-data-helper'
import { createCandidateTalentResumeBind } from '@/api/marketing'
import { phoneAreaDict } from '@/utils/phone-area-dict'
import FilePreViewer from '@/components/file-previewer/file-previewer.vue'
import { QUERY_ACTION } from '@/api/job'
import type { Rule } from 'ant-design-vue/es/form';

const supportExtension = ['txt', 'pdf', 'doc', 'docx', 'mht', 'jpg', 'jpeg', 'png']

const router = useRouter()
const originData = ref<any>()

const userStore = useUserStore()
const uploadRequestInfo = {
  action: API_URL.RESUME_UPLOAD,
  headers: {
    Authorization: userStore.token
  }
}

const status = reactive({
  loading: false,
  showAddSchool: false,
  addSchoolLoading: false,
  showSuccess: false,
  hasDuplicateTalent: false,
  showDuplicateCombine: false,
  combineTalent: false,
})

const talentFormInstance = ref()
const resumeFile = ref<any>({})
const resumeFileParsedId = ref<number>()
const talentForm = ref({
  talent: {},
  talentDemand: {},
  talentEducations: [],
  talentExperiences: [],
  talentProjects: [],
  talentSkills: [],
  originResumeParsedId: 0
} as {
  talent: any,
  talentDemand: any,
  talentEducations: any[]
  talentExperiences: any[]
  talentProjects: any[]
  talentSkills: any[]
  originResumeParsedId: number
})

const dict = reactive({
  degree: [] as any[],
  companyType: [] as any[],
  companyScale: [] as any[],
  area: [] as any,
  industry: [] as any,
  function: [] as any[],
  employeeStatus: [] as any[],
  schoolKind: [] as any[],
  schoolLevel: [] as any[],
  talentSource: [] as any[],
  mobileArea: [] as any[]
})

const validatorEmailOrMobileNumber  = (_rule: Rule,  value: string) => {
  if (_rule.pattern && value) {
    if (_rule.pattern.test(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('Please input');
    }
  }
  
  if (talentForm.value.talent.email || talentForm.value.talent.mobileNumber) {
    return Promise.resolve();
  } 
 
  return Promise.reject('Please input');
}

async function initDict() {
  const dictPromiseList = [
    dictionary.getDegree(), getCompanyTypeList(), getCompanyScaleList(), dictionary.getAllAreaList(),
    getAllIndustryList(), getAllFunctionList(), dictionary.getEmployeeStatus(), getSchoolKind(),
    getSchoolLevel(), getTalentSource()
  ]

  const [
    dictDegree, dictCompanyType, dictCompanyScale, dictArea,
    dictIndustry, dictFunction, dictEmployeeStatus, dictSchoolKindRes,
    dictSchoolLevelRes, dictTalentSource
  ] = await Promise.all(dictPromiseList)

  for (let id in dictDegree.data) {
    dict.degree.push({ value: Number(id), label: dictDegree.data[id] })
  }

  dict.talentSource = Object.entries(dictTalentSource.data).map((item, index) => {
    const [value, label] = item
    return { value: Number(value), label }
  })

  const [areaDictTreeData] = areaDictToTreeData(dictArea.data)
  dict.area = areaDictTreeData

  const [industryDictTreeData] = industryDictToTreeData(dictIndustry.data)
  dict.industry = industryDictTreeData

  dict.mobileArea = phoneAreaDict.map((item: any, index: number) => {
    return { label: `${item.number} ${item.areaNameCN}`, value: item.number }
  })

  dict.function = dictFunction.data.map((item: any, index: number) => {
    return { value: item.id, label: item.name }
  })

  dict.employeeStatus = dictEmployeeStatus.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })

  dict.schoolLevel = dictSchoolLevelRes.data.map((item: any, index: number) => {
    return { value: item.id, label: item.title }
  })
  dict.schoolKind = dictSchoolKindRes.data
  dict.companyScale = dictCompanyType.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })
  dict.companyType = dictCompanyScale.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })
}

function handleBirthdayChange(value: any) {
  if (value) talentForm.value.talent.birthday = value.format('YYYY-MM-DD')
  else talentForm.value.talent.birthday = value
}

function handleStartDateChange(target: any, value: any) {
  if (value) target.fromDate = value.format('YYYY-MM-DD')
  else target.fromDate = value
}

function handleEndDateChange(target: any, value: any) {
  if (value) target.toDate = value.format('YYYY-MM-DD')
  else target.toDate = value
}

const schoolList = ref<any[]>([])
function getTalentEducationSchoolOption() {
  const schoolSearchList = [] as any[]
  talentForm.value.talentEducations.forEach((item: any, index: number) => {
    schoolSearchList.push({
      value: item.schoolId,
      label: item.schoolName
    })
  })
  schoolList.value = schoolSearchList
}

function getDictSchoolList(keyword: string) {
  getSchoolList(keyword, 1, 20).then((res) => {
    const schoolSearchList = [] as any[]
    res.data.forEach((schoolItem: any) => {
      schoolSearchList.push({
        value: schoolItem.id,
        label: schoolItem.name
      })
    })
    schoolList.value = schoolSearchList
  })
}

const debouncedSchoolSearch = debounce(getDictSchoolList)

function addEducation() {
  talentForm.value.talentEducations.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    schoolId: null,
    schoolName: '',
    major: '',
    majorDesc: '',
    degree: null,
    isUnified: 0
  })
}

function addProject() {
  talentForm.value.talentProjects.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    companyName: '',
    fromDate: dayjs(),
    toDate: null,
    lastModifyTime: 0,
    createBy: 0,
    lastUpdatedBy: 0,
    projectName: '',
    projectDesc: '',
    duties: '',
    talentTitle: ''
  })
}

function addExperience() {
  talentForm.value.talentExperiences.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    companyName: '',
    companyInfo: '',
    companyScale: 0,
    companyType: 0,
    industryCode: '',
    isManager: 0,
    jobDesc: '',
    juniorNumber: 0,
    position: '',
    reporter: '',
    duties: ''
  })
}

function addSkill() {
  talentForm.value.talentSkills.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    skillType: '',
    timeOfUse: '',
    competencyLevel: '',
  })
}

function delEducation(index: number) {
  talentForm.value.talentEducations.splice(index, 1)
}

function delExperience(index: number) {
  talentForm.value.talentExperiences.splice(index, 1)
}

function delSkill(index: number) {
  talentForm.value.talentSkills.splice(index, 1)
}

function delProject(index: number) {
  talentForm.value.talentProjects.splice(index, 1)
}

function formatDatePickerValue(data: any) {
  if (data) return dayjs(data)
  else return null
}

function handleSingleCheckBoxChange(value: any, type: string, index: number) {
  const checkedValue = value[0] ? 1 : 0
  switch (type) {
    case 'ext_medical':
      talentForm.value.talentDemand.currentExtMedical = checkedValue
      break
    case 'catastrophic_medical':
      talentForm.value.talentDemand.currentCatastrophicMedical = checkedValue
      break
    case 'edu_unified':
      talentForm.value.talentEducations[index].isUnified = checkedValue
      break
    case 'exp_manage':
      talentForm.value.talentExperiences[index].isManager = checkedValue
      break
  }
}

async function checkDuplicate() {
  try {
    const res = await checkTalentDuplicate({
      idCardNumber: '',
      realName: talentForm.value.talent.realName || '',
      mobileNumber: talentForm.value.talent.mobileNumber || '',
      email: talentForm.value.talent.email || ''
    })

    // 如果检查时，返回的是一样的结果，则表示不检查
    if (duplicateTalents.value.join() == res.data.join()) {
      return
    } else {
      duplicateTalents.value = res.data
      if (duplicateTalents.value.length) {
        status.hasDuplicateTalent = true
      } else {
        status.hasDuplicateTalent = false
      }
    }
  } catch (err) {
    return 
  }
}

const route = useRoute()

async function submit() {
  status.loading = true
  try {
    // 表单验证
    await talentFormInstance.value?.validate()
    const res = await createOrUpdateTalent(talentForm.value)
    talentForm.value.talent.id = res.data

    // 清除区别对比
    originData.value = JSON.stringify(talentForm.value)

    // 绑定人才到candidate
    const candidateId = Number(route.query.candidate_id)
    if (candidateId) await createCandidateTalentResumeBind({ candidateId: candidateId, talentId: res.data, resumeOriginParsedId: talentForm.value.originResumeParsedId })

    status.showSuccess = true
  } catch (err: any) {
    if (err.errorFields) {
      const firstError = err.errorFields[0]
      message.error(firstError.errors.join(','))
      talentFormInstance.value?.scrollToField(firstError.name, { behavior: 'smooth', block: 'center' })
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

const addSchoolForm = reactive({
  areaId: null,
  ename: '',
  name: '',
  kinds: [],
  level: null,
})

let targetSchoolItem: any

function showAddShool(target: any) {
  targetSchoolItem = target
  status.showAddSchool = true
}

async function addSchool() {
  status.addSchoolLoading = true
  try {
    let kind = 0
    addSchoolForm.kinds.forEach((item, index) => { kind += item })
    const res = await createSchool({
      areaId: addSchoolForm.areaId,
      ename: addSchoolForm.ename,
      name: addSchoolForm.name,
      kind: kind,
      level: addSchoolForm.level
    })
    targetSchoolItem.schoolId = res.data
    targetSchoolItem.schoolName = addSchoolForm.name
    schoolList.value = [{ value: res.data, label: addSchoolForm.name }]
    status.showAddSchool = false
  } catch (err: any) {
    message.error(err.message)
  }
  status.addSchoolLoading = false
}

function talentDataProcess(talent: any) {
  talentForm.value = talent

  if (!talent.talent.mobileArea) talentForm.value.talent.mobileArea = '+86'
  if (talent.talent.gender === 0) talentForm.value.talent.gender = null

  talent.talentEducations.forEach((item: any, index: Number) => {
    schoolList.value.push({ label: item.schoolName, value: item.schoolId })
  })

  if (resumeFileParsedId.value) talentForm.value.originResumeParsedId = resumeFileParsedId.value
}

const duplicateTalents = ref<any[]>([])
async function handleResumeUpload(info: any) {
  const uploadStatus = info.file.status
  if (uploadStatus === 'uploading') {
    status.loading = true
  }

  if (uploadStatus === 'done') {
    const response = info.file.response
    // talentForm.value = response.data.talentInfo
    // 上传简历时获取originResumeParsedId
    resumeFileParsedId.value = response.data.talentInfo.originResumeParsedId
    resumeFile.value = response.data.fileInfo
    status.loading = false
    talentDataProcess(response.data.talentInfo)
    message.success(`文件${info.file.name}解析成功。`)

    // 检查重复人才
    // duplicateTalents.value = 
    await checkDuplicate()
    // if (duplicateTalents.value.length) {
    //   status.hasDuplicateTalent = true
    // } else {
    //   status.hasDuplicateTalent = false
    // }
  }

  if (uploadStatus === 'error') {
    status.loading = false
    message.error(`抱歉，文件${info.file.name}上传失败，请稍后重试。`)
  }
}

async function handleDuplicateTalentCheck() {
  // duplicateTalents.value = 
  await checkDuplicate()
  // if (duplicateTalents.value.length) {
  //   status.hasDuplicateTalent = true
  // } else {
  //   status.hasDuplicateTalent = false
  // }
}

const targetCombineTalent = ref({
  talent: {} as any
})

function getTalentSummary(talent: any) {
  const result = []
  result.push([talent.talent.realName, talent.talent.mobileNumber, talent.talent.email])

  const firstEducationInfo = Array.isArray(talent.talentEducations) ? talent.talentEducations[0] : null
  if (firstEducationInfo) result.push([
    firstEducationInfo.schoolName, firstEducationInfo.major,
    `${dayjs(firstEducationInfo.fromDate).format('YYYY.MM')} - ${dayjs(firstEducationInfo.toDate).format('YYYY.MM')}`
  ])

  Array.isArray(talent.talentExperiences) && talent.talentExperiences.forEach((item: any, index: number) => {
    result.push([
      item.companyName, item.position,
      `${dayjs(item.fromDate).format('YYYY.MM')} - ${dayjs(item.toDate).format('YYYY.MM')}`
    ])
  })
  return result
}

async function combineTalents() {
  status.combineTalent = true
  try {
    const res = await getMergedTalent(targetCombineTalent.value.talent.id, talentForm.value)
    talentDataProcess(res.data)
    message.success('合并后的人才信息已填入表单')
  } catch (err: any) {
    message.error(err.message)
  }
  status.hasDuplicateTalent = false
  status.showDuplicateCombine = false
  status.combineTalent = false
}

function reset() {
  status.loading = false
  status.showAddSchool = false
  status.addSchoolLoading = false
  status.showSuccess = false
  status.hasDuplicateTalent = false
  status.showDuplicateCombine = false
  status.combineTalent = false

  resumeFile.value = {}

  talentFormInstance.value?.clearValidate()
  talentForm.value = {
    talent: {},
    talentDemand: {},
    talentEducations: [],
    talentExperiences: [],
    talentProjects: [],
    talentSkills: [],
    originResumeParsedId: 0
  }

  duplicateTalents.value = []
}

onBeforeRouteLeave((to, from, next) => {
  if (originData.value !== JSON.stringify(talentForm.value)) {
    Modal.confirm({
      title: '确认要离开？',
      content: '如果离开，未保存的数据将会丢失。',
      onOk() { 
        next(true) 
      },
      onCancel() { next(false) }
    })
  } else {
    next(true)
  }
})

onMounted(() => {
  initDict()
})

const memoStore = useMemoStore()
onMounted(() => {
  reset()
  const talentParseResult = memoStore.getOnce('talent-resume-parse')
  if (talentParseResult) {
    talentDataProcess(talentParseResult.talentInfo)
    resumeFile.value = talentParseResult.fileInfo

    checkDuplicate()
  }
})

function handleDemondChange() {

}
</script>

<style lang="scss" scoped>
h3 {
  margin-bottom: 12px;
}

.talent-edit-body {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 24px;
  border-radius: 8px;

  .resume-preview {
    position: sticky;
    top: 64px;
    height: calc(100vh - 84px);
    overflow: scroll;
  }

  .resume-upload {
    position: sticky;
    top: 64px;

    .upload-drag-icon {
      color: #ff9111;
      font-size: 40px;
    }

    .upload-drag-text {
      font-weight: bold;
      font-size: 18px;
    }

    .upload-drag-hint {
      padding: 0 16px;
      color: #999;
    }
  }
}

.resume-container {
  padding-right: 24px;
  height: 81vh;
  width: 100%;

  // border: 1px solid #f0f0f0;
  // overflow: scroll;
  // overflow: scroll;

  .resume-wrapper {
    width: 100%;
    height: 100%;
    border: 1px solid #f0f0f0;
    overflow-y: scroll;

    .pdf-resume {
      width: 100%;
      overflow: hidden;
    }
  }
}

.resume-form {
  margin: -24px 0;
}

.form-group {
  padding: 24px 0px 24px 16px;
  border-bottom: 1px solid #f0f0f0;

  >*:nth-child(2) {
    margin-top: 24px;
  }

  h4 {
    line-height: 56px;
    font-size: 18px;
    font-weight: bold;
    position: sticky;
    top: 64px;
    background-color: #fff;
    z-index: 1;

    &::before {
      content: "";
      display: block;
      background-color: #ff9111;
      height: 20px;
      width: 3px;
      top: 18px;
      left: -16px;
      border-radius: 2px;
      position: absolute;
    }
  }


  .right_tools {
    font-size: 14px;
    line-height: 56px;
    cursor: pointer;
    float: right;
    text-align: right;

    &:hover {
      color: #ff9111;
    }
  }
}

span.required {
  color: #ff9111;
}

.education_item,
.experience_item,
.project_item,
.skill_item {
  margin: -16px;
  padding: 16px;
  padding-bottom: 16px;
  margin-bottom: 32px;
  border-radius: 8px;
  transition: all .3s;

  &:hover {
    background-color: #FAFAFA;
    transition: all .3s;
  }
}

.date-pikcer-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.submit_panel {
  // width: 100%;
  // padding: 16px 0px;
  // text-align: right;
  // background: #fff;
  // border-top: 1px solid #f0f0f0;

  position: sticky;
  bottom: 16px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  z-index: 2;
  box-shadow: 0 2px 10px RGBA(0, 0, 0, 0.08);
}

.duplicate_panel {
  position: sticky;
  bottom: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff1df;
  border: 1px solid #ff9111;
  box-shadow: 0 2px 10px RGBA(255, 171, 17, 0.3);
  border-radius: 8px;
  z-index: 2;
}

.duplicate-talent-selector {

  .duplicate-talent {
    cursor: pointer;
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 6px;
    background-color: #FAFAFA;
    border: 1px solid #FAFAFA;

    &.selected {
      border: 1px solid #ff9111;
      background-color: #fff1df;
    }

    em {
      font-weight: bold;
      font-style: normal;
    }
  }
}
</style>  
