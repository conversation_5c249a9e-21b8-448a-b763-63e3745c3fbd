<template lang="pug">
.talent-upload-page
  h2 全部人才

  .page-header 
    a-page-header(
      title="新增人才",
      sub-title="",
      @back="goBack",
      style="padding: 0 0 8px;"
    )

  .page-main
    .talent-uploader
      a-spin(:spinning="status.loading")
        a-upload-dragger(
          name="file",
          :before-upload="beforeUpload",
          :multiple="false",
          :showUploadList="false",
          :action="uploadRequestInfo.action",
          :headers="uploadRequestInfo.headers",
          @change="handleChange"
        )
          p.upload-drag-icon
            ContainerOutlined
          p.upload-drag-text 点击或将文件拖拽到这里上传
          p.upload-drag-hint 支持{{ supportExtension.join('、') }}格式，单个简历大小不超过6MB。
</template>

<script lang="ts">
import { ref } from 'vue'
import { ContainerOutlined } from '@ant-design/icons-vue'
import { reactive } from '@vue/reactivity'
import { useUserStore } from '@/store/user.store'
import { message, UploadChangeParam } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { API_URL } from '@/api/talent/talent'
import { useMemoStore } from '@/store/memo.store'

export default {

  components: {
    ContainerOutlined
  },

  setup() {

    const userstore = useUserStore()
    const menoStore = useMemoStore()
    const supportExtension = ['txt', 'pdf', 'doc', 'docx', 'mht', 'jpg', 'jpeg', 'png']

    const router = useRouter()

    const uploadRequestInfo = {
      action: API_URL.RESUME_UPLOAD,
      headers: { Authorization: userstore.token }
    }

    function beforeUpload(fileObject: File) {
      const fileExtention = fileObject.name?.split('.').pop()
      if (!supportExtension.includes(fileExtention!)) {
        message.error('抱歉，暂不支持您上传的文件格式。')
        return false
      }
      return true
    }

    const status = reactive({
      loading: false,
    })

    function handleChange(info: UploadChangeParam) {
      const uploadStatus = info.file.status
      if (uploadStatus === 'uploading') {
        status.loading = true
      }

      if (uploadStatus === 'done') {
        status.loading = false
        message.success(`文件${info.file.name}上传成功。`)
        const response = info.file.response
        const talent = response.data.talentInfo

        menoStore.set('talent-resume-parse', response.data)
        // debugger

        if (talent.talent.id) {
          router.push(`/talent/${talent.talent.id}/edit`)
        } else {
          router.push(`/talent/create`)
        }
      }

      if (uploadStatus === 'error') {
        message.error(`抱歉，文件${info.file.name}上传失败，请稍后重试。`)
      }
    }

    function goBack() {
      router.go(-1)
    }

    return { supportExtension, beforeUpload, uploadRequestInfo, goBack, handleChange, status }
  }
}
</script>

<style lang="scss" scoped>
.talent-upload-page {
  border-radius: 6px;
  overflow: hidden;

  h2 {
    display: none;
  }

  .page-main {
    min-height: 420px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    background-color: #fff;

    .talent-uploader {
      width: 600px;
      margin: 0 auto;

      .upload-drag-icon {
        color: #ff9111;
        font-size: 40px;
        font-weight: bold;
      }

      .upload-drag-text {
        font-size: 16px;
        font-weight: bold;
        margin: 8px 0;
      }

      .upload-drag-hint {
        font-size: 13px;
        color: #999;
        padding: 0 32px;
      }
    }
  }
}
</style>
