<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { SearchOutlined, LoadingOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
import { WORK_YEAR } from '@/consts/multi'
import { getMultiSearchTalentList, getMultiSearchTalentParams, saveSmartDeerAppTalent, unionSearchTranslateParams, unionSearchTalentDetail } from '@/api/talent/talent'
import { dictionary, getAllIndustryList, getDegreeList } from "@/api/dictionary"
import { message } from 'ant-design-vue'
import { setSearchBury } from "@/api/track"

import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import TalentSearchItem from '@/components/app/talent-search-item.vue'
import ChatBox from '@/components/chat/chat-box.vue'

import LiepinService from '@/service/multi/liepin/index-v2'
import MaimaiService from '@/service/multi/maimai/index-v2'
import SmartDeerService from '@/service/multi/smartdeer/index-v2'
import BossService from '@/service/multi/boss/index-v2'
import LinkedInService from '@/service/multi/linkedin/index-v2'
import ItpTalentService from '@/service/multi/itp-talent/index-v2'
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { watch } from 'vue'
import tracker from '@/utils/tracker'
import GlobalTalentService from '@/service/multi/global-talent/index-v2'
import TalentGlobalSearchItem from '@/components/app/talent-global-search-item.vue'
import { unionSearchParamsCompany, unionSearchParamsMajor, unionSearchParamsSchool, unionSearchParamsArea } from '@/api/talent/talent'
import TalentGlobalDetailModal from '@/components/app/talent-global-detail-modal.vue'
import AIChatBox from '@/components/ai/ai-chat-box.vue'

const env = import.meta.env.VITE_VUE_APP_BUILD_ENV

const liepin = new LiepinService()
const maimai = new MaimaiService()
const smartdeer = new SmartDeerService()
const boss = new BossService()
const linkedin = new LinkedInService()
const itpTalent = new ItpTalentService()
const globalTalent = new GlobalTalentService()

const talentId = ref<number>()
const trackId = ref('')

const globalTalentId = ref<number>()

// jobId是当全网搜针对某个职位进行搜索时，需要的参数。
const props = defineProps<{ jobId?: number }>()

const status = reactive({
  spinning: false,
  showTalentDetail: false,
  showGlobalTalentDetail: false,
  showChatBox: false,
  initDict: false,
  showAIChatBox: false
})

const activeTab = ref('all')
const totalTalentList = ref([] as any[])

const form = ref<UnionSearchParams>({
  keywords: [],
  ageFrom: 16,
  ageTo: 80,
  //0-不限 1-男， 2-女
  gender: 0,
  workAgeFrom: null,
  workAgeTo: null,
  companyIds: [], // 公司Ids
  companies: [], // 公司
  companyJoined: '',
  schoolIds: [], // 学校Ids
  schools: [], // 学校名称
  majorId: [], // 专业Ids
  major: '', // 专业
  is211: false,
  is985: false,
  cities: [],
  areaIds: [],
  demandAreaIds: [],
  educationDegree: 0,
  nationalUnified: 0,
  companyPeriod: 0,
  positionPeriod: 0,
  overseasWork: false,
  positionKeywordsAny: false,
  positionName: [],
  currentSalaryFrom: 0,
  currentSalaryTo: 0,
  demandSalaryFrom: 0,
  demandSalaryTo: 0,
  searchCount: 200,
  industryIds: [],
  schoolLevel: [],

  isFirstClass: false,
  isOverseaStudy: false,
  // 领域
  fields: [],

  phone: '',
  email: '',
  name: '',
})

const dict = reactive({
  degree: [] as any[],
  degreeMap: [] as any[],
  industry: [] as any[],
  city: [] as any[],
})

const options = reactive({
  keywords: [],
  workYear: WORK_YEAR,
  fields: [],
  companies: [] as any[],
  nationalUnified: [
    { label: '不限(统招)', value: 0 },
    { label: '统招', value: 1 },
  ],
  schoolLevel: [
    { label: '985', value: '985' },
    { label: '211', value: '211' },
    { label: '双一流', value: '双一流' },
    { label: '海外留学', value: '海外留学' },
  ],
  companyPeriod: [
    { label: '不限', value: 0 },
    { label: '仅限当前', value: 1 },
  ],
  positionPeriod: [
    { label: '不限', value: 0 },
    { label: '仅限当前', value: 1 },
  ],
  schools: [] as any[],
  majors: [] as any[],
  relateKeywords: []
})

// 这一组定义用于控制页面展示不支持的选项。
const unavailableFormItems = ref<any[]>([])
const UNAVAILABLE_FORM_ITEM_MAP = new Map()
UNAVAILABLE_FORM_ITEM_MAP.set('all', [])
UNAVAILABLE_FORM_ITEM_MAP.set('liepin', ['name', 'phone', 'email'])
UNAVAILABLE_FORM_ITEM_MAP.set('boss', ['overseasWork', 'schools', 'company', 'positions', 'currentCity', 'industry', 'name', 'phone', 'email', 'currentSalary'])
UNAVAILABLE_FORM_ITEM_MAP.set('maimai', ['age', 'gender', 'overseasWork', 'expectCity', 'currentSalary', 'expectSalary', 'name', 'phone', 'email'])
UNAVAILABLE_FORM_ITEM_MAP.set('smartdeer', ['industry', 'expectCity', 'currentSalary', 'expectSalary'])
UNAVAILABLE_FORM_ITEM_MAP.set('itp', ['expectCity', 'currentSalary', 'expectSalary'])
UNAVAILABLE_FORM_ITEM_MAP.set('linkedin', ['age', 'gender', 'degree', 'schools'])
UNAVAILABLE_FORM_ITEM_MAP.set('globalTalent', ['industry'])

// 这两个map用于转换ITP参数的时候，将IT转成字符串使用
let IndustryMap = new Map()
let AreaMap = new Map()

// 初始化字典
async function initDict() {
  status.initDict = true
  try {
    const [dictArea, dictIndustry] = await Promise.all([
      unionSearchParamsArea(), getAllIndustryList()
    ])

    const dictAreaTreeData = [] as any[]
    const areaList = dictArea.data.list
    areaList.forEach((item: any, index: number) => {
      const childAreaList = item.child
      const dictChildAreaTreeData = [] as any[]
      for (let i = 0; i < childAreaList.length; i++) {
        const child = childAreaList[i]
        dictChildAreaTreeData.push({
          title: child.name,
          id: child.id,
          children: []
        })
        AreaMap.set(child.id, child)
      }
      const tmpObject = {
        title: item.name,
        id: item.id,
        children: dictChildAreaTreeData
      }
      dictAreaTreeData.push(tmpObject)
      AreaMap.set(item.id, item)
    })
    // const [dictAreaTreeData, dictAreaMap] = areaDictToTreeData(dictArea.data)
    dict.city = dictAreaTreeData as any[]
    // AreaMap = dictAreaMap as Map<number, any>

    const dictIndustryMap = new Map()
    const tempIdustryList = [] as any[]
    dictIndustry.data.forEach((item: any, index: number) => {
      const targetObj = Object.assign({}, item, { label: item.industryName, value: item.id, children: [] })
      dictIndustryMap.set(item.id, targetObj)
      if (item.parentId === 0) tempIdustryList.push(targetObj)
    })

    dictIndustryMap.forEach((item, key) => {
      if (item.parentId === 0) return
      const parent = dictIndustryMap.get(item.parentId)
      parent.children.push(item)
    })

    dict.industry = tempIdustryList
    IndustryMap = dictIndustryMap

    // 初始化教育经历字典
    const res = await getDegreeList()
    dict.degreeMap = res.data
    dict.degree = Object.keys(res.data).map((value: any) => {
      return { label: res.data[value], value: parseInt(value) }
    })
  } catch (err: any) {
    message.error('抱歉，初始化字典失败, 请重试。')
  }
  status.initDict = false
}

// 这里用于嵌入到项目详情时，自动获取分析出的参数
async function initJobSearchParams(jobId: number) {
  if (!jobId) return
  const { data } = await getMultiSearchTalentParams(jobId)
  form.value.is211 = data['211']
  form.value.is985 = data['985']
  form.value.areaIds = data.cities
  form.value.companies = data.companies
  form.value.educationDegree = data.education_degree
  form.value.workAgeFrom = data.work_age_from === 0 ? null : data.work_age_from
  form.value.workAgeTo = data.work_age_to === 0 ? null : data.work_age_to
  form.value.keywords = []

  data.position_keywords?.forEach((item: any) => {
    if (item.important) form.value.keywords.push(item.word)
  })

  options.relateKeywords = data.related_keywords
  options.keywords = data.related_keywords?.map((item: any) => {
    return { value: item, label: item }
  })

  form.value.fields = []
}

function handleSchoolLevelChange(value: any) {
  form.value.is211 = false
  form.value.is985 = false
  form.value.isFirstClass = false
  form.value.isOverseaStudy = false

  form.value.is211 = value.includes('211')
  form.value.is985 = value.includes('985')
  form.value.isFirstClass = value.includes('双一流')
  form.value.isOverseaStudy = value.includes('海外留学')
}

function handleWorkYearChange(value: any) {
  const [min, max] = value.split(',')
  form.value.workAgeFrom = parseInt(min)
  form.value.workAgeTo = parseInt(max)
}

async function handleTotalTalentNextPage() {
  status.spinning = true
  const itpQueryDto = await translateToItpQueryDto(form.value)
  itpQueryDto.last_rank = lastSearchRank

  try {
    const requestList = [
      liepin.nextPage(),
      maimai.nextPage(),
      boss.nextPage(),
      smartdeer.nextPage(),
      itpTalent.nextPage(),
      linkedin.nextPage(),
    ]
    const [lieDataDto, maiDataDto, bossDataDto, smartdeerDataDto, itpDataDto, lingDataDto] = await Promise.all(requestList)

    const listQueryParams = {
      lieDataDto, maiDataDto, smartdeerDataDto, bossDataDto, lingDataDto,
      queryDto: itpQueryDto
    }

    const aggregateListData = await getMultiSearchTalentList(listQueryParams)
    lastSearchRank = aggregateListData.data.maxItpTalentRank
    totalTalentList.value = aggregateListData.data.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

// 这个变量用于记录”全部聚合“ TAB的数据转换返回的最后的页码。
let lastSearchRank = 0
const translateToItpQueryDto = async (searchParams: UnionSearchParams) => {
  const cities = searchParams.areaIds.map(item => {
    const area = AreaMap.get(item)
    return area.name
  })

  return {
    search_id: new Date().getTime() + '',
    last_rank: 0,
    search_count: searchParams.searchCount,
    position_keywords: searchParams.keywords,
    position_keywords_any: searchParams.positionKeywordsAny,
    fields: searchParams.fields,
    work_age_from: searchParams.workAgeFrom || 0,
    work_age_to: searchParams.workAgeTo || 0,
    overseas_work: searchParams.overseasWork,
    only_last_position: searchParams.companyPeriod === 1,
    cities: cities,
    companies: searchParams.companies,
    only_last_company: searchParams.companyPeriod === 1,
    schools: searchParams.schools,
    education_degree: searchParams.educationDegree,
    major: searchParams.major,
    is_211: searchParams.is211,
    is_985: searchParams.is985,
    national_unified: !!searchParams.nationalUnified,
    age_from: searchParams.ageFrom || 0,
    age_to: searchParams.ageTo || 0,
    phone: searchParams.phone,
    email: searchParams.email,
    name: searchParams.name,
    gender: searchParams.gender
  }
}

async function handleSelectRelateKeywords(keyword: string) {
  if (form.value.keywords.includes(keyword)) {
    form.value.keywords = form.value.keywords.filter(item => item !== keyword)
  } else {
    form.value.keywords.push(keyword)
  }
}

async function handleIndustryChange(value: number) {
  if (!value) {
    form.value.fields = []
    form.value.industryIds = []
    return
  }

  const industry = IndustryMap.get(value)
  form.value.industryIds = [industry.id]
  form.value.fields = [industry.industryName]
}

async function handleSearch(type: any = '') {
  status.spinning = true
  try {
    const res = await unionSearchTranslateParams(form.value)

    const itpParamsJson = res.data['1']
    const liepinParamsJson = JSON.parse(res.data['2'])
    const maimaiParamsJson = JSON.parse(res.data['3'])
    const linkedinParamsJson = JSON.parse(res.data['4'])
    const bossParamsJson = JSON.parse(res.data['5'])
    const globalTalentParamsJson = JSON.parse(res.data['7'])

    const itpQueryDto = await translateToItpQueryDto(form.value)
    itpQueryDto.last_rank = lastSearchRank

    const requestList = [
      liepin.fetch(liepinParamsJson, itpQueryDto),
      maimai.fetch(maimaiParamsJson, itpQueryDto),
      boss.fetch(bossParamsJson, itpQueryDto),
      smartdeer.fetch(itpQueryDto),
      itpTalent.fetch(itpQueryDto),
      linkedin.fetch(linkedinParamsJson, itpQueryDto),
      globalTalent.fetch(globalTalentParamsJson)
    ]
    const [lieDataDto, maiDataDto, bossDataDto, smartdeerDataDto, itpDataDto, lingDataDto, globalTalentDataDto] = await Promise.all(requestList)

    const listQueryParams = {
      lieDataDto, maiDataDto, smartdeerDataDto, bossDataDto, lingDataDto, globalTalentDataDto,
      queryDto: itpQueryDto
    }

    const aggregateListData = await getMultiSearchTalentList(listQueryParams)
    lastSearchRank = aggregateListData.data.maxItpTalentRank
    totalTalentList.value = aggregateListData.data.data

  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

function handleTabChange(tabKey: string) {
  const formItemList = UNAVAILABLE_FORM_ITEM_MAP.get(tabKey)
  unavailableFormItems.value = formItemList
  tracker.click('web-talent-search-tab-click', { tab: tabKey })
}

const handleGoLogin = () => {
  let url = ''
  switch (activeTab.value) {
    case 'liepin':
      url = 'https://h.liepin.com/account/login'
      break;
    case 'maimai':
      url = 'https://maimai.cn/platform/login'
      break;
    case 'linkedin':
      url = 'https://www.linkedin.cn/login'
      break;
    case 'boss':
      url = 'https://www.zhipin.com/web/user/'
      break;
    default:
      break;
  }
  window.open(url)
}

async function handleSearchClick(type: any = '') {
  handleSearch(type)
  tracker.click('web-talent-search-click', {})
}

async function handleClickTalent(item: any) {
  status.spinning = true
  try {
    setSearchBury({ action: 1, track_id: item.trackId })

    console.log('click: ', item)
    if (isNaN(Number(item.resumeUrl))) {
      boss.beforeOpen(item.bossLid, item.bossExpectId, item.bossSecurityId)
      if (env === 'production') {
        if (item.resumeUrl.indexOf('linkedin') !== -1) {
          window.open(item.resumeUrl)
        } else {
          window.open(item.resumeUrl)
        }
      } else {
        window.open(item.resumeUrl)
      }
    } else {
      console.log(item.site)
      if (Number(item.site) === 1) {
        talentId.value = Number(item.resumeUrl)
        trackId.value = item.trackId
        status.showTalentDetail = true
      } else if (Number(item.site) === 6) {
        const res = await saveSmartDeerAppTalent(item.accountId)
        talentId.value = res.data.talent.id
        trackId.value = item.trackId
        status.showTalentDetail = true
      } else if (Number(item.site) === 7) {
        status.showGlobalTalentDetail = true
        globalTalentId.value = item.id
      } else {
        talentId.value = Number(item.resumeUrl)
        trackId.value = item.trackId
        status.showTalentDetail = true
      }
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

const chatUsers = reactive({
  from: {}, to: {}
})

const FROM_USER = 'smart:0:smartdeer'
function handleChat(props: any) {
  chatUsers.to = {
    imUserId: props.imUserId,
    nick: props.name,
    avatar: props.avatar
  }
  chatUsers.from = {
    imUserId: FROM_USER,
    nick: 'Jobs（乔布斯）',
    avatar: 'https://global-image.smartdeer.work/p/images/0x47b67a41f6324d2ea85c03270fe0064d.jpeg_median'
  }
  status.showChatBox = true
}

const handleRefreshPage = () => {
  location.reload()
}

function checkSiteLoginStatus() {
  boss.checkLogin()
  maimai.checkLogin()
  liepin.checkLogin()
  smartdeer.checkLogin()
  linkedin.checkLogin()
  itpTalent.checkLogin()
  globalTalent.checkLogin()
}

const searchCompany = async (keyword: string) => {
  const res = await unionSearchParamsCompany(keyword)
  options.companies = []
  for (let i = 0; i < res.data.list.length; i++) {
    options.companies.push({
      label: res.data.list[i],
      value: res.data.list[i]
    })
  }
}

const handleChangeCompany = async () => {
  const companyNames = options.companies.map(company => company.label)
  form.value.companyJoined = companyNames.join(',')
}

const searchSchool = async (keyword: string) => {
  const res = await unionSearchParamsSchool(keyword)

  options.schools = []
  for (let i = 0; i < res.data.list.length; i++) {
    options.schools.push({
      label: res.data.list[i].key,
      value: res.data.list[i].value
    })
  }
}

const handleChangeSchool = async () => {
  form.value.schools = options.schools.map(school => school.label)
}

const searchMajor = async (keyword: string) => {

}

const handleChangeMajor = () => {

}

const searchArea = async () => {

}

const handleChangeArea = () => {

}

const searchable = computed(() => {
  return !(form.value.name || form.value.email || form.value.phone || form.value.keywords.length > 0)
})

onMounted(async () => {
  checkSiteLoginStatus()
  initDict()
  if (!props.jobId) return
  await initJobSearchParams(props.jobId)
  status.showAIChatBox = true
})

watch(()=>props.jobId, async (value) => {
  if (!value) return
  await initJobSearchParams(value)
  setTimeout(handleSearch, 1000)
})

</script>
<template lang="pug">
mixin page-no-login
  .no-login
    div
      img(src="@/assets/noLogin.png")
    div 请先登录招聘网站，
      a-button(type="link" @click="handleGoLogin") 点此登录
    div 若已登录，
      a-button(type="link" @click="handleRefreshPage") 请刷新

mixin talent-filter
  .talent-filter
    a-row.keyword-search(:gutter="[16, 16]")
      a-col(:span="24")
        a-input-group(compact)
          a-select(
            placeholder="请输入输入关键词"
            mode="tags"
            :token-separators="[',','，', '\t']",
            :options="options.keywords"
            style="width: 80%"
            v-model:value="form.keywords",
            size="large"
          )

          a-button(
            type="primary"
            :loading="status.spinning" 
            @click="handleSearchClick('remake')"
            size="large"
            style="width: 20%"
            :disabled="searchable"
          ) 搜索
            template(#icon)
              SearchOutlined
      a-col(:span="24" v-if="options.relateKeywords.length > 0")
        a-space(:size="[0, 8]" wrap)
          span(style="white-space:nowrap;") 相关关键字：
          a-tag(v-for="(item, index) in options.relateKeywords" :key="index" style="cursor:pointer" @click="()=> handleSelectRelateKeywords(item)" :color="form.keywords.includes(item) ? 'orange': ''") {{ item }}

    a-divider

    a-row.keyword-search(:gutter="[16, 8]" align="middle")
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('age')}")
          label 年龄
          a-space
            a-input-number(placeholder="最小" v-model:value="form.ageFrom"  style="width: 100px")
              template(#addonAfter)
                span 岁
            span -
            a-input-number(placeholder="最大" v-model:value="form.ageTo"  style="width: 100px")
              template(#addonAfter)
                span 岁

      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('gender')}")
          label 性别
          a-radio-group(v-model:value="form.gender")
            a-radio(:value="0") 不限
            a-radio(:value="1") 男
            a-radio(:value="2") 女

    a-row.keyword-search(:gutter="[16, 8]" align="middle")
      a-col(:span="8")
        .talent-filter-item
          label 工作年限
          a-select(
            v-model:value="form.yearsOfWork"
            :options="options.workYear"
            style="width: 100%"
            placeholder="请选择工作年限（工作经验）"
            @change="handleWorkYearChange"
          )

      a-col(:span="8")
        .talent-filter-item
          label 自定义
          a-space
            a-input-number(
              v-model:value="form.workAgeFrom" 
              addon-after="年" 
              style="width: 100px"
              placeholder="最小"
            )
            span -
            a-input-number(
              v-model:value="form.workAgeTo"
              addon-after="年"
              style="width: 100px"
              placeholder="最大"
            )

      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('overseasWork')}")
          a-checkbox(v-model:checked="form.overseasWork") 海外工作经验

    a-row.keyword-search(:gutter="[16, 8]" align="middle")
      a-col(:span="8")
        .talent-filter-item
          label 学历
          a-input-group(compact)

            a-select(
              v-model:value="form.educationDegree"
              :options="dict.degree"
              style="width: 60%"
            )

            //- 统招选择。
            a-select(
              v-model:value="form.nationalUnified"
              :options="options.nationalUnified"
              style="width: 40%"
            )
      a-col(:span="8")
        .talent-filter-item
          label 学校要求
          a-select(
            v-model:value="form.schoolLevel"
            style="width: 100%"
            mode="multiple"
            allow-clear
            :options="options.schoolLevel"
            @change="handleSchoolLevelChange"
            placeholder="请选择学校要求"
          )

      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('schools')}")
          label 学校
          a-select(
            style="width:100%;"
            v-model:value="form.schoolIds"
            mode="multiple"
            :filter-option="false"
            :options="options.schools"
            placeholder="学校名称"
            show-search
            @search="searchSchool"
            @change="handleChangeSchool"
          )

    a-row.keyword-search(:gutter="[16, 8]" align="middle" wrap)
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('company')}")
          label 公司

          a-input-group(compact)
            a-select(
              v-model:value="form.companies"
              style="width: 100%"
              mode="multiple"
              :filter-option="false"
              :options="options.companies"
              placeholder="公司名称"
              show-search
              @search="searchCompany"
              @change="handleChangeCompany"
            )
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('positions')}")
          label 职位

          a-input-group(compact)
            a-select(
              v-model:value="form.positionName"
              style="width: 100%"
              mode="tags"
              :options="options.companies"
              placeholder="职位名称"
            )
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('industry')}")
          label 当前行业
          a-tree-select(
            placeholder="请选择所属行业",
            v-model:value="form.industryIds",
            :tree-data="dict.industry",
            style="width: 100%"
            :filterTreeNode="true"
            treeNodeFilterProp="label"
            @change="handleIndustryChange"
            show-search
            allowClear
          )
    a-row.keyword-search(:gutter="[16, 8]" align="middle" wrap)
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('currentCity')}")
          label 目前城市
          a-tree-select(
            style="width:100%",
            placeholder="请在下拉列表中选择城市",
            v-model:value="form.areaIds",
            :fieldNames="{ label: 'title', value: 'id' }",
            treeNodeFilterProp="title",
            show-search,
            allow-clear
            multiple
            :tree-data="dict.city",
          )
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('expectCity')}")
          label 期望城市

          a-tree-select(
            style="width:100%",
            placeholder="请在下拉列表中选择城市",
            v-model:value="form.demandAreaIds",
            :fieldNames="{ label: 'title', value: 'id' }",
            treeNodeFilterProp="title",
            allow-clear
            multiple
            show-search,
            :tree-data="dict.city",
          )
    a-divider 
      span(style="color:#999; font-size:12px;") 更多选项

    a-row.keyword-search(:gutter="[16, 8]")
      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('name')}")
          label 姓名
          a-input(v-model:value="form.name" placeholder="请输入姓名")

      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('phone')}")
          label 手机
          a-input(v-model:value="form.phone" placeholder="手机号码")

      a-col(:span="8")
        .talent-filter-item(:class="{'no-available': unavailableFormItems.includes('email')}")
          label 邮箱
          a-input(v-model:value="form.email" placeholder="邮箱")

.talent-web-search
  a-spin(:spinning="status.initDict")
    +talent-filter

  .talent-list
    a-tabs(v-model:activeKey="activeTab" size="large" @change="handleTabChange")
      a-tab-pane(key="all") 
        a-spin(:spinning="status.spinning")
          .talent-search(v-if="totalTalentList.length")
            TalentSearchItem(
              v-for="(item, index) in totalTalentList"
              :key="index"
              :item="item"
              :degree="dict.degreeMap"
              @click="handleClickTalent(item)"
              @open="handleClickTalent",
              @chat="() => handleChat(item)"
            )
            .search-pagination
              a-button(@click="handleTotalTalentNextPage" type="primary") 下一页
          a-empty(v-else)

        template(#tab)
          a-space
            span 全部聚合
            LoadingOutlined(v-if="status.spinning")

      a-tab-pane(key="itp") 
        a-spin(:spinning="itpTalent.loading")
          .talent-search(v-if="itpTalent.list.length")
            TalentSearchItem(
              v-for="(item, index) in itpTalent.list"
              :key="item.trackId"
              :item="item"
              :degree="dict.degreeMap"
              @click="handleClickTalent(item)"
              @open="handleClickTalent"
              @chat="() => handleChat(item)"
            )
            .search-pagination
              //- a-pagination(:total="itpTalent.total")
              a-button(@click="itpTalent.nextPage()" type="primary") 下一页
          a-empty(v-else)

        template(#tab)
          a-space
            span ITP人才库
            template(v-if="itpTalent.isLogin")
              LoadingOutlined(v-if="itpTalent.loading")
              .count(v-else) ({{ itpTalent.total }})
            .login(v-else) （请登录）
      a-tab-pane(key="globalTalent")
        a-spin(:spinning="globalTalent.loading")
          .talent-search(v-if="globalTalent.list.length")
            TalentGlobalSearchItem(
              v-for="(item, index) in globalTalent.list"
              :key="item.trackId"
              :item="item"
              :degree="dict.degreeMap"
              @click="handleClickTalent(item)"
              @open="handleClickTalent"
            )
            .search-pagination
              a-button(@click="globalTalent.nextPage()" type="primary") 下一页
          a-empty(v-else)

        template(#tab)
          a-space
            span 新版人才库
            template(v-if="globalTalent.isLogin")
              LoadingOutlined(v-if="globalTalent.loading")
              .count(v-else) ({{ globalTalent.total }})
            .login(v-else) （请登录）
      a-tab-pane(key="liepin") 
        template(v-if="!liepin.isLogin")
          +page-no-login
        template(v-else)
          a-spin(:spinning="liepin.loading")
            .talent-search(v-if="liepin.list.length")
              TalentSearchItem(
                v-for="(item, index) in liepin.list"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-space(:size="12")
                  a-button(@click="liepin.prevPage()" type="primary" :disabled="liepin.page === 0") 上一页
                  a-button(@click="liepin.nextPage()" type="primary") 下一页
            a-empty(v-else)

        template(#tab)
          a-space
            span 猎聘
            template(v-if="liepin.isLogin")
              LoadingOutlined(v-if="liepin.loading")
              .count(v-else) ({{ liepin.total === 10000 ? '10000+' : liepin.total }})
            .login(v-else) （请登录）

      a-tab-pane(key="maimai") 
        template(v-if="!maimai.isLogin")
          +page-no-login
        template(v-else)
          a-spin(:spinning="maimai.loading")
            .talent-search(v-if="maimai.list.length")
              TalentSearchItem(
                v-for="(item, index) in maimai.list"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent"
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-space(:size="12")
                  a-button(@click="maimai.prevPage()" type="primary" :disabled="maimai.page === 0") 上一页
                  a-button(@click="maimai.nextPage()" type="primary" ) 下一页

            a-empty(v-else)

        template(#tab)
          a-space()
            span 脉脉
            template(v-if="maimai.isLogin")
              LoadingOutlined(v-if="maimai.loading")
              .count(v-else) ({{ maimai.total === 1000 ? '1000+' : maimai.total }})
            .login(v-else) （请登录）

      a-tab-pane(key="linkedin") 
        template(v-if="false")
          +page-no-login
        template(v-else)
          a-spin(:spinning="linkedin.loading")
            .talent-search(v-if="linkedin.list.length")
              TalentSearchItem(
                v-for="(item, index) in linkedin.list"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent"
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-space(:size="12")
                  a-button(@click="linkedin.prevPage()" type="primary" :disabled="linkedin.page === 0") 上一页
                  a-button(@click="linkedin.nextPage()" type="primary" ) 下一页

            a-empty(v-else)
        template(#tab)
          a-space
            span 领英
            template(v-if="linkedin.isLogin")
              LoadingOutlined(v-if="linkedin.loading")
              .count(v-else) ({{ linkedin.total === 1000 ? '1000+' : linkedin.total }})
            .login(v-else) （请登录）

      a-tab-pane(key="boss")
        template(v-if="!boss.isLogin")
          +page-no-login
        template(v-else)
          a-spin(:spinning="boss.loading")
            .talent-search(v-if="boss.list.length")
              TalentSearchItem(
                v-for="(item, index) in boss.list"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent"
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-space(:size="12")
                  //- a-pagination(:total="boss.total")
                  a-button(@click="boss.prevPage()" type="primary" :disabled="boss.page === 0") 上一页
                  a-button(@click="boss.nextPage()" type="primary") 下一页

            a-empty(v-else)

        template(#tab)
          a-space()
            span Boss直聘
            template(v-if="boss.isLogin")
              LoadingOutlined(v-if="boss.loading")
              template(v-else)
                template(v-if="boss.error")
                  a-tooltip
                    ExclamationCircleFilled(style="color: #F9470D;")
                    template(#title)
                      span {{ boss.error }}
                template(v-else)
                  .count() ({{ boss.total }})
            template(v-else)
              .login 请登录

      a-tab-pane(key="smartdeer")
        template(v-if="!smartdeer.isLogin")
          +page-no-login
        template(v-else)
          a-spin(:spinning="smartdeer.loading")
            .talent-search(v-if="smartdeer.list.length")
              TalentSearchItem(
                v-for="(item, index) in smartdeer.list"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent"
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-space(:size="12")
                  a-button(@click="smartdeer.prevPage()" type="primary" :disabled="smartdeer.page === 0") 上一页
                  a-button(@click="smartdeer.nextPage()" type="primary") 下一页
            a-empty(v-else)

        template(#tab)
          a-space()
            span SmartDeer
            LoadingOutlined(v-if="smartdeer.loading")
            .count(v-else) ({{ smartdeer.total }})

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" :jobId="props.jobId" :trackId="trackId" @close="status.showTalentDetail = false")

  a-modal(v-model:open="status.showGlobalTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentGlobalDetailModal(:talentId="globalTalentId" :jobId="props.jobId" :trackId="trackId" @close="status.showGlobalTalentDetail = false")

  a-drawer(v-model:open="status.showChatBox" :destroyOnClose="true" :width="480" title="聊天记录" :bodyStyle="{padding: 0}")
    ChatBox(:from="chatUsers.from" :to="chatUsers.to")
</template>

<style lang="scss" scoped>
.talent-web-search {
  width: 100%;
  height: 100%;
  padding-bottom: 32px;
}

.keyword-search {
  margin-bottom: 8px;
}

.talent-filter {
  background: #fff;
  padding: 20px 20px 4px;
  margin-bottom: 20px;
  border-radius: 8px;
  opacity: 100%;

  .talent-filter-item {
    display: flex;
    align-items: center;
    height: 100%;
    transition: all .3s;

    label {
      white-space: nowrap;
      margin-right: 8px;
      width: 65px;
      min-width: 65px;
      color: #777;
    }
  }
}

.no-login {
  text-align: center;
  padding-bottom: 80px;

  img {
    margin: 0 auto;
  }
}

.talent-list {
  border-radius: 8px;
  background: #fff;
  padding: 0 20px;

  .talent-search {
    .search-pagination {
      position: sticky;
      bottom: 0;
      background-color: #fff;
      padding: 16px;
      text-align: center;
    }
  }
}

.no-available {
  opacity: 20%;
  transition: all .3s
}
</style>