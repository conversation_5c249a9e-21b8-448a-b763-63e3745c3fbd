<template lang="pug">
mixin page-header
  a-page-header(title="编辑人才报告", @back="()=>{$router.go(-1)}", style="padding: 0 0 8px;")

.report-editor-page
  +page-header
  a-row(:gutter="[16, 16]")
  a-spin(:spinning="status.loading")
    ReportEditor(
      ref="editorInstance"
      :talentId="talentId"
      :report="currentReport"
      :jobRequirementId="jobRequirementId"
      @save="handleSaveReport"
      @export="handleExportReport"
      v-if="!status.loading"
    )
</template>

<script lang="ts" setup>
import { attachReportToTalentJob, getTalentDetail, getTalentReports, getAIConsultantRecommendation } from '@/api/talent/talent'
import { message } from 'ant-design-vue'
import { onActivated, toRef, nextTick, ref, onMounted } from 'vue'
import { saveTalentReportToFile } from '@/api/talent/talent'
import { reactive } from 'vue'
import { getJobRequirementDetail } from '@/api/position'
import ReportEditor from '@/components/app/report-editor/report-editor.vue'
import 'quill/dist/quill.bubble.css'

const props = defineProps<{
  talentId: number,
  processInstanceId: string,
  jobRequirementId: number
}>()

const talentId = toRef(props, 'talentId')
const processInstanceId = toRef(props, 'processInstanceId')
const jobRequirementId = toRef(props, 'jobRequirementId')

const status = reactive({
  loading: true,
  talentLoading: false,
  jobLoading: false
})

const talentDetail = ref<any>({})
const jobDetail = ref<any>({})
const reportData = ref<any>({})
const reportInfoList = ref<any>([])

async function fetchTalentDetail(talentId: number) {
  status.talentLoading = true
  try {
    const res = await getTalentDetail(talentId)
    talentDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.talentLoading = false
}

async function fetchJobDetail(jobId: number) {
  try {
    const res = await getJobRequirementDetail(jobId)
    jobDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const editorInstance = ref<any>()

async function handleExportReport() {
  const fileInfo = await saveReport()
  window.open(fileInfo.fileAbsolutePath)
}

async function handleSaveReport() {
  const fileInfo = await saveReport()
  message.success('保存成功')
}

async function saveReport() {
  const config = await editorInstance.value.getConfig()
  const html = await editorInstance.value.getHtml()

  try {
    const res = await saveTalentReportToFile(html)
    const result = await attachReportToTalentJob({
      talentId: talentId.value,
      jobRequirementId: jobRequirementId.value,
      processInstanceId: processInstanceId.value,
      htmlContent: html,
      jsonContent: JSON.stringify(config),
      fileId: res.data.id
    })
    return res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const reports = ref<any[]>([])
const currentReport = ref<any>({})
async function fetchTalentReports(talentId: number) {
  try {
    const res = await getTalentReports(talentId)
    reports.value = res.data.talentReportItems
    currentReport.value = res.data.talentReportItems.find((item:any) => item.processInstanceId === processInstanceId.value)
  } catch (err: any) {
    message.error(err.message)
  }
}

onActivated(async () => {
  status.loading = true
  await fetchTalentDetail(talentId.value)
  await fetchTalentReports(talentId.value)
  status.loading = false
})

</script>

<style lang="sass" scoped>
.report-editor-page
  position: relative
  z-index: 1
</style>