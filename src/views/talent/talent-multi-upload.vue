<template lang="pug">
mixin resume-section
  .resume-preview(v-if="resumeFile.fileAbsolutePath")
    //- VuePdfEmbed.pdf-resume(:source="resumeFile.fileAbsolutePath" :disableTextLayer="true")
    FilePreViewer(:url="resumeFile.fileAbsolutePath" :mime="resumeFile.mimeType")
  .resume-upload(v-else)
    //- :before-upload="beforeUpload",
    a-upload-dragger(
      name="file",
      :multiple="false",
      :showUploadList="false",
      :action="getBatchUploadResumeUrl",
      :headers="uploadRequestInfo.headers",
      @change="handleResumeUpload"
    )
      p.upload-drag-icon
        ContainerOutlined
      p.upload-drag-text 点击或将文件拖拽到这里上传
      p.upload-drag-hint 支持{{ supportExtension.join('、') }}格式，单个简历大小不超过100MB。

mixin form-section
  a-form(:model="talentForm", label-align="left", ref="talentFormInstance", :scrollToFirstError="true", layout="vertical")
    a-form-item(
      label="人才渠道",
      name=['talent', 'sourceType'],
      :rules="[{ required: false, message: '人才渠道不能为空', trigger: ['change'] }]"
    )
      a-select(v-model:value="talentForm.talent.sourceType" :options="dict.talentSource" placeholder="请选择人才来源的渠道")


.talent-edit-page
  a-page-header( title="批量导入人才", sub-title="", @back="()=>{$router.go(-1)}", style="padding: 0 0 8px;" )
    template(#extra)
      a-button(type="primary" @click="reset()" v-if="resumeFile.fileAbsolutePath") 重新上传

  a-spin(:spinning="status.loading")
    .talent-edit-body(v-if="!status.showSuccess")
      a-row(:gutter="[24, 0]")
        a-col(:span="12")
          +resume-section
        a-col.main-form(:span="12")
          +form-section
    a-result(v-else title="人才创建成功！" status="success")
      template(#extra)
        a-button(type="primary" @click="() => { $router.go(-1) }") 返回
        a-button(type="primary" ghost @click="() => { $router.push('/talent/list/me') }") 我的人才
        a-button(type="primary" ghost @click="() => { $router.push(`/talent/${talentForm.talent.id}/detail`) }") 查看人才详情
        a-button(type="primary" ghost @click="reset()") 继续添加人才
</template>

<script lang="ts" setup>
import VuePdfEmbed from 'vue-pdf-embed'
import dayjs from 'dayjs'
import querystring from 'query-string'

import { onMounted, reactive, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { getTalentDetail, createOrUpdateTalent, checkTalentDuplicate, getMergedTalent } from '@/api/talent/talent'
import {
  getCompanyTypeList, getCompanyScaleList,
  getAllIndustryList, getAllFunctionList, getSchoolList,
  createSchool, getSchoolKind, getSchoolLevel, getTalentSource, dictionary
} from '@/api/dictionary'
import { LoadingOutlined, PlusOutlined, MinusOutlined, ContainerOutlined, ConsoleSqlOutlined } from '@ant-design/icons-vue'
import { API_URL } from '@/api/talent/talent'
import { useUserStore } from '@/store/user.store'
import { useMemoStore } from '@/store/memo.store'
import { debounce } from '@/utils/util'
import { areaDictToTreeData, industryDictToTreeData } from '@/utils/form-data-helper'
import { createCandidateTalentResumeBind } from '@/api/marketing'
import { phoneAreaDict } from '@/utils/phone-area-dict'
import FilePreViewer from '@/components/file-previewer/file-previewer.vue'
import { QUERY_ACTION } from '@/api/job'
import type { Rule } from 'ant-design-vue/es/form';

const supportExtension = ['zip', 'rar', '7z']

const router = useRouter()
const originData = ref<any>()

const userStore = useUserStore()
const uploadRequestInfo = {
  action: API_URL.MULTI_RESUME_UPLOAD,
  headers: {
    Authorization: userStore.token
  }
}

const status = reactive({
  loading: false,
  showAddSchool: false,
  addSchoolLoading: false,
  showSuccess: false,
  hasDuplicateTalent: false,
  showDuplicateCombine: false,
  combineTalent: false,
})

const talentFormInstance = ref()
const resumeFile = ref<any>({})
const resumeFileParsedId = ref<number>()
const talentForm = ref({
  talent: {},
  talentDemand: {},
  talentEducations: [],
  talentExperiences: [],
  talentProjects: [],
  talentSkills: [],
  originResumeParsedId: 0
} as {
  talent: any,
  talentDemand: any,
  talentEducations: any[]
  talentExperiences: any[]
  talentProjects: any[]
  talentSkills: any[]
  originResumeParsedId: number
})

const dict = reactive({
  degree: [] as any[],
  companyType: [] as any[],
  companyScale: [] as any[],
  area: [] as any,
  industry: [] as any,
  function: [] as any[],
  employeeStatus: [] as any[],
  schoolKind: [] as any[],
  schoolLevel: [] as any[],
  talentSource: [] as any[],
  mobileArea: [] as any[]
})

const validatorEmailOrMobileNumber  = (_rule: Rule,  value: string) => {
  if (_rule.pattern && value) {
    if (_rule.pattern.test(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('Please input');
    }
  }
  
  if (talentForm.value.talent.email || talentForm.value.talent.mobileNumber) {
    return Promise.resolve();
  } 
 
  return Promise.reject('Please input');
}

async function initDict() {
  const dictPromiseList = [
    dictionary.getDegree(), getCompanyTypeList(), getCompanyScaleList(), dictionary.getAllAreaList(),
    getAllIndustryList(), getAllFunctionList(), dictionary.getEmployeeStatus(), getSchoolKind(),
    getSchoolLevel(), getTalentSource()
  ]

  const [
    dictDegree, dictCompanyType, dictCompanyScale, dictArea,
    dictIndustry, dictFunction, dictEmployeeStatus, dictSchoolKindRes,
    dictSchoolLevelRes, dictTalentSource
  ] = await Promise.all(dictPromiseList)

  for (let id in dictDegree.data) {
    dict.degree.push({ value: Number(id), label: dictDegree.data[id] })
  }

  dict.talentSource = Object.entries(dictTalentSource.data).map((item, index) => {
    const [value, label] = item
    return { value: Number(value), label }
  })

  const [areaDictTreeData] = areaDictToTreeData(dictArea.data)
  dict.area = areaDictTreeData

  const [industryDictTreeData] = industryDictToTreeData(dictIndustry.data)
  dict.industry = industryDictTreeData

  dict.mobileArea = phoneAreaDict.map((item: any, index: number) => {
    return { label: `${item.number} ${item.areaNameCN}`, value: item.number }
  })

  dict.function = dictFunction.data.map((item: any, index: number) => {
    return { value: item.id, label: item.name }
  })

  dict.employeeStatus = dictEmployeeStatus.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })

  dict.schoolLevel = dictSchoolLevelRes.data.map((item: any, index: number) => {
    return { value: item.id, label: item.title }
  })
  dict.schoolKind = dictSchoolKindRes.data
  dict.companyScale = dictCompanyType.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })
  dict.companyType = dictCompanyScale.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })
}

function getBatchUploadResumeUrl () {
  if (talentForm.value.talent.sourceType === undefined) {
    return uploadRequestInfo.action
  }
  return uploadRequestInfo.action + "?sourceType=" + talentForm.value.talent.sourceType
}

function handleBirthdayChange(value: any) {
  if (value) talentForm.value.talent.birthday = value.format('YYYY-MM-DD')
  else talentForm.value.talent.birthday = value
}

function handleStartDateChange(target: any, value: any) {
  if (value) target.fromDate = value.format('YYYY-MM-DD')
  else target.fromDate = value
}

function handleEndDateChange(target: any, value: any) {
  if (value) target.toDate = value.format('YYYY-MM-DD')
  else target.toDate = value
}

const schoolList = ref<any[]>([])
function getTalentEducationSchoolOption() {
  const schoolSearchList = [] as any[]
  talentForm.value.talentEducations.forEach((item: any, index: number) => {
    schoolSearchList.push({
      value: item.schoolId,
      label: item.schoolName
    })
  })
  schoolList.value = schoolSearchList
}

function getDictSchoolList(keyword: string) {
  getSchoolList(keyword, 1, 20).then((res) => {
    const schoolSearchList = [] as any[]
    res.data.forEach((schoolItem: any) => {
      schoolSearchList.push({
        value: schoolItem.id,
        label: schoolItem.name
      })
    })
    schoolList.value = schoolSearchList
  })
}

const debouncedSchoolSearch = debounce(getDictSchoolList)

function addEducation() {
  talentForm.value.talentEducations.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    schoolId: null,
    schoolName: '',
    major: '',
    majorDesc: '',
    degree: null,
    isUnified: 0
  })
}

function addProject() {
  talentForm.value.talentProjects.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    companyName: '',
    fromDate: dayjs(),
    toDate: null,
    lastModifyTime: 0,
    createBy: 0,
    lastUpdatedBy: 0,
    projectName: '',
    projectDesc: '',
    duties: '',
    talentTitle: ''
  })
}

function addExperience() {
  talentForm.value.talentExperiences.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    companyName: '',
    companyInfo: '',
    companyScale: 0,
    companyType: 0,
    industryCode: '',
    isManager: 0,
    jobDesc: '',
    juniorNumber: 0,
    position: '',
    reporter: '',
    duties: ''
  })
}

function addSkill() {
  talentForm.value.talentSkills.unshift({
    id: null,
    talentId: talentForm.value.talent.id,
    skillType: '',
    timeOfUse: '',
    competencyLevel: '',
  })
}

function delEducation(index: number) {
  talentForm.value.talentEducations.splice(index, 1)
}

function delExperience(index: number) {
  talentForm.value.talentExperiences.splice(index, 1)
}

function delSkill(index: number) {
  talentForm.value.talentSkills.splice(index, 1)
}

function delProject(index: number) {
  talentForm.value.talentProjects.splice(index, 1)
}

function formatDatePickerValue(data: any) {
  if (data) return dayjs(data)
  else return null
}

function handleSingleCheckBoxChange(value: any, type: string, index: number) {
  const checkedValue = value[0] ? 1 : 0
  switch (type) {
    case 'ext_medical':
      talentForm.value.talentDemand.currentExtMedical = checkedValue
      break
    case 'catastrophic_medical':
      talentForm.value.talentDemand.currentCatastrophicMedical = checkedValue
      break
    case 'edu_unified':
      talentForm.value.talentEducations[index].isUnified = checkedValue
      break
    case 'exp_manage':
      talentForm.value.talentExperiences[index].isManager = checkedValue
      break
  }
}

async function checkDuplicate() {
  try {
    const res = await checkTalentDuplicate({
      idCardNumber: '',
      realName: talentForm.value.talent.realName || '',
      mobileNumber: talentForm.value.talent.mobileNumber || '',
      email: talentForm.value.talent.email || ''
    })

    // 如果检查时，返回的是一样的结果，则表示不检查
    if (duplicateTalents.value.join() == res.data.join()) {
      return
    } else {
      duplicateTalents.value = res.data
      if (duplicateTalents.value.length) {
        status.hasDuplicateTalent = true
      } else {
        status.hasDuplicateTalent = false
      }
    }
  } catch (err) {
    return 
  }
}

const route = useRoute()

async function submit() {
  status.loading = true
  try {
    // 表单验证
    await talentFormInstance.value?.validate()
    const res = await createOrUpdateTalent(talentForm.value)
    talentForm.value.talent.id = res.data

    // 清除区别对比
    originData.value = JSON.stringify(talentForm.value)

    // 绑定人才到candidate
    const candidateId = Number(route.query.candidate_id)
    if (candidateId) await createCandidateTalentResumeBind({ candidateId: candidateId, talentId: res.data, resumeOriginParsedId: talentForm.value.originResumeParsedId })

    status.showSuccess = true
  } catch (err: any) {
    if (err.errorFields) {
      const firstError = err.errorFields[0]
      message.error(firstError.errors.join(','))
      talentFormInstance.value?.scrollToField(firstError.name, { behavior: 'smooth', block: 'center' })
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

const addSchoolForm = reactive({
  areaId: null,
  ename: '',
  name: '',
  kinds: [],
  level: null,
})

let targetSchoolItem: any

function showAddShool(target: any) {
  targetSchoolItem = target
  status.showAddSchool = true
}

async function addSchool() {
  status.addSchoolLoading = true
  try {
    let kind = 0
    addSchoolForm.kinds.forEach((item, index) => { kind += item })
    const res = await createSchool({
      areaId: addSchoolForm.areaId,
      ename: addSchoolForm.ename,
      name: addSchoolForm.name,
      kind: kind,
      level: addSchoolForm.level
    })
    targetSchoolItem.schoolId = res.data
    targetSchoolItem.schoolName = addSchoolForm.name
    schoolList.value = [{ value: res.data, label: addSchoolForm.name }]
    status.showAddSchool = false
  } catch (err: any) {
    message.error(err.message)
  }
  status.addSchoolLoading = false
}

function talentDataProcess(talent: any) {
  talentForm.value = talent

  if (!talent.talent.mobileArea) talentForm.value.talent.mobileArea = '+86'
  if (talent.talent.gender === 0) talentForm.value.talent.gender = null

  talent.talentEducations.forEach((item: any, index: Number) => {
    schoolList.value.push({ label: item.schoolName, value: item.schoolId })
  })

  if (resumeFileParsedId.value) talentForm.value.originResumeParsedId = resumeFileParsedId.value
}

const duplicateTalents = ref<any[]>([])
async function handleResumeUpload(info: any) {
  const uploadStatus = info.file.status
  if (uploadStatus === 'uploading') {
    status.loading = true
  }

  if (uploadStatus === 'done') {
    status.loading = false
    message.success(`文件${info.file.name}上传成功，解析完成后会以站内信的形式通知您。`)
  }

  if (uploadStatus === 'error') {
    status.loading = false

    message.error(`抱歉，文件${info.file.name}上传失败，请稍后重试。`)
  }
}

async function handleDuplicateTalentCheck() {
  // duplicateTalents.value = 
  await checkDuplicate()
  // if (duplicateTalents.value.length) {
  //   status.hasDuplicateTalent = true
  // } else {
  //   status.hasDuplicateTalent = false
  // }
}

const targetCombineTalent = ref({
  talent: {} as any
})

function getTalentSummary(talent: any) {
  const result = []
  result.push([talent.talent.realName, talent.talent.mobileNumber, talent.talent.email])

  const firstEducationInfo = Array.isArray(talent.talentEducations) ? talent.talentEducations[0] : null
  if (firstEducationInfo) result.push([
    firstEducationInfo.schoolName, firstEducationInfo.major,
    `${dayjs(firstEducationInfo.fromDate).format('YYYY.MM')} - ${dayjs(firstEducationInfo.toDate).format('YYYY.MM')}`
  ])

  Array.isArray(talent.talentExperiences) && talent.talentExperiences.forEach((item: any, index: number) => {
    result.push([
      item.companyName, item.position,
      `${dayjs(item.fromDate).format('YYYY.MM')} - ${dayjs(item.toDate).format('YYYY.MM')}`
    ])
  })
  return result
}

async function combineTalents() {
  status.combineTalent = true
  try {
    const res = await getMergedTalent(targetCombineTalent.value.talent.id, talentForm.value)
    talentDataProcess(res.data)
    message.success('合并后的人才信息已填入表单')
  } catch (err: any) {
    message.error(err.message)
  }
  status.hasDuplicateTalent = false
  status.showDuplicateCombine = false
  status.combineTalent = false
}

function reset() {
  status.loading = false
  status.showAddSchool = false
  status.addSchoolLoading = false
  status.showSuccess = false
  status.hasDuplicateTalent = false
  status.showDuplicateCombine = false
  status.combineTalent = false

  resumeFile.value = {}

  talentFormInstance.value?.clearValidate()
  talentForm.value = {
    talent: {},
    talentDemand: {},
    talentEducations: [],
    talentExperiences: [],
    talentProjects: [],
    talentSkills: [],
    originResumeParsedId: 0
  }

  duplicateTalents.value = []
}

onBeforeRouteLeave((to, from, next) => {
  if (originData.value !== JSON.stringify(talentForm.value)) {
    Modal.confirm({
      title: '确认要离开？',
      content: '如果离开，未保存的数据将会丢失。',
      onOk() { 
        next(true) 
      },
      onCancel() { next(false) }
    })
  } else {
    next(true)
  }
})

onMounted(() => {
  initDict()
})

const memoStore = useMemoStore()
onMounted(() => {
  reset()
  const talentParseResult = memoStore.getOnce('talent-resume-parse')
  if (talentParseResult) {
    talentDataProcess(talentParseResult.talentInfo)
    resumeFile.value = talentParseResult.fileInfo

    checkDuplicate()
  }
})

function handleDemondChange() {

}
</script>

<style lang="scss" scoped>
h3 {
  margin-bottom: 12px;
}

.talent-edit-body {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 24px;
  border-radius: 8px;

  .resume-preview {
    position: sticky;
    top: 64px;
    height: calc(100vh - 84px);
    overflow: scroll;
  }

  .resume-upload {
    position: sticky;
    top: 64px;

    .upload-drag-icon {
      color: #ff9111;
      font-size: 40px;
    }

    .upload-drag-text {
      font-weight: bold;
      font-size: 18px;
    }

    .upload-drag-hint {
      padding: 0 16px;
      color: #999;
    }
  }
}

.resume-container {
  padding-right: 24px;
  height: 81vh;
  width: 100%;

  // border: 1px solid #f0f0f0;
  // overflow: scroll;
  // overflow: scroll;

  .resume-wrapper {
    width: 100%;
    height: 100%;
    border: 1px solid #f0f0f0;
    overflow-y: scroll;

    .pdf-resume {
      width: 100%;
      overflow: hidden;
    }
  }
}

.resume-form {
  margin: -24px 0;
}

.form-group {
  padding: 24px 0px 24px 16px;
  border-bottom: 1px solid #f0f0f0;

  >*:nth-child(2) {
    margin-top: 24px;
  }

  h4 {
    line-height: 56px;
    font-size: 18px;
    font-weight: bold;
    position: sticky;
    top: 64px;
    background-color: #fff;
    z-index: 1;

    &::before {
      content: "";
      display: block;
      background-color: #ff9111;
      height: 20px;
      width: 3px;
      top: 18px;
      left: -16px;
      border-radius: 2px;
      position: absolute;
    }
  }


  .right_tools {
    font-size: 14px;
    line-height: 56px;
    cursor: pointer;
    float: right;
    text-align: right;

    &:hover {
      color: #ff9111;
    }
  }
}

span.required {
  color: #ff9111;
}

.education_item,
.experience_item,
.project_item,
.skill_item {
  margin: -16px;
  padding: 16px;
  padding-bottom: 16px;
  margin-bottom: 32px;
  border-radius: 8px;
  transition: all .3s;

  &:hover {
    background-color: #FAFAFA;
    transition: all .3s;
  }
}

.date-pikcer-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.submit_panel {
  // width: 100%;
  // padding: 16px 0px;
  // text-align: right;
  // background: #fff;
  // border-top: 1px solid #f0f0f0;

  position: sticky;
  bottom: 16px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  z-index: 2;
  box-shadow: 0 2px 10px RGBA(0, 0, 0, 0.08);
}

.duplicate_panel {
  position: sticky;
  bottom: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff1df;
  border: 1px solid #ff9111;
  box-shadow: 0 2px 10px RGBA(255, 171, 17, 0.3);
  border-radius: 8px;
  z-index: 2;
}

.duplicate-talent-selector {

  .duplicate-talent {
    cursor: pointer;
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 6px;
    background-color: #FAFAFA;
    border: 1px solid #FAFAFA;

    &.selected {
      border: 1px solid #ff9111;
      background-color: #fff1df;
    }

    em {
      font-weight: bold;
      font-style: normal;
    }
  }
}
</style>  
