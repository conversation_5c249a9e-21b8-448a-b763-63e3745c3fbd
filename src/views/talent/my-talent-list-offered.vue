<template lang="pug">
mixin page-head
  .talent-page-header
    a-page-header(
      title="我的人才",
      sub-title="",
      @back="()=> {$router.go(-1)}",
      style="padding: 0 0 8px;"
    )
      template(#extra)
        a-button(type="primary" @click="() => {goto('/talent/create')}") 新增人才
          template(#icon)
            UserAddOutlined

mixin filter-section
  a-spin(:spinning="status.initFilter")
    section.talent-search-filter
      .search-input
        a-input-search(
          placeholder="按关键词搜索",
          enter-button,
          v-model:value="queryParams.keyword",
          @search="handleKeywordSearch"
        )

      a-row.search-filter(:gutter="[12, 12]")
        a-col(:span="8")
          FilterItem(label="姓名")
            a-input-search(
              placeholder="请输入姓名",
              enter-button,
              v-model:value="queryParams.realName",
              @search="handleFilterChange"
            )

        a-col(:span="8")
          FilterItem(label="学历")
            a-select(
              v-model:value="queryParams.degree",
              :options="filter.degrees",
              allow-clear,
              :dropdownMatchSelectWidth="false",
              @change="handleFilterChange"
            )

        a-col(:span="8")
          FilterItem(label="城市")
            a-select(
              v-model:value="queryParams.areaCode",
              :options="filter.areas",
              :fieldNames="{ label: 'title', value: 'areaCode' }",
              allow-clear,
              show-search
              :filter-option="filterOption"
              :dropdownMatchSelectWidth="false",
              @change="handleFilterChange"
            )

        a-col(:span="8")
          FilterItem(label="经验")
            a-select(
              v-model:value="queryParams.workYears"
              :options="filter.workYears",
              allow-clear,
              :dropdownMatchSelectWidth="false"
              @change="handleFilterChange"
            )
        a-col(:span="8")
          FilterItem(label="性别")
            a-select(
              v-model:value="queryParams.gender",
              allow-clear,
              @change="handleFilterChange"
            )
              a-select-option(:value="1") 男
              a-select-option(:value="2") 女
              a-select-option(:value="3") 保密

        a-col(:span="8")
          FilterItem(label="来源")
            a-select(
              v-model:value="queryParams.sourceType"
              :options="filter.sourceType",
              allow-clear,
              :dropdownMatchSelectWidth="false"
              @change="handleFilterChange"
            )

mixin talent-search-list
  .talent-list
    a-table(
      :pagination="pagination" 
      :data-source="searchResult" 
      :loading="status.listLoading" 
      :columns="columns"
      @change="pageChange"
    )
      template(#bodyCell="{ column, record }")
        template(v-if="column.key === 'talent'")
          TableTalentInfo(:talent="record" @click="() => showTalentDetail(record.talentBase.id)" )

        template(v-if="column.key === 'exp'")
          TableTalentExp(:talent="record")

        //- template(v-if="column.key === 'job'")
        //-   TableTalentJob(:talent="record")

    //- template(v-for="(item, i) in searchResult" :key="item.talentBase.id")
      //- TalentInfoItem(:talent="item" @click="() => showTalentDetail(item.talentBase.id)" )

  //- .talent-pagination(v-if="pagination.total > 0")
    //- a-pagination(:total="pagination.total" :current="pagination.current" :pageSize="pagination.size" @change="pageChange" )

mixin side-nav
  nav
    .nav-group
      .title 人才
      ul.list
        li(@click="handleGetTalentList('all')" :class="{active: activeSideNav == 'all'}") 全部
        li(@click="handleGetTalentList('created')" :class="{active: activeSideNav == 'created'}") 我录入的
        a-tooltip(placement="right", title="有跟进动作的，如填写followup，发送邮件。")
          li(@click="handleGetTalentList('followed')" :class="{active: activeSideNav == 'followed'}") 我跟进的

    .nav-group
      .title 流程
      ul.list
        li(@click="handleGetTalentList('recommend')" :class="{active: activeSideNav == 'recommend'}") 我推荐的
        li(@click="handleGetTalentList('interview')" :class="{active: activeSideNav == 'interview'}") 面试中的
        li(@click="handleGetTalentList('offered')" :class="{active: activeSideNav == 'offered'}") 已Offer的

.talent-list-page
  +page-head
  .list-page-laytout
    //- .side-nav
    //-   +side-nav
    //- .content
    +filter-section
    +talent-search-list

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")

</template>

<script lang="ts" setup>
import { getMyTalentList } from "@/api/talent/talent"
import { getWorkYearList, getAreaList, getTalentSource, dictionary } from "@/api/dictionary"
import { replaceBlankString, getAgeFromBirthday, getShortDate } from "@/utils/string"
import { ref, reactive, onMounted, onActivated } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { UserAddOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import { useUserStore } from "@/store/user.store"
import { areaDictToTreeData } from "@/utils/form-data-helper"
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import TableTalentInfo from "@/components/app/table-talent-info.vue"
import TableTalentExp from "@/components/app/table-talent-exp.vue"
import FilterItem from "@/components/ui/filter-item.vue"

const filterOption = (input: string, option: any) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const userStore = useUserStore()
const router = useRouter()
const activeSideNav = ref('all')

const queryParams = reactive<any>({
  companyUserId: userStore.id,
  keyword: '',
  areaId: null,
  degree: null,
  workYears: null,
  mobileNumber: '',
  sourceId: null,
  sourceType: null,
  gender: null,
  isFromRec: false,
  searchId: getSearchId(),
})

const filter = reactive({
  areas: [] as any,
  workYears: [] as any[],
  degrees: [] as any[],
  sourceType: [] as any[],
})

const status = reactive({
  initFilter: false,
  listLoading: false,
  showTalentDetail: false,
})

const columns = ref([
  { key: 'talent', title: '人才'},
  { key: 'exp', title: '履历'},
])

const searchResult = ref([])

function goto(path: string) {
  router.push(path)
}

const pagination = reactive({
  pageSize: 20,
  current: 0,
  total: 0
})

function handleGetTalentList(key: string) {
  activeSideNav.value = key
}

async function initFilter() {
  status.initFilter = true
  Promise.all([
    getWorkYearList(), getAreaList(), dictionary.getDegree(), getTalentSource()
  ]).then(([workYearList, areaList, degreeList, talentSourceList]: any[]) => {

    filter.sourceType = Object.entries(talentSourceList.data).map((item, index) => {
      const [key, value] = item
      return { label: value, value: key }
    })

    filter.workYears = Object.entries(workYearList.data).map((item, index) => {
      const [key, value] = item
      return { label: value, value: key }
    })

    const [areaDictTreeData] = areaDictToTreeData(areaList.data)
    filter.areas = areaDictTreeData

    filter.degrees = Object.entries(degreeList.data).map((item, index) => {
      const [key, value] = item
      return { label: value, value: key }
    })
  }).catch(err => {
    message.error(err)
  }).finally(() => {
    status.initFilter = false
  })
}

async function getTalentSearchList() {
  status.listLoading = true
  try {
    const talentList = await getMyTalentList(queryParams, pagination)
    const listData = talentList.data
    searchResult.value = listData.records
    pagination.current = listData.current
    pagination.total = listData.total
  } catch (err: any) {
    message.error(`搜索人才失败，请重试。${err.message}`)
  }
  status.listLoading = false
}

function handleFilterChange() {
  pagination.current = 1
  queryParams.searchId = getSearchId()
  getTalentSearchList()
}

function pageChange(pageinfo: any) {
  pagination.current = pageinfo.current
  pagination.pageSize = pageinfo.pageSize
  getTalentSearchList()
}

function getSearchId() {
  return `${new Date().getTime().toString(36)}${Math.random().toString(36)}`
}

function handleKeywordSearch(value: string) {
  queryParams.keyword = value
  queryParams.searchId = getSearchId()
  pagination.current = 1
  getTalentSearchList()
}

const talentId = ref<number>()
function showTalentDetail(id: number) {
  talentId.value = id
  status.showTalentDetail = true
}

onActivated(() => {
  initFilter()
  getTalentSearchList()
})

</script>

<style lang="scss" scoped>
.list-page-laytout {
  // display: flex;
  background-color: #fff;
  border-radius: 8px;
  height: calc(100vh - 64px - 64px - 16px);
  overflow-y: scroll;
}

.talent-list {
  padding: 0 12px 12px;
  flex: 0 0 100%;
}

.talent-pagination {
  text-align: center;
  padding: 12px 0 24px;
}

.side-nav {
  flex: 0 0 180px;
  // background-color: #1e1d1d;
  // color: #fff;
  position: sticky;
  top: 0;
  padding: 12px 18px;
  border-radius: 8px;
  border-right: 1px solid #eee;;

  .nav-group {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 8px;
    }

    .title {
      color: #999;
      margin-bottom: 18px;
    }

    .list {
      margin: -8px;
      padding: 0;

      li {
        padding: 8px;
        margin: 4px 0;
        list-style: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all .2s;

        &:hover {
          transition: all .2s;
          background-color: #eee;
        }

        &.active {
          background-color: RGBA(255, 145, 17, 0.1);
          color: #FF9111;
        }
      }
    }
  }
}

.content {
  flex:  1;
  max-width: calc(100% - 180px);
}

.talent-search-filter {
  background-color: #fff;
  padding: 12px;
  border-radius: 8px;

  .search-input {
    margin-bottom: 12px;
  }
}
</style>