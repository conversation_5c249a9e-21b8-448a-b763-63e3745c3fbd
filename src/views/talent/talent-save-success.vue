<template lang="pug">
.talent-save-success-page
  a-result(title="人才保存成功！" status="success")
    template(#extra)
      a-button(type="primary" @click="() => { goDetail(talentId) }") 查看人才详情
      a-button(type="primary" ghost @click="() => { goList() }") 人才列表
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const talentId = ref<number>(Number(route.params.id))

const router = useRouter()
function goDetail(talentId: number) {
  router.push(`/talent/${talentId}/detail`)
}

function goList() {
  router.push(`/talent/list`)
}

onMounted(() => {
  talentId.value = Number(route.params.id)
})

</script>

<style lang="scss" scoped>
.talent-save-success-page {
  background-color: #fff;
  border-radius: 8px;
}
</style>