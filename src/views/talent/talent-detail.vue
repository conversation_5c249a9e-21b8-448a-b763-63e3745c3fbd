<template lang="pug">
.talent-detail-page
  a-page-header( title="人才详情", sub-title="", @back="()=>{$router.go(-1)}", style="padding: 0 0 8px;")
  TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")
</template>

<script lang="ts" setup>
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import { reactive } from 'vue';
import { toRef } from 'vue'

const status = reactive({
  showTalentDetail: false,
})

const props = defineProps<{id:number}>()
const talentId = toRef(props, 'id')
</script>

<style lang="scss" scoped>
.talent-detail-page {
  .talent-detail-container {
    background-color: #FFF;
    border-radius: 8px;
    overflow: hidden;

    .talent-action {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
      text-align: right;
      background-color: #fff;
      width: 100%;
    }
  }
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__head {
    padding: 24px 24px 0 32px;

    h4 {
      font-size: 20px;
      margin-bottom: 24px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }

  &__empty {
    padding-bottom: 24px;
  }
}

.follow-up {
  background-color: #fff;
  border-radius: 4px;
  margin-top: 16px;

  &__head {
    padding: 24px 24px 0 32px;
    position: relative;

    &:before {
      content: '';
      display: block;
      width: 4px;
      background-color: #FF9111;
      height: 30px;
      position: absolute;
      left: 16px;
      top: 21px;
    }

    h4 {
      font-size: 20px;
      position: relative;
      line-height: 24px;
      color: #ddd;
      margin: 0;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }

  &__textarea {
    padding: 16px 24px 24px;
    text-align: right;
  }

  &__textarea-input {
    background-color: #f9f9f9;
    border: none;
    border-radius: 4px;
    outline: none;
    margin-bottom: 16px;
  }

  &__comments {
    .comments-item {
      padding: 24px;
      border-top: 1px solid #f0f0f0;

      &__comment {
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      &__name {
        color: #999;
      }

      &__date {
        color: #999;
      }
    }
  }

  .comment-empty {
    padding-bottom: 24px;
  }

  .comment-pagination {
    padding: 16px 0;
    text-align: center;
  }
}
</style>