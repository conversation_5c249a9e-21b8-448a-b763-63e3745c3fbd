<script setup lang="ts">
import { getAreaList } from "@/api/dictionary"
// import { reactive } from "@vue/reactivity"
import { ref, onMounted, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from "vue-router"
import { getAllIndustryList, getCompanyFundingStageList, dictionary } from '@/api/dictionary'
import { getCustomerList } from "@/api/customer"
import { areaDictToTreeData, industryDictToTreeData } from "@/utils/form-data-helper"
import FilterItem from '@/components/ui/filter-item.vue'
import { onActivated } from "vue"

interface OAny {
  [propName: string]: any;
}

const router = useRouter()

// 检索条件
const queryParams = reactive({
  keyWord: '' as string | null,
  areaIds: [] as any[] | null,
  industryIds: [] as any[] | null,
  investStatus: null as string | null,
})

// 用于渲染filter
const filter = reactive({
  areas: [] as any,
  areasMap: {} as any,
  fundingStage: [] as any[],
  industry: [] as any,
  industryMap: {} as any,
})

const status = reactive({
  initFilter: false,
  listLoading: false,
  searchLoading: false,
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: false
})

const customerList = ref([]);

onMounted(() => {
  initFilter()
})

// 初始化Filter
const initFilter = async () => {
  status.initFilter = true
  try {
    const [industryDictRes, areaDictRes, fundingStageDictRes] = await Promise.all([getAllIndustryList(), dictionary.getAllAreaList(), getCompanyFundingStageList()])
    // area list init

    for(let key in fundingStageDictRes.data) {
      filter.fundingStage.push({
        label: fundingStageDictRes.data[key],
        value: key
      })
    }
    // filter.fundingStage = fundingStageDictRes.data

    const [industryFormData, industryMap] = industryDictToTreeData(industryDictRes.data)
    filter.industry = industryFormData
    filter.industryMap = industryMap

    const [areaFormData, areaMap] = areaDictToTreeData(areaDictRes.data)
    filter.areas = areaFormData
    filter.areasMap = areaMap

  } catch (err) {
    console.log(err)
    message.error('初始化筛选器失败，请重试。')
  }
  status.initFilter = false
}

function handleAreaChange(areaId: number) {
  const areaItem = filter.areasMap.get(areaId)
  if (areaItem) queryParams.areaIds = [areaItem.id].concat(areaItem.children.map((item: any) => item.id))
  else queryParams.areaIds = null
  pagination.current = 1
  fetchCustomerList(queryParams)
}

function handleFundingStageChange(value: any) {
  queryParams.investStatus = value
  pagination.current = 1
  fetchCustomerList(queryParams)
}

function handleSearchKeywordChange(keyword: string) {
  queryParams.keyWord = keyword
  pagination.current = 1
  fetchCustomerList(queryParams)
}

function handleIndustryChange(industryId: number) {
  const industryItem = filter.industryMap.get(industryId)
  if (industryItem) queryParams.industryIds = [industryItem.id].concat(industryItem.children.map((item: any) => item.id))
  else queryParams.industryIds = null
  pagination.current = 1
  fetchCustomerList(queryParams)
}

const columnsConfig = [
  { title: '公司', key: 'customer-logo', width: 60 },
  { title: '', key: 'customer-info' },
  { title: '项目数', key: 'job_count', dataIndex: 'jobRequirementCount' },
  { title: '合作类型', key: 'cooperation', dataIndex: 'cooperationStatusStr' },
  { title: 'BD', key: 'staff', dataIndex: 'companyUserStr' },
  { title: '操作', key: 'action', dataIndex: 'action' },
]
// 获取职位列表 
async function fetchCustomerList(queryParams: any) {
  status.searchLoading = true
  try {
    const res = await getCustomerList(Object.assign({}, queryParams, { current: pagination.current, size: pagination.pageSize }))
    pagination.total = res.data.total
    customerList.value = res.data.customers
  } catch (err: any) {
    message.error(`查询错误，请稍后重试 [${err.message}]`)
  }
  status.searchLoading = false
}

const handleClickToDetail = (job: any) => {
  router.push({
    path: `/job/${job.id}/detail`
  })
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      router.push({
        path: `/customer/${record.id}/detail`
      })
    }
  }
}

function pageChange(pageInfo: any) {
  pagination.current = pageInfo.current
  fetchCustomerList(queryParams)
}

onActivated(() => {
  fetchCustomerList(queryParams)
})

</script>

<template lang="pug">
mixin page-header-section
  a-page-header(
    title="全部客户",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      a-button(type="primary" @click="$router.push('/customer/create')") 新增客户

//- mixin keyword-search-section
//-   .customer-list-page__search
//-     a-input-search(
//-       v-model:value="queryParams.name",
//-       enter-button="搜索",
//-       placeholder="输入公司名称"
//-       @search="(value) => {handleSearchKeywordChange(value)}"
//-       :allowClear="true"
//-     )

mixin filter-section
  .position-filter
    a-spin(:spinning="status.initFilter")
      .search-input
        a-input-search(placeholder="职位名称搜索", enter-button, v-model:value="queryParams.name", @search="(value) => {handleSearchKeywordChange(value)}")

      a-row.search-filter(:gutter="[12, 12]")
        a-col(:xxl="6" :lg="8" :md="12" :sm="12")
          FilterItem(label="城市")
            a-tree-select(
              :tree-data="filter.areas",
              :fieldNames="{ label: 'title', value: 'id' }",
              allow-clear
              placeholder="请选择城市",
              @change="handleAreaChange"
            )

        a-col(:xxl="6" :lg="8" :md="12" :sm="12")
          FilterItem(label="行业")
            a-tree-select(
              :tree-data="filter.industry",
              :fieldNames="{ label: 'title', value: 'id' }",
              allow-clear
              placeholder="请选择行业",
              @change="handleIndustryChange"
            )

        a-col(:xxl="6" :lg="8" :md="12" :sm="12")
          FilterItem(label="融资阶段")
            a-select(
              :options="filter.fundingStage",
              allow-clear
              placeholder="请选择融资阶段",
              @change="handleFundingStageChange"
            )

mixin position-search-list
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsConfig" 
        :data-source="customerList" 
        :scroll="{ x: 1200 }"
        :pagination="pagination"
        :customRow="handleCustomRow"
        @change="(pagination)=>{pageChange(pagination)}"
        rowClassName="clickable"
      )
        template(#bodyCell="{ column, record }")
          template(v-if="column.key === 'action'") 
            a-button(type="link" @click="()=>{}") 查看详情

          template(v-if="column.key === 'customer-logo'")
            a-avatar.logo(shape="square" :size="36" :src="`https://image.itp.smartdeer.work/images/${record.customerLogo}`") {{ record.customerFullName[0]}}
          template(v-if="column.key === 'customer-info'")
            .customer-info
              .name {{ record.customerFullName }}
              .info
                span {{ record.industryStr }} · {{ record.investStatusStr }} · {{ record.companyScaleStr }} · {{ record.areaStr }}

.customer-list-page
  +page-header-section
  +filter-section
  +position-search-list
</template>


<style lang="scss" scoped>
.customer-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }

  :deep(.clickable) {
    cursor: pointer;
  }
}

.position-filter {
  background: #fff;
  padding: 16px 16px;
  margin-bottom: 16px;
  border-radius: 6px;
  .search-input {
    margin-bottom: 16px;
  }
}

.customer-info {
  .name {
    font-weight: bold;
  }

  .info {
    color: #999;
  }
}

.position-search {
  background-color: #fff;
  border-radius: 6px;

  &-head {
    height: 74px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;

    &__title {
      font-weight: bold;
      font-size: 16px;
    }
  }
}
</style>