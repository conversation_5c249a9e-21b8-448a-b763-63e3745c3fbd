<template lang="pug">
mixin page-head
  a-page-header(
    title="客户详情",
    sub-title="",
    @back="()=>$router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      //- a-button(type="primary" @click="() => status.showCreatePayment = true") 回款申请
      //- a-button(type="primary" @click="() => status.showCreateInvoice = true") 申请开票

mixin customer-detail
  .customer-detail
    info-section(editable @edit="handleEditBasicInfo")
      .customer-basic-info
        .customer-basic-info__avatar
          template(v-if="customer.customerLogo")
            .customer-basic-info__logo(:style="`background-image:url(https://image.itp.smartdeer.work/images/${customer.customerLogo});`")
          template(v-else)
            a-avatar(:size="96" alt="avatar" shape="square" :src="`https://image.itp.smartdeer.work/images/${customer.customerLogo}`") {{customer.customerFullName}}
        .customer-basic-info__content
          h2 {{customer.customerFullName}}
          .customer-basic-info__basic
            a-space(:size="12")
              span 公司规模: {{ customer.companyScaleStr }}
              span 所属行业: {{ customer.industryStr }}
              span 融资规模: {{ customer.investStatusStr }}
          .customer-basic-info__contact
            a-space
              a-tag(v-for="(tag, index) in customer.tags") {{tag.name}}

    info-section(title="基础信息")
      a-descriptions(:column="2" :labelStyle="{color: '#999'}")
        a-descriptions-item(label="所在地区") {{customer.areaStr}}
        a-descriptions-item(label="所属行业") {{customer.industryStr}}
        a-descriptions-item(label="公司规模") {{customer.companyScaleStr}}
        a-descriptions-item(label="公司性质") {{customer.companyTypeStr}}
        a-descriptions-item(label="融资规模") {{customer.investStatusStr}}
        a-descriptions-item(label="详细地址") {{customer.customerAddress}}
        a-descriptions-item(label="客户网站") {{customer.customerWebsite}}
        a-descriptions-item(label="扩展类别") {{ customer.extendIndustryStr }}
        a-descriptions-item(label="客户级别") {{ customer.customerPriority }}
        a-descriptions-item(label="客户负责人") {{ customer.companyUserStr }}

    info-section(title="公司介绍")
      pre {{customer.customerIntroduction}}

    info-section(title="相关文件" v-if="customer.customerFileObjects && customer.customerFileObjects.length > 0")
      .file-list
        .file-item(v-for="file in customer.customerFileObjects" :key="file.fileName")
          .file-info
            .file-icon
              FileOutlined
            .file-name {{ file.fileName }}
          .file-actions
            a-button(type="link" size="small" @click="downloadFile(file.fileUrl, file.fileName)")
              DownloadOutlined
              span 下载
    info-section(title="合作条款")
      .finance-config
        a-descriptions(:column="2" :labelStyle="{color: '999'}")
          a-descriptions-item(label="货币类型") {{ customerFinanceConfig.currencyTypeStr }}
          a-descriptions-item(label="最低金额") {{ customerFinanceConfig.minPrice }} {{ customerFinanceConfig.currencyTypeStr }}
          a-descriptions-item(label="付款期数") {{ customerFinanceConfig.paymentCycles.length }} 期
          a-descriptions-item(label="客户付款周期") {{ customerFinanceConfig.exceedConfirmDays }} 天
          a-descriptions-item(label="付款周期配置")
            ul.pay_cycles
              li(v-for="(item, index) in customerFinanceConfig.paymentCycles") {{ getPaymentCycleStr(item) }}
          a-descriptions-item(label="佣金阶梯配置")
            ul.pay_steps
              li(v-for="(item, index) in customerFinanceConfig.quotedPriceOnDelivery") {{ getPaymentPriceStr(item) }}
        a-button(
          class="add-button"
          type="default"
          @click="status.financeConfigFormVisible = true"
        ) 更新客户佣金配置

    info-section(title="联系人")
      .section-body
        a-row.customer-contact(v-for="(item, index) in customer.contactors")
          a-col(:span="4")
            strong {{item.name}}
            a-tag.customer-contact__tag(v-if="item.isKeyMan" color="#ff9911") 关键
          a-col(:span="4") {{ getGenderStr(item.gender) }}
          a-col(:span="8") {{ item.phone }}
          a-col(:span="8") {{ item.areaStr }}

    info-section(title="合同")
      ContractList(:customerId="customerId")

mixin customer-postions
  .customer-positions
    h4.title 进展中项目

    .job-list
      a-spin(:spinning="status.jobLoading")
        template(v-for="(job, index) in jobList" v-if="jobList.length")
          a-divider(v-if="index !== 0")
          .job-item(@click="$router.push(`/job/${job.jobRequirementId}/detail`)")
            a-space.job-head(:size="0")
              JobPriority(:priority="job.priority")
              strong {{job.positionTitle}}
            .job-info {{ getJobInfoList(job).join(' · ') }}
            .job-user
              a-row(type="flex" :gutter="[8, 4]")
                a-col(flex="1 1 auto") BD: {{ getUsers('bd', job.properties) }}
                a-col(flex="1 1 auto") PM: {{ getUsers('pm', job.properties) }}
                a-col(flex="1 1 auto") CA: {{ getUsers('ca', job.properties) }}

        template(v-if="jobList.length == 0 && status.jobLoading === false")
          a-empty(description="暂无进行中项目")

.customer-detail-page
  +page-head
  a-row(:gutter="16")
    a-col(:span="16")
      +customer-detail
    a-col(:span="8")
      +customer-postions

  a-drawer(v-model:open="drawer.show" :title="drawer.title" :destroyOnClose="true" :width="480" :bodyStyle="{padding: 0}")
    customerBasicEditor(:customer-id="customer.id" @close="drawer.show=false" @update="handleDetailUpdate")

  template(v-if="status.showCreateInvoice")
    CreateInvoice(
      :customerId="customerId"
      @close="status.showCreateInvoice = false"
      @success="status.showCreateInvoice = false"
    )

  FinanceConfigForm(
    v-if="status.financeConfigFormVisible"
    :customerId="customerId"
    @close="status.financeConfigFormVisible = false"
    @success="handleSubmittedFinanceConfig"
  )
</template>

<script lang="ts" setup>
import { getCustomerDetail } from '@/api/customer'
import { getCustomerFinanceConfig } from '@/api/finance'
import { getCustomerJobRequirements } from '@/api/job'
import { message } from 'ant-design-vue'
import { onActivated, onMounted, reactive, ref, shallowRef } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { getPositionDetail, getPositionsByCustomer } from '@/api/position'
import customerBasicEditor from '@/components/app/customer-basic-update.vue'
import InfoSection from '@/components/info-section/info-section.vue'
import { formatSalary } from '@/utils/salary-helper'
import CreateInvoice from '@/components/app/create-invoice.vue'
import ContractList from '@/components/app/contract-list.vue'
import JobPriority from '@/components/app/job-priority.vue'
import FinanceConfigForm from '@/components/app/finance-config-form.vue'
import { getJobStatus } from '@/api/dictionary'
import { toRef, watch } from 'vue'
import { FileOutlined, DownloadOutlined } from '@ant-design/icons-vue'

const props = defineProps<{id:number}>()
const customerId = toRef(props, 'id')

const drawer = reactive({
  show: false,
  title: '',
  props: {},
  component: null as any
})

const status = reactive({
  detailLoading: false,
  jobLoading: false,
  positionLoading: false,

  showCreateInvoice: false,
  showCreatePayment: false,
  contractLoading: false,
  financeConfigFormVisible: false
})

const GENDER_MAP = ['未知', '男', '女', '保密']
function getGenderStr(gender: number) {
  return GENDER_MAP[gender]
}

function getJobInfoList(job:any) {
  const list = []
  if (job.areaStr) list.push(job.areaStr)
  if (job.requireDegreeStr !== '未知') list.push(job.requireDegreeStr)
  if (job.requireWorkYearsStr !== '未知') list.push(job.requireWorkYearsStr)
  list.push(formatSalary(job))

  return list
}

function downloadFile(fileUrl: string, fileName: string) {
  if (fileUrl) {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

const customer = ref<any>({})
const customerLatestContract = ref<any>({})
const customerFinanceConfig = ref<any>(null)
async function fetchCustomerDetail(customerId: number) {
  status.detailLoading = true
  try {
    const res = await getCustomerDetail(customerId)
    customer.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.detailLoading = false
}

async function fetchCustomerFinanceConfig(customerId: number) {
  status.detailLoading = true
  try {
    const res = await getCustomerFinanceConfig(customerId);
    customerFinanceConfig.value = res.data
  } catch (err: any) {
    // message.error(err.message)
  }
  status.detailLoading = false
}

function handleEditBasicInfo() {
  drawer.show = true
  drawer.component = shallowRef(customerBasicEditor)
  drawer.title = '编辑客户信息'
}

function handleDetailUpdate() {
  fetchCustomerDetail(customerId.value)
  drawer.show = false
}

function handleSubmittedFinanceConfig() {
  status.financeConfigFormVisible = false
  fetchCustomerFinanceConfig(customerId.value)
}

const jobList = ref<any[]>([])
async function fetchCustomerJobRequirement(customerId: number) {
  status.jobLoading = true

  try {
    const res = await getCustomerJobRequirements(customerId)
    const tempJobList: any[] = []
    const positionReqeustList = res.data.jobRequirements.map((item: any, index: number) => {
      tempJobList.push({ jobRequirementId: item.id, properties: item.properties, priority: item.priority })
      return getPositionDetail(item.positionId)
    })

    const positionResList = await Promise.all(positionReqeustList)
    positionResList.forEach((item, index) => {
      tempJobList[index] = Object.assign({}, item.data, tempJobList[index])
    })

    jobList.value = tempJobList.sort((a, b) => {
      return  b.priority - a.priority
    })
  } catch (err: any) {
    message.error(err.message)
  }

  status.jobLoading = false
}

function getUsers(type: 'bd' | 'pm' | 'ca', source: any) {
  const users: any[] = []
  source.forEach((item: any, index: number) => {
    if (item.key?.toLowerCase() === type) {
      users.push(item.valueName)
    }
  })
  return users.join(' / ')
}

const positionPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: false
})

const positionList = ref<any[]>([])

const columnsConfig = [
  { title: '职位名称', key: 'title', dataIndex: 'positionTitle', width: 320 },
  { title: '薪资', key: 'salary' },
  { title: '工作地', key: 'cities', dataIndex: 'areaStr' },
  { title: '职能', key: 'function', dataIndex: 'functionStr' },
  { title: '职位类型', key: 'type' },
  { title: '管理岗', key: 'manager' },
  // { title: '职能', key: 'function', dataIndex: 'functionStr' },
]

async function fetchPositionsByCustomer(customerId: number) {
  status.positionLoading = true
  try {
    const res = await getPositionsByCustomer(customerId, {
      current: positionPagination.current,
      size: positionPagination.pageSize,
    })
    positionPagination.total = res.data.total
    positionList.value = res.data.positions
  } catch (err: any) {

  }
  status.positionLoading = false
}

function getPostionTypeStr(type: number) {
  switch (type) {
    case 0:
      return '实习';
    case 1:
      return '校招';
    case 3:
      return '社招';
  }
}

let jobStatusDict = [] as any[]
function getJobStatusStr(status: number) {
  const statusDict = jobStatusDict.find((element) => {
    return element.value === status
  })
  if (statusDict) return statusDict.label
  else return ''
}

function getPaymentCycleStr(cycle: any) {
  let processName = ''
  let processPeriod = ''
  if (cycle.taskDefinitionKey === 'position_pm_talent_interview') {
    processName = '面试'
  } else if (cycle.taskDefinitionKey === 'position_pm_talent_to_offer') {
    processName = 'Offer 确认'
  } else if (cycle.taskDefinitionKey === 'position_pm_talent_to_hired') {
    processName = '入职'
  } else if (cycle.taskDefinitionKey === 'position_pm_talent_in_keep') {
    processName = '保证期'
  }
  if (cycle.onFinished) {
    processPeriod = '完成后'
  } else {
    processPeriod = '开始时'
  }
  return `${processName}阶段${processPeriod}，支付总佣金金额的${cycle.paymentPercent}%`
}

function getPaymentPriceStr(price: any) {
  let timeUnitStr = ''
  let salaryRangeStr = ''
  let commissionStr = ''
  if (price.commissionTimeUnit === 0) {
    timeUnitStr = '月薪'
  } else if (price.commissionTimeUnit === 1) {
    timeUnitStr = '日薪'
  } else {
    timeUnitStr = '年薪'
  }
  if (price.salaryFrom === 0 && price.salaryTo === -1) {
    salaryRangeStr = ''
  } else if (price.salaryTo === -1) {
    salaryRangeStr = '薪资≥' + price.salaryFrom + '，'
  } else {
    salaryRangeStr = '薪资在' + price.salaryFrom + '-' + price.salaryTo + '之间，'
  }
  if (price.percentageAgentFee) {
    commissionStr = timeUnitStr + '的' + Math.floor(price.percentageAgentFee) + '%'
  } else {
    commissionStr = '固定金额' + Math.floor(price.fixedAgentFee)
  }

  return salaryRangeStr + commissionStr
}

onBeforeRouteLeave(() => {
  drawer.show = false
})

onActivated(async ()=>{
  const res = await getJobStatus()
  jobStatusDict = res.data
  await fetchCustomerDetail(customerId.value)
  await fetchCustomerJobRequirement(customerId.value)
  await fetchPositionsByCustomer(customerId.value)
  await fetchCustomerFinanceConfig(customerId.value)
})

</script>

<style lang="scss" scoped>
.section-spliter {
  display: block;
  border-bottom: 1px solid #f0f0f0;
}

section.detail-section {
  padding: 24px 24px 24px 36px;
  border: 1px solid #fff;
  position: relative;

  &.editable {
    .detail-edit-action {
      display: none;
      position: absolute;
      right: 24px;
      top: 24px;
      color: #FF9111;
    }

    &:hover {
      border: 1px solid #FF9111;
      cursor: pointer;

      .detail-edit-action {
        display: block;
      }
    }
  }

  .pre-present {
    display: block;
    unicode-bidi: embed;
    white-space: pre-wrap;
  }

  p {
    margin-bottom: 0;
  }

  &:last-child {
    border-bottom: none;
  }

  .section-head {
    h4 {
      font-size: 20px;
      margin-bottom: 0px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  .section-body {
    margin-top: 24px;
    line-height: 24px;
  }
}

.customer-detail {
  background-color: #fff;
  border-radius: 8px;

  .customer-basic-info {
    display: flex;

    &__logo {
      background-size: contain;
      width: 96px;
      height: 96px;
      background-position: center;
      background-repeat: no-repeat;
    }

    &__content {
      padding-left: 16px;
      line-height: 32px;
    }

    &__name {
      font-size: 20px;
      display: flex;
      align-items: center;
    }

    &__tag {
      margin: 0 12px;
    }
  }
}

.customer-positions {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;

  h4.title {
    font-size: 20px;
    position: relative;
    line-height: 24px;
    margin: 0 ;
    padding-left: 18px;
    margin-bottom: 24px;

    &::before {
      content: '';
      display: block;
      height: 20px;
      width: 4px;
      background-color: #FF9111;
      border-radius: 2px;
      position: absolute;
      left: 0;
      top: 2px;
    }
  }

  .job-list {
    .job-head {
      font-size: 16px;
      transition: all .3s;
      margin-bottom: 8px;

      em {
        font-size: 14px;
        font-style: normal;
        margin-left: 8px;
        color: #999;
      }
    }
    .job-info{
      color: #999;
      margin-bottom: 4px;
    }

    .job-item {
      cursor: pointer;
      &:hover {
        .job-head {
          color: #FF9111;
          transition: all .3s;
        }
      }
    }

    .job-user {
      color: #999;
    }
  }
}

.customer-contact {
  line-height: 32px;

  &__tag {
    margin-left: 12px;
  }
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }
}

.company-info {
  .title {
    color: #999;
  }
}

.position-list {
  margin: -24px 0;

  .position-item {
    margin: 8px 0;
    padding: 16px 24px 16px 36px;

    &:hover {
      background-color: #f9f9f9;
      cursor: pointer;
    }

    &__position {
      strong {
        font-size: 18px;
        line-height: 36px;
      }
    }
  }
}

.position-title {
  line-height: 32px;

  .title {}
}
.red {
  padding: 20px 0;
  font-size: 14px;
  color: #FF6600;
}
.black {
  height: 60px;
  margin: 10px 0;
  border: 1px solid #000;
}
.finance-config {
  position: relative;
}
.add-button{
  position: absolute;
  right: 0;
  top: -52px;
}
.pay_cycles, .pay_steps {
  padding: 0;
  margin: 0;

  li {
    padding: 0;
    list-style: none;
  }
}

.file-list {
  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;

      .file-icon {
        font-size: 20px;
        color: #1890ff;
        margin-right: 12px;
      }

      .file-name {
        font-size: 14px;
        color: #333;
        word-break: break-all;
      }
    }

    .file-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>