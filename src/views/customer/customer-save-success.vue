<template lang="pug">
.customer-save-success-page
  a-result(title="客户保存成功！" status="success")
    template(#extra)
      a-button(type="primary" @click="() => { goDetail(customerId) }") 查看客户详情
      a-button(type="primary" ghost @click="() => { goList() }") 客户列表
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const customerId = ref<number>(Number(route.params.id))
const router = useRouter()

function goDetail(customerId: number) {
  router.push(`/customer/${customerId}/detail`)
}

function goList() {
  router.push(`/customer/list`)
}

onMounted(()=>{
  customerId.value = Number(route.params.id)
})

</script>

<style lang="scss" scoped>
.customer-save-success-page {
  background-color: #fff;
  border-radius: 8px;
}

</style>