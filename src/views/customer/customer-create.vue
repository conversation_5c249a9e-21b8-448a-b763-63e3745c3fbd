<template lang="pug">
.customer-create-page
  a-page-header(
    title="新增客户",
    sub-title="",
    @back="goBack",
    style="padding: 0 0 8px;"
  )

  a-spin(:spinning="status.initDict")
    .page-body
      a-form(labelAlign="left" ref="formInstance" :model="form")
        a-row
          a-col(:span="12")
            .form-group
              .form-group-title
                h4 基础信息

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 客户LOGO
                a-col(:span="20")
                  a-form-item(
                    name="customerLogo"
                  )
                    a-upload.logo_uploader(
                      name="image",
                      accept="png,jpg,jpeg",
                      :show-upload-list="false",
                      :action="API_URL.LOGO_UPLOAD",
                      :headers="{'Authorization': userStore.token}",
                      @change="handleLogoUploadChange"
                    )
                      a-avatar.avatar(
                        v-if="form.logoUrl",
                        :src="form.logoUrl",
                        alt="avatar"
                        shape="square"
                        :size="140"
                      )
                      div.upload-area(v-else)
                        LoadingOutlined(v-if="status.logoUploading")
                        PlusOutlined(v-else)

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 客户名称
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="customerFullName"
                    :rules="[{ type: 'string', required: true, message: '客户名称不能为空'}]"
                  )
                    a-input(v-model:value="form.customerFullName")

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 所在地区
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="areaId"
                    :rules="[{ type: 'number', required: true, message: '客户所在地区不能为空' }]"
                  )
                    a-tree-select(
                      style="width:100%",
                      placeholder="请在下拉列表中选择城市",
                      show-search,
                      treeNodeFilterProp="title",
                      :fieldNames="{ label: 'title', value: 'id' }",
                      :tree-data="dict.areas",
                      v-model:value="form.areaId",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 所属行业
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="industryId"
                    :rules="[{ type: 'array', required: true, message: '客户所属行业不能为空' }]"
                  )
                    a-cascader(
                      placeholder="请选择所属行业",
                      v-model:value="form.industryId",
                      :fieldNames="{ label: 'title', value: 'id' }",
                      treeNodeFilterProp="title",
                      :options="dict.industry",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 详细地址
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="customerAddress"
                    :rules="[{ type: 'string', required: false }]"
                  )
                    a-input(type="text" v-model:value="form.customerAddress")


              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 公司规模
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="companyScale"
                    :rules="[{required: true, message: '客户名称不能为空' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-select(
                      placeholder="请选择公司规模",
                      v-model:value="form.companyScale",
                      :options="dict.scale",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 公司性质
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="companyType"
                    :rules="[{required: true, message: '客户名称不能为空' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-select(
                      placeholder="请选择公司性质",
                      v-model:value="form.companyType",
                      :options="dict.type",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 融资规模
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="investStatus"
                    :rules="[{required: true, message: '客户名称不能为空' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-select(
                      placeholder="请选择公司融资规模",
                      v-model:value="form.investStatus",
                      :options="dict.fundingStage",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 公司介绍
                a-col(:span="20")

                  a-form-item(
                    name="customerIntroduction"
                    :rules="[{required: true, message: '公司简介不能为空' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-textarea(
                      placeholder="请输入公司介绍",
                      v-model:value="form.customerIntroduction",
                      :autoSize= "{minRows: 4, maxRows: 10}"
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 相关文件
                a-col(:span="20")
                  a-form-item(name="fileIds")
                    multi-file-upload(
                      v-model:value="form.fileIds"
                      :max-count="10"
                      :max-size="50 * 1024 * 1024"
                      @change="handleFileListChange"
                    )

            .form-group
              .form-group-title
                h4 基础信息

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 对接人
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    name="companyUserId"
                    :rules="[{ required: true, type: 'number', message: '负责的员工不能为空' }]"
                  )
                    a-select(
                      placeholder="请选择负责的员工",
                      v-model:value="form.companyUserId",
                      show-search
                      :filterOption="true",
                      :optionFilterProp="'label'",
                      :options="dict.staff",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 客户来源
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                  )
                    a-radio-group(v-model:value="form.source")
                      a-radio(:value="2") 灵鹿推
                      a-radio(:value="1") 灵鹿聘
                      a-radio(:value="3") 手动上传
                      a-radio(:value="0") 其他

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 备注信息
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    name="content"
                  )
                    a-textarea(v-model:value="form.content" :rows="4")

          a-col(:span="12")
            .form-group
              .form-group-title
                h4 联系人
                a-button(@click="addContact") 新增联系人

              .company-contact(v-for="(item, index) in form.customerContacts")
                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label.required 姓名
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'name']"
                      :rules="[{type:'string', required:true, message:'联系人姓名不能为空'}]"
                    )
                      a-input(v-model:value="form.customerContacts[index].name")

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label.required 尊称
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'gender']"
                      :rules="[{type:'number', required:true, message:'联系人尊称不能为空'}]"
                    )
                      a-radio-group(v-model:value="form.customerContacts[index].gender")
                        a-radio(:value="3") 未知
                        a-radio(:value="1") 先生
                        a-radio(:value="2") 女士

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label.required 关键角色
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'isKeyMan']"
                      :rules="[{required:true, message:'联系人是否是关键角色为必填项'}]"
                    )
                      a-radio-group(v-model:value="form.customerContacts[index].isKeyMan")
                        a-radio(:value="true") 是
                        a-radio(:value="false") 否

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label 部门职务
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }",
                      :name="['customerContacts', index, 'department']"
                    )
                      a-input(v-model:value="form.customerContacts[index].department")

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label 手机号码
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'phone']"
                      :rules="[{ type: 'string', pattern: new RegExp(/^[\\d]*$/), required: false, message: '请填写正确的手机号码' }]"
                    )
                      a-input(:maxlength="11" v-model:value="form.customerContacts[index].phone")

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label 微信号
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'wechatNumber']"
                      :rules="[{type:'string', required:false, message:'微信号不能为空'}]"
                    )
                      a-input(:maxlength="30" v-model:value="form.customerContacts[index].wechatNumber")

                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label 电子邮箱
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                      :name="['customerContacts', index, 'mail']"
                      :rules="[{ type: 'email', message: '请填写正确的邮箱地址' }]"
                    )
                      a-input(v-model:value="form.customerContacts[index].mail")


                a-row.form-item-line
                  a-col(:span="4")
                    .form-item-label 所在地区
                  a-col(:span="20")
                    a-form-item(
                      :label-col="{ span: 8 }",
                      :wrappercol="{ span: 16 }"
                    )
                      a-tree-select(
                        style="width:100%",
                        placeholder="请在下拉列表中选择城市",
                        v-model:value="form.customerContacts[index].areaId",
                        :fieldNames="{ label: 'title', value: 'id' }",
                        treeNodeFilterProp="title",
                        show-search,
                        :tree-data="dict.areas",
                    )
                a-row.form-item-line(v-if="form.customerContacts.length > 1")
                  a-col(:span="24")
                    .form-item-action
                      a-popconfirm(
                        title="确定要删除本条联系人信息吗？",
                        placement="left",
                        @confirm="delContact(index)"
                      )
                        MinusOutlined
                        span 删除

        a-affix(:offset-bottom="0")
          .form-action
            a-space()
              //- a-button(@click="draft" type="primary" ghost) 保存草稿
              a-button(@click="save" type="primary" :loading="status.loading") 新增客户
</template>

<script lang="ts" setup>

import { UploadOutlined, MinusOutlined, PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import MultiFileUpload from '@/components/upload/multi-file.vue'
import { useRouter } from 'vue-router';
import { getCompanyTypeList, getCompanyScaleList, getAllIndustryList, getCompanyFundingStageList, dictionary } from '@/api/dictionary'
import { reactive, ref } from 'vue'
import localStore from 'store'
import { getCompanyUserList } from '@/api/system/users';
import { createCustomer } from '@/api/customer'
import { message } from 'ant-design-vue';
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { useUserStore } from '@/store/user.store'
import { API_URL } from '@/api/customer'

const userStore = useUserStore()

const router = useRouter()
function goBack() {
  router.go(-1)
}

const formInstance = ref()

const form = ref({
  areaId: null,
  companyScale: null,
  companyType: null,
  customerContacts: [{
    name: '',
    areaId: null,
    department: '',
    gender: null,
    isKeyMan: null,
    mail: '',
    phone: '',
    wechatNumber: ''
  }],
  customerFullName: '',
  customerAddress: '',
  source: 0,
  industryId: [],
  investStatus: null,
  companyUserId: null,
  contractUrls: [],
  content: '',
  logoUrl: '',
  customerLogo: '',
  customerIntroduction: '',
  fileIds: [] as string[]
})

const dict = reactive({
  areas: [] as any,
  industry: [] as any[],
  scale: [],
  type: [],
  fundingStage: [] as any[],
  staff: []
})

const status = reactive({
  initDict: false,
  loading: false,
  logoUploading: false,
})

async function initDict() {
  status.initDict = true
  try {
    const [
      dictCompanyType, dictCompanyScale, dictArea,
      dictIndustry, dictFundingStag, dictStaff
    ] = await Promise.all([
      getCompanyTypeList(), getCompanyScaleList(), dictionary.getAllAreaList(),
      getAllIndustryList(), getCompanyFundingStageList(), getCompanyUserList({onboard: true})
    ])

    dict.type = dictCompanyType.data.map((item: any, index: number) => {
      return { value: item.id, label: item.type }
    })
    dict.scale = dictCompanyScale.data.map((item: any, index: number) => {
      return { value: item.id, label: item.type }
    })

    const [areaDictData] = areaDictToTreeData(dictArea.data)
    dict.areas = areaDictData

    dict.staff = dictStaff.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })

    for (let key in dictFundingStag.data) {
      dict.fundingStage.push({ value: key, label: dictFundingStag.data[key] })
    }

    const dictIndustryMap = new Map()
    const tempIdustryList = [] as any[]
    dictIndustry.data.forEach((item: any, index: number) => {
      const targetObj = Object.assign({}, item, { title: item.industryName, children: [] })
      dictIndustryMap.set(item.id, targetObj)
      if (item.parentId === 0) tempIdustryList.push(targetObj)
    })
    dictIndustryMap.forEach((item, key) => {
      if (item.parentId === 0) return
      const parent = dictIndustryMap.get(item.parentId)
      parent.children.push(item)
    })
    dict.industry = tempIdustryList
  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

function addContact() {
  form.value.customerContacts.unshift({
    name: '',
    areaId: null,
    department: '',
    gender: null,
    isKeyMan: null,
    mail: '',
    phone: '',
    wechatNumber: ''
  })
}

function delContact(index: number) {
  form.value.customerContacts.splice(index, 1)
}

function handleLogoUploadChange(info: any) {
  if (info.file.status === 'uploading') {
    status.logoUploading = true
  } else if (info.file.status === 'done') {
    const fileResponse = info.file.response.data
    status.logoUploading = false
    form.value.logoUrl = fileResponse.fileAbsolutePath
    form.value.customerLogo = fileResponse.id
  }
}

async function resetForm() {
  form.value = {
    areaId: null,
    companyScale: null,
    companyType: null,
    customerContacts: [{
      name: '',
      areaId: null,
      department: '',
      gender: null,
      isKeyMan: null,
      mail: '',
      phone: '',
      wechatNumber: '',
    }],
    customerFullName: '',
    customerAddress: '',
    source: 0,
    industryId: [],
    investStatus: null,
    companyUserId: null,
    contractUrls: [],
    content: '',
    logoUrl: '',
    customerLogo: '',
    customerIntroduction: '',
    fileIds: []
  }
}

function handleFileListChange(fileList: any[]) {
  // 文件列表变化时的处理逻辑
  console.log('文件列表已更新:', fileList)
}

async function save() {
  status.loading = true
  try {
    await formInstance.value.validate()

    form.value.customerContacts.forEach((item: any) => {
      if (!item.phone && !item.wechatNumber) {
        throw new Error('联系人手机号或微信号请填写一项')
      }
    })

    const params = {
      ...form.value,
      industryId: form.value.industryId[form.value.industryId.length - 1]
    }

    const res = await createCustomer(params as any)
    resetForm()
    router.push(`/customer/${res.data.id}/save/success`)

  } catch (err: any) {
    if (err.errorFields) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

initDict()

</script>

<style lang="scss" scoped>
.page-body {
  border-radius: 8px;
  background-color: #fff;

  .form-group {
    padding: 24px 48px;

    .company-contact {
      margin-bottom: 32px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
    }

    .form-group-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        line-height: 32px;
        font-size: 18px;
        font-weight: bold;
        position: relative;

        &::before {
          content: "";
          display: block;
          background-color: #ff9111;
          height: 20px;
          width: 3px;
          top: 6px;
          left: -16px;
          border-radius: 2px;
          position: absolute;
        }
      }
    }

    .form-item-line {
      line-height: 32px;

      .form-item-action {
        text-align: right;

      }

      .upload-desc {
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }

      .form-item-label {
        &.required {
          &::after {
            content: ' *';
            color: #ff9111;
          }
        }
      }
    }
  }

  .form-action {
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px;
    text-align: right;
  }

  .logo_uploader {
    .upload-area {
      width: 140px;
      height: 140px;
      border: 1px dotted #999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>