<template lang="pug">
.exception-page
  a-result(title="抱歉，您没有该页面的权限。" sub-title="抱歉，您没有该页面权限，如有需要，请联系系统管理员进行操作。")
    template(#icon)
      ExclamationCircleTwoTone(two-tone-color="#ff9111")

    template(#extra)
      a-button(type="primary" @click="backToHome") 返回
</template>

<script lang="ts" setup>
import router from '@/router';
import { ExclamationCircleTwoTone } from '@ant-design/icons-vue'

function backToHome() {
  // router.push({ path: '/' })
  router.go(-1)
}

</script>

<style lang="scss" scoped></style>
