<template lang="pug">
.exception-page
  a-result(status="404" title="404" sub-title="Sorry, the page you visited does not exist.")
    template(#extra)
      a-button(type="primary" @click="goHome") 返回首页
</template>

<script lang="ts" setup>
import router from '@/router'

function goHome() {
  router.replace('/')
}
</script>

<style lang="sass" scoped>
.exception-page 
  text-align: center
  display: flex
  flex-direction: column
  align-items: center
  justify-content: center
  min-height: 80vh

  .title
    font-size: 20px
    font-weight: bold
</style>
