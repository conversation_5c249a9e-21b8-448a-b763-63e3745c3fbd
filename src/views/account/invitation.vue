<template lang="pug">
.login-page
  .login-container
    .login-logo
      img(src="@/assets/logo-black.png")

    h2.login-title 欢迎回来

    .login-form
      a-form(:model="registerForm", name="basic", layout="vertical")
        a-form-item(
          name="userMobile",
          :rules="[{ required: true, message: 'Please input your mobile!' }]"
        )
          a-input(v-model:value="registerForm.userMobile", size="large")
            template(#prefix)
              UserOutlined(style="color: RGBA(0, 0, 0, 0.2)")

        a-form-item(
          name="password",
          :rules="[{ required: true, message: 'Please input your password!' }]"
        )
          a-input-password(
            v-model:value="registerForm.password",
            size="large"
          )
            template(#prefix)
              LockOutlined(style="color: RGBA(0, 0, 0, 0.2)")

        a-form-item(name="remember")
          .lgin-form-extra
            .login-form-autologin
              a-checkbox(v-model:checked="registerForm.remember") 7天内自动登录

            //- .login-form-forgot
            //-   router-link(to="/") 忘记密码了?

      a-button(
        type="primary",
        html-type="submit",
        block,
        @click="onFinish",
        size="large",
        :loading="status.loading"
      ) 登录
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
const registerForm = reactive({
  username: '',
  userMobile: '',
  password: '',
  remember_me: true,
  captcha: '',
})
</script>

<style lang="scss" scoped>
.login-page {
  padding-top: 10%;
}

.login-logo {
  width: 100%;
  margin: 0 auto 20px;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 0;

  img {
    display: block;
    width: 120px;
    margin: 0 auto;
  }
}

.login-title {
  padding: 0px 20px 10px;
  color: #444;
}

.login-form {
  padding: 0 20px 40px;
}

.login-container {
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 0 20px #eee;
  border-radius: 6px;
}

.lgin-form-extra {
  display: flex;
  justify-content: space-between;

  .login-form-forgot {
    color: #999;
    text-align: right;
  }
}
</style>