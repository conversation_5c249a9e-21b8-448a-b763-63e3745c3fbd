<template lang="pug">
.login-page
  .login-container
    .login-logo
      img(src="@/assets/main-logo.svg")
      span SmartDeer ITP

    h2.login-title 欢迎回来

    .login-form
      a-form(:model="loginFormState", name="basic", layout="vertical")
        a-form-item(
          name="userMobile",
          :rules="[{ required: true, message: 'Please input your mobile!' }]"
        )
          a-input(v-model:value="loginFormState.userMobile", size="large")
            template(#prefix)
              UserOutlined(style="color: RGBA(0, 0, 0, 0.2)")

        a-form-item(
          name="password",
          :rules="[{ required: true, message: 'Please input your password!' }]"
        )
          a-input-password(
            v-model:value="loginFormState.password",
            size="large"
          )
            template(#prefix)
              LockOutlined(style="color: RGBA(0, 0, 0, 0.2)")

        a-form-item(name="remember")
          .lgin-form-extra
            .login-form-autologin
              a-checkbox(v-model:checked="loginFormState.remember_me") 7天内自动登录

            //- .login-form-forgot
            //-   router-link(to="/") 忘记密码了?

      a-button(
        type="primary",
        html-type="submit",
        block,
        @click="submit",
        size="large",
        :loading="status.loading"
      ) 登录
</template>

<script lang="tsx" setup>
import { h, reactive } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/store/user.store'
import { useRouter } from 'vue-router'
import { message, notification, Button } from 'ant-design-vue'
import { securityCheck } from '@/api/user'

const userStore = useUserStore()
const router = useRouter()

const loginFormState = reactive({
  username: '',
  userMobile: '',
  password: '',
  remember_me: true,
})

const status = reactive({
  loading: false
})

async function submit() {
  status.loading = true
  try {
    const res = await userStore.login(loginFormState)
    router.push('/')
    startSecurityCheck()
  } catch (err: any) {
    message.error(err.toString())
  }
  status.loading = false
}

async function goto(path: string) {
  router.push(path)
}

function startSecurityCheck() {
  securityCheck().then(res => {
    if (res.data.isSecurity === false) {
      let btn = null
      let key = new Date().getTime().toString()
      if (res.data.reason === '您的密码为默认密码，请修改密码') {
        btn = () => { return h(Button, { type: 'primary', onClick: () => { goto('/account/password/change'); notification.close(key) } }, { default: () => '修改密码' }) }
      }

      notification.warning({
        message: '安全风险提示',
        description: res.data.reason,
        btn,
        key
      })
    }
  }).catch(err => {

  })
}

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo)
}
</script>

<style lang="scss" scoped>
.login-page {
  padding-top: 10%;
}

.login-logo {
  width: 100%;
  margin: 0 auto 20px;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 0;
  text-align: center;

  img {
    display: block;
    width: 160px;
    margin: 0 auto;
    margin-bottom: 8px;
  }

  span {
    font-size: 16px;
  }
}

.login-title {
  padding: 0px 20px 10px;
  color: #444;
}

.login-form {
  padding: 0 20px 40px;
}

.login-container {
  max-width: 400px;
  margin: 0 auto;
  box-shadow: 0 0 20px #eee;
  border-radius: 6px;
}

.lgin-form-extra {
  display: flex;
  justify-content: space-between;

  .login-form-forgot {
    color: #999;
    text-align: right;
  }
}
</style>
