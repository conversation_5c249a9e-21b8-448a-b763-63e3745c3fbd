<template lang="pug">
.change-page
  .change-container
    .change-title 
      h2 更换密码

    .change-form
      a-form(:model="formState", name="basic", layout="vertical", ref="formInstance")

        a-form-item(
          name="oldPassword",
          :rules="[{ required: true, message: '请输入您的原密码' }]"
        )
          a-input-password(
            v-model:value="formState.oldPassword",
            size="large",
            placeholder="原密码"
          )
            template(#prefix)
              LockOutlined(style="color: RGBA(0, 0, 0, 0.2)")
        a-form-item(
          name="newPassword",
          :rules="[{ required: true, message: '请输入您的新密码' }]"
        )
          a-input-password(
            v-model:value="formState.newPassword",
            size="large",
            placeholder="新密码"
          )
            template(#prefix)
              LockOutlined(style="color: RGBA(0, 0, 0, 0.2)")
        a-form-item(
          name="repeatPassword",
          :rules="[{ asyncValidator: repeatPasswordValidator }]"
        )
          a-input-password(
            v-model:value="formState.repeatPassword",
            size="large",
            placeholder="再次输入新密码"
          )
            template(#prefix)
              LockOutlined(style="color: RGBA(0, 0, 0, 0.2)")

        //- a-form-item(name="remember")
        //-   .lgin-form-extra
        //-     //- .change-form-autologin
        //-     //-   a-checkbox(v-model:checked="loginFormState.remember") 7天内自动登录

        //-     .change-form-forgot
        //-       router-link(to="/") 忘记密码了?

      a-button(
        type="primary",
        block,
        @click="submit",
        size="large",
        :loading="status.loading"
      ) 更换密码
  </template>
  
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/store/user.store'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { updatePassword } from '@/api/user'

const userStore = useUserStore()
const router = useRouter()

const formInstance = ref()
const formState = reactive({
  oldPassword: '',
  newPassword: '',
  repeatPassword: ''
})

const status = reactive({
  loading: false
})

async function submit() {
  status.loading = true
  try {
    await formInstance.value.validate()
    const res = await updatePassword(formState.newPassword, formState.oldPassword)
    message.success('修改密码成功!')
    router.push('/')
  } catch (err: any) {
    if (err.errorFields && err.errorFields.length > 0) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

function repeatPasswordValidator(rule: any, value: string, callback: Function) {
  if (value === '') callback(new Error('请再次填写您的新密码'))
  if (value !== formState.newPassword) callback(new Error('两次输入的密码不一致'))
  callback()
}
</script>
  
<style lang="scss" scoped>
.change-page {
  background-color: #fff;
}

.change-logo {
  width: 100%;
  margin: 0 auto 20px;
  padding: 16px 0;

  img {
    display: block;
    width: 120px;
    margin: 0 auto;
  }
}

.change-title {
  padding: 12px 24px;
  border-bottom: 1px solid #f9f9f9;

  h2 {
    margin: 0;
  }
}

.change-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 24px;
}

.change-container {
  margin: 0 auto;
  border-radius: 6px;
}

.lgin-form-extra {
  display: flex;
  justify-content: space-between;

  .change-form-forgot {
    color: #999;
    text-align: right;
  }
}
</style>
  