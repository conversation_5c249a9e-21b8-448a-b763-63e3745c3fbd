<template lang="pug">
.account-bind-page
  .account-bind-page-title
    h2 账户绑定

  .account-bind-page-content
    .bind-item
      .bind-item__title 灵鹿聘(社招)小程序
      

    .bind-item
      .bind-item__title 灵鹿聘(校招)小程序

</template>

<script lang="ts" setup>

</script>

<style lang="scss" scoped>
.account-bind-page {
  background-color: #fff;
  border-radius: 8px;

  .account-bind-page-title {
    padding: 12px 24px;
    border-bottom: 1px solid #f9f9f9;

    h2 {
      margin: 0
    }
  }
}

.account-bind-page-content {
  .bind-item {
    border-bottom: 1px solid #f9f9f9;
    padding: 24px 24px;

    &:hover {
      background-color: #f9f9f9;
      cursor: pointer;
    }

    &__title {
      font-size: 16px;
    }
  }
}
</style>