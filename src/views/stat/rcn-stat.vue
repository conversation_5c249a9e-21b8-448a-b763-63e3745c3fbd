<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getRcnAccountList } from '@/api/rcn'

// 定义表格数据项的类型
interface TableDataItem {
  id: number
  email: string
  name: string
  addTalentCount: number
  projectCount: number
  recommendCount: number
  registerDate: string
  lastActiveDate: string
}

// 定义 API 响应的类型
interface ApiResponse {
  code: number
  message: string
  data: {
    total: number
    users: any[]
  }
}

// 搜索参数
const searchParams = reactive({
  realName: '',
  current: 1,
  size: 10,
})

// 表格数据
const tableData = ref<TableDataItem[]>([])

// 表格列定义
const columns = [
  { title: '账户 id', dataIndex: 'id', key: 'id', align: 'center' },
  { title: '账号（邮箱）', dataIndex: 'email', key: 'email', align: 'center' },
  { title: '姓名', dataIndex: 'name', key: 'name', align: 'center' },
  { title: '添加人才数量', dataIndex: 'addTalentCount', key: 'addTalentCount', align: 'center' },
  { title: '参与项目数量', dataIndex: 'projectCount', key: 'projectCount', align: 'center' },
  { title: '推荐人才次数', dataIndex: 'recommendCount', key: 'recommendCount', align: 'center' },
  { title: '注册时间', dataIndex: 'registerDate', key: 'registerDate', align: 'center' },
  { title: '最新活跃时间', dataIndex: 'lastActiveDate', key: 'lastActiveDate', align: 'center' },
]

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
})

// 格式化时间戳为日期字符串
function formatDate(timestamp: number): string {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toISOString().split('T')[0]
}

// 处理接口返回的数据
function processTableData(users: any[]): TableDataItem[] {
  return users.map(user => {
    const companyUser = user.companyUser
    const statistic = user.companyUserStatistic
    
    return {
      id: companyUser.id,
      email: companyUser.workMobile || companyUser.email || '-',
      name: companyUser.realName || '-',
      addTalentCount: statistic?.talentTotalCount || 0,
      projectCount: statistic?.joinedProjectCount || 0,
      recommendCount: statistic?.recommendCount || 0,
      registerDate: formatDate(companyUser.createTime),
      lastActiveDate: formatDate(companyUser.updateTime),
    }
  })
}

// 加载数据
async function loadData() {
  try {
    const res = await getRcnAccountList(searchParams) as unknown as ApiResponse
    
    if (res.code === 0 && res.data) {
      tableData.value = processTableData(res.data.users || [])
      pagination.total = res.data.total || 0
      pagination.current = searchParams.current
    } else {
      message.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  }
}

// 搜索处理
function handleSearch() {
  searchParams.current = 1
  pagination.current = 1
  loadData()
}

// 分页变化处理
function handleTableChange(paginationInfo: any) {
  searchParams.current = paginationInfo.current
  searchParams.size = paginationInfo.pageSize
  pagination.current = paginationInfo.current
  pagination.pageSize = paginationInfo.pageSize
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<template lang="pug">
mixin page-header-section
  a-page-header(
    title="全部客户",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      a-button(type="primary" @click="$router.push('/stat/rcn-stat')") RCN 账户

.page-rcn-stat
  +page-header-section
  .search-bar
    a-input(
      v-model:value="searchParams.realName"
      placeholder="RCN 用户ID 或者 邮箱"
      style="width: 400px; margin-right: 16px;"
      @pressEnter="handleSearch"
    )
    a-button(type="primary" @click="handleSearch") 确定
  .table-area
    a-table(
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      :row-key="record => record.id"
      :loading="false"
      @change="handleTableChange"
      bordered
    )
      template(#bodyCell="{ column, record }")
        template(v-if="column.dataIndex === 'addTalentCount' && typeof record[column.dataIndex] === 'number'")
          a(
            style="text-decoration: underline; cursor: pointer;"
            @click.prevent="() => $router.push(`/talent/list?fromUserId=${record.id}`)"
          ) {{ record[column.dataIndex] }}
        template(v-else-if="column.dataIndex === 'projectCount' && typeof record[column.dataIndex] === 'number'")
          a(
            style="text-decoration: underline; cursor: pointer;"
            @click.prevent="() => $router.push(`/job/list?fromUserId=${record.id}`)"
          ) {{ record[column.dataIndex] }}
        template(v-else)
          | {{ record[column.dataIndex] }}
</template>

<style scoped>
.page-rcn-stat {
  padding: 24px;
  background: #f7f7f7;
  min-height: 100vh;
}
.search-bar {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}
.table-area {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}
:deep(.ant-table-thead) th {
  background: #f5f5f5;
  font-weight: bold;
}
</style>