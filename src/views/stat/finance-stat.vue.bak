<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getFinance, getFinanceHeader } from '@/api/stat'
import dayjs from 'dayjs'
import { useWinHeightStore } from '@/store/winHeight.store'
import { getDateRanges } from '@/utils/util'

const winHeightStore = useWinHeightStore()

const queryParams = reactive({
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  dateType: 1
})
const filter = reactive({
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,

})

const columnsList = ref([])
const dataList = ref([])
const dataListSummary = reactive({
  isCompleteDesc: '汇总',
  applyInvoiceAmount: '',
  confirmedInvoiceAmount: '',
  applyStatementAmount: '',
  confirmedStatementAmount: '',
})

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  if (time === '-') return '-'

  return dayjs(time).format(format)
}

const formatAmount = (val: {}) => {
  if (JSON.stringify(val) === '{}') return ''

  const o: any = Object.assign({}, val)

  Object.keys(o).forEach((key: string) => {
    o[key] = o[key] / 100
  })

  return JSON.stringify(o).replace(/[{"]/g, '').replace(/[}"]/g, '\n')
}

// 同属性相加合并
const mergeObjects = (a: any, b: any) => {
  const result = { ...a }; // create a copy of object a
  for (const key in b) {
    if (result.hasOwnProperty(key)) {
      result[key] += b[key]; // add the values of same properties
    } else {
      result[key] = b[key]; // add new properties
    }
  }
  return result;
}

const formatAmountTotal = (val: any) => {
  if (JSON.stringify(val) === '{}') return ''

  const o = Object.assign({}, val)
  
  Object.keys(o).forEach((key: string) => {
    o[key] = o[key] / 100
  })

  return JSON.stringify(o).replace(/[{}"]/g, '').replace(/[:]/g, ' ');
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const columns: any = await getFinanceHeader()

    columnsList.value = columns.data

    const res = await getFinance({
      endTime,
      startTime
    })

    const summary: any = {
      isCompleteDesc: '汇总',
      applyInvoiceAmount: {},
      confirmedInvoiceAmount: {},

      applyStatementAmount: {},
      confirmedStatementAmount: {},
    }

    const list = res.data.map((item: any) => {

      summary.applyInvoiceAmount = mergeObjects(summary.applyInvoiceAmount, item.applyInvoiceAmount) 
      summary.confirmedInvoiceAmount = mergeObjects(summary.confirmedInvoiceAmount, item.confirmedInvoiceAmount)
      
      summary.applyStatementAmount = mergeObjects(summary.applyStatementAmount, item.applyStatementAmount)
      summary.confirmedStatementAmount = mergeObjects(summary.confirmedStatementAmount, item.confirmedStatementAmount)

      return {
        startTime: startTime,
        endTime: endTime,
        customerName: item.customerName,
        positionTitle: item.positionTitle,
        offerTime: dateFormat(item.offerTime),
        realName: item.realName,
        onboardTime: item.onboardTime,
        finishTime: item.finishTime,
        invoiceIds: item.invoiceIds.length,
        applyInvoiceAmount: formatAmount(item.applyInvoiceAmount),
        applyStatementAmount: formatAmount(item.applyStatementAmount),
        statementIds: item.statementIds.length,
        confirmedInvoiceAmount: formatAmount(item.confirmedInvoiceAmount),
        confirmedStatementAmount: formatAmount(item.confirmedStatementAmount),
      }
    })

    dataListSummary.applyInvoiceAmount = formatAmountTotal(summary.applyInvoiceAmount)
    dataListSummary.applyStatementAmount = formatAmountTotal(summary.applyStatementAmount)
    dataListSummary.confirmedInvoiceAmount = formatAmountTotal(summary.confirmedInvoiceAmount)
    dataListSummary.confirmedStatementAmount = formatAmountTotal(summary.confirmedStatementAmount)



    dataList.value = list

    status.searchLoading = false
  } catch (err) {
    console.log(err)
    status.searchLoading = false
  }
}
getList()
</script>
<template lang="pug">
.ca-statistics-page
  section.statistics-search-filter
    .search-filter
      a-form-item(label="时间")
        a-range-picker(
          v-model:value="queryParams.date"
          :bordered="false"
          :presets="filter.ranges"
          @change="(value:any) => { handleFilterChange('dateType', value) }"
        )

  //-列表
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1800, y: winHeightStore.value - 400 }" 
        :pagination="false"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(
            v-if="column.key === 'salary' && record.salaryUnit === 'monthly'" 
          ) {{ record.currencyType }} {{ record.salary }}月 x {{ record.payMonth }}
          template(
            v-if="column.key === 'salary' && record.salaryUnit === 'yearly'" 
          ) {{ record.currencyType }} {{ record.salary }}年

          template(
            v-if="column.key === 'expectCommission'" 
          ) {{ record.commissionCurrencyType }} {{ record.expectCommission }}

        template(#summary)
          a-table-summary(fixed)
            a-table-summary-row
              a-table-summary-cell 汇总：
              a-table-summary-cell(class="bold" :col-span="11") 
                div 总数: {{ dataList.length }}
                div 申请开票总额: {{ dataListSummary.applyInvoiceAmount }}
                div 实际开票总额: {{ dataListSummary.confirmedInvoiceAmount }}
                div 申请回款总额: {{ dataListSummary.applyStatementAmount }}
                div 实际回款总额: {{ dataListSummary.confirmedStatementAmount }}

</template>
<style lang="scss" scoped>
.ca-statistics-page {
  section.statistics-search-filter{
    background-color: #fff;
    padding: 24px 16px 0;
    border-radius: 8px;
    margin-bottom: 16px;

    .search-filter {
      display: flex;
      margin: 0 -8px;
      justify-content: flex-start;

      >* {
        padding: 0 8px;
        width: 33%;
      }

      :deep(.ant-form-item) {
        flex-wrap: nowrap;
      }

      :deep(.ant-select-selector) {
        background-color: #f9f9f9;
        border-color: transparent;
      }

      :deep(.ant-picker) {
        background-color: #f9f9f9 !important;
      }

      :deep(.ant-select-selection-item) {
        color: #FF9111;
      }

      :deep(label) {
        background-color: #f9f9f9;
        padding-left: 8px;
      }
    }
  }

  .pointer {
    cursor: pointer;
  }

  .pagination {
    padding: 16px;
    background: white;
    text-align: right;
  }

  .bold {
    font-weight: bold;
  }
}
</style>