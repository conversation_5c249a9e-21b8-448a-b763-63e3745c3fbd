<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getStat, getStatDetail, getCustomerCountHeader, getPositionCountHeader } from '@/api/stat'
import dayjs from 'dayjs'
import { useWinHeightStore } from '@/store/winHeight.store'
import { getDateRanges } from '@/utils/util'

const winHeightStore = useWinHeightStore()

const noView = ['endTime', 'startTime', 'companyUser']
const queryParams = reactive({
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  dateType: 1
})
const filter = reactive({
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,
  showDetailed: false,
  detailLoading: false
})

const columnsList = ref([])
const dataList = ref([])
const dataListSummary = reactive({
  info: {}
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: true
})

const detailTitle = ref('')
const detailHeader = ref([] as any[])
const detailList = ref([])

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}

function handleTableChange(pagination: any, filters: any, sorter: any, { currentDataSource }: any) {
  dataListSummary.info = getSummary(currentDataSource)
}

function getSummary(dataset: any) {
  const summary: any = {}
  const customerCounter = new Set()
  const jobCounter = new Set()

  dataset.forEach((item: any) => {
    Object.keys(item).forEach((key: any) => {
      if (summary[key] !== undefined && !isNaN(item[key])) {
        summary[key] = Number(item[key]) + Number(summary[key])
      } else {
        summary[key] = item[key]
      }
    })
  })
  return summary
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const res = await getStat('bdWork', {
      endTime,
      startTime,
      // endTime: '2023-02-16',
      // startTime: '2022-02-14',
      ids: []
    })



    const summary: any = {}

    const bdCounter = new Set()
    const list = res.data.rows.map((item: any) => {
      const o:any = {}
      item.columns.forEach((row: any) => {

        // 如果是客户，将客户加入到set中， 用于统计数量和筛选。
        if (row.headKey === 'companyUser') {
          bdCounter.add(row.value)
        }

        o[row.headKey] = row.value
        if (summary[row.headKey] !== undefined && !isNaN(row.value)) {
          summary[row.headKey] =  Number(row.value) + Number(summary[row.headKey])
        } else {
          summary[row.headKey] = row.value
        }
        o[row.headKey + 'Query'] = row.query
      })

      return o
    })

    const heads = res.data.heads.map((item: any) => {
      const filterOptions = { filters: null as any, onFilter: null as any, filterSearch: true }

      if (item.headKey === 'companyUser') {
        filterOptions.filters = []
        bdCounter.forEach((companyUser: any) => {
          filterOptions.filters.push({
            text: companyUser,
            value: companyUser
          })
        })
        filterOptions.onFilter = (value: any, record: any) => record.companyUser === value
      }

      return {
        ...filterOptions,
        title: item.headName,
        dataIndex: item.headKey,
        key: item.headKey,
        sorter: noView.includes(item.headKey) ? false : (a: any, b: any) => a[item.headKey] - b[item.headKey],
      }
    })

    columnsList.value = heads

    // console.log('heads:', heads)

    summary.companyUser = '汇总'
    dataListSummary.info = summary

    dataList.value = list

    status.searchLoading = false
  } catch (err) {
    status.searchLoading = false
  }
}
getList()

let curQueryStr = ''
let curActionKey = ''
// 显示报表明细
const handleClickRow = async (column: any, record: any) => {
  const queryStr = record[column.key + 'Query']
  curQueryStr = queryStr

  curActionKey = column.key

  detailTitle.value = record.companyUser + '-' + column.title
  pagination.current = 1
  pagination.pageSize = 20
  pagination.total = 0
  status.showDetailed = true

  const res = await getDetail(queryStr)

  pagination.total = res.total

  detailList.value = res.list
  detailHeader.value = res.header
}

// 报表明细分野
const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize

  const res = await getDetail(curQueryStr)

  pagination.total = res.total

  detailList.value = res.list
  detailHeader.value = res.header
}

// 获取报表明细
const getDetail = async (queryStr: string) => {
  status.detailLoading = true

  try {
    const res = await getStatDetail('bdWork', {
      queryStr,
      // queryStr: 'function=workload.recommendPmCount,companyId=1,esSearchName=assignee,startTime=2023-02-01,endTime=2023-02-28,id=1',
      current: pagination.current,
      size: pagination.pageSize,
    })

    let header: any = {}
    let list = []

    if (curActionKey === 'customerCount') {
      header = await getCustomerCountHeader()

      list = res.data.objects.map((item: any) => {
        return {
          ...item,
          createDate: dateFormat(item.createTime)
        }
      })
    } else {
      header = await getPositionCountHeader()

      list = res.data.objects.map((item: any) => {

        let bdNmae = ''
        item.jobRequirement.properties.forEach((row: any) => {
          if (row.key === 'bd') {
            bdNmae = row.valueName + ','
          }
        })

        bdNmae.substring(0,bdNmae.length - 1)

        return {
          processId: item.jobRequirement.id,
          processName: item.jobRequirement.processName,
          id: item.customer.id,
          customerFullName: item.customer.customerFullName,
          companyUserStr: bdNmae,
          pmName: item.pmName,
          createDate: dateFormat(item.createTime),
          pmCount: item.pmCount,
          interviewCount: item.interviewCount,
          offerCount: item.offerCount,
          offer: item.offer,
          hiredCount: item.hiredCount,
          hired: item.hired,
          keepCount: item.keepCount,
          keep: item.keep,
        }
      })
    }

    status.detailLoading = false

    return {
      header: header.data,
      list,
      total: res.data.total
    }
  } catch (err) {
    status.detailLoading = false
    return {
      header: [],
      list: [],
      total: 0
    }
  }
}

</script>
<template lang="pug">
.bd-statistics-page
  section.statistics-search-filter
    .search-filter
      a-form-item(label="时间")
        a-range-picker(
          v-model:value="queryParams.date"
          :bordered="false"
          :presets="filter.ranges"
          @change="(value:any) => { handleFilterChange('dateType', value) }"
        )

  //-列表
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 800, y: winHeightStore.value - 350 }" 
        :pagination="false"
        rowClassName="clickable"
        @change="handleTableChange"
      )
        template(#bodyCell="{ text, record, index, column }")
          .pointer(
            v-if="!noView.includes(column.key)"
            @click="handleClickRow(column, record)"
          ) {{ text }}

        template(#summary)
          a-table-summary(fixed)
            a-table-summary-row
              a-table-summary-cell {{ dataListSummary.info.startTime }}
              a-table-summary-cell {{ dataListSummary.info.endTime }}
              a-table-summary-cell 汇总
              a-table-summary-cell {{ dataListSummary.info.customerCount }}
              a-table-summary-cell {{ dataListSummary.info.positionCount }}
              
  //- 报表明细
  a-modal(
    v-model:open="status.showDetailed"
    :title="detailTitle"
    width="90%"
    :footer="null"
    @ok="() => status.showDetailed = false"
  )
    a-spin(:spinning="status.detailLoading")
      a-table(
        :scroll="{y: winHeightStore.value - 430 }" 
        :columns="detailHeader" 
        :data-source="detailList" 
        :pagination="pagination"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(v-if="column.dataIndex === 'customerFullName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.id }}
              .pointer {{ text }}
          template(v-if="column.dataIndex === 'processName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.processId }}
              .pointer {{ text }}
          template(v-if="column.dataIndex === 'pmName'")
            a-popover(placement="top")
              template(#content)
                div {{ text }}
              .ellipsis {{ text }}

         
</template>
<style lang="scss" scoped>
.bd-statistics-page {
  section.statistics-search-filter{
    background-color: #fff;
    padding: 24px 16px 0;
    border-radius: 8px;
    margin-bottom: 16px;

    .search-filter {
      display: flex;
      margin: 0 -8px;
      justify-content: flex-start;

      >* {
        padding: 0 8px;
        width: 33%;
      }

      :deep(.ant-form-item) {
        flex-wrap: nowrap;
      }

      :deep(.ant-select-selector) {
        background-color: #f9f9f9;
        border-color: transparent;
      }

      :deep(.ant-picker) {
        background-color: #f9f9f9 !important;
      }

      :deep(.ant-select-selection-item) {
        color: #FF9111;
      }

      :deep(label) {
        background-color: #f9f9f9;
        padding-left: 8px;
      }
    }
  }

  .pagination {
    padding: 16px;
    background: white;
    text-align: right;
  }
}

.pointer {
  cursor: pointer;
}

.ellipsis {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>