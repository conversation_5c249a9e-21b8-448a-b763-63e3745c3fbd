<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2023-02-08 16:23:13
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-03-14 11:46:58
 * @FilePath: /itp-operation-web/src/views/statistics/ca-statistics.vue
* @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getTimeQuantum } from '@/api/dictionary'
import { getCompanyOnboardUsers } from '@/api/system/roles'
import { getStat, getStatDetail, getStatDetailHeader } from '@/api/stat'
import dayjs, { Dayjs } from 'dayjs';
import { getJobRequireHeaderIndex } from '@/api/job'
import { useRouter } from "vue-router"
import { useWinHeightStore } from '@/store/winHeight.store'
import { message } from 'ant-design-vue'
import { getCompanyDepartment } from '@/api/system/department'
import { getDepartmentUserList } from '@/api/system/users'
import { getDateRanges } from '@/utils/util'
import { useUserStore } from '@/store/user.store'
import FilterItem from '@/components/ui/filter-item.vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import JobPriority from '@/components/app/job-priority.vue'
import { getCompanyList } from '@/api/platform';
import { getMySubordinates } from '@/api/user'
import { onMounted } from 'vue';

const winHeightStore = useWinHeightStore()
const router = useRouter()
const userStore = useUserStore()
const noView = ['endTime', 'startTime', 'companyUser']
const isAdmin = ref(userStore.isAdmin)
const companyId = ref(userStore.companyId)

const queryParams = reactive({
  department: [] as number[],
  userIds: [] as number[],
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  statsTalentCount: false,
})
const filter = reactive({
  CAUsers: [] as any,
  timeQuantum: [] as any,
  ranges: getDateRanges(),
  departmentTreeData: [] as any[],
  companies: [] as any[]
})

const status = reactive({
  searchLoading: false,
  fetching: false,
  showDetailed: false,
  detailLoading: false,
  showTalentDetail: false
})

const columnsList = ref([])
const dataList = ref([])
const queryUserIds = ref([])
const dataListSummary = reactive({
  info: {}
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: true
})

const detailTitle = ref('')
const detailHeader = ref<any[]>([])
const detailList = ref<any[]>([])
const talentId = ref<number>()
const jobId = ref<number>()

const initFilter = async () => {
  const [dictCAUsers, dictTimeQuantum, dictCompanyList, dictDepartment] = await Promise.all([getCompanyOnboardUsers('ca', companyId.value), getTimeQuantum(), getCompanyList(), getCompanyDepartment(companyId.value)])
  filter.CAUsers = dictCAUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
  filter.timeQuantum = dictTimeQuantum.data
  filter.companies = dictCompanyList.data
  filter.departmentTreeData = _processDepartmentData(dictDepartment.data)
}
initFilter()

interface Department {
  id: number,
  companyId: number,
  parentId: number,
  deptName: string,
  userId: number,
  children?: Department[],
  title?: string,
  value: number,
}

// async function fetchCompanyDepartment() {
//   try {
//     const res = await getCompanyDepartment()
//     filter.departmentTreeData = _processDepartmentData(res.data)
//   } catch (err: any) {
//     message.error(err.message)
//   }
// }
// fetchCompanyDepartment()

function _processDepartmentData(departments: Department[]) {
  const deptMap = new Map<number, Department>()
  const deptTree = new Array<Department>()

  // 初始化数据结构，并更具id创建索引
  departments.forEach((dept) => {
    dept.children = []
    dept.title = dept.deptName
    dept.value = dept.id
    deptMap.set(dept.id, dept)

    // 如果父级部门ID是0， 则表示上级无企业
    if (dept.parentId === 0) deptTree.push(dept)
  })

  // 组装父子关系
  deptMap.forEach((dept, key) => {
    if (dept.parentId) {
      const parent = deptMap.get(dept.parentId)
      parent?.children?.push(dept)
    }
  })

  return deptTree
}

async function fetchDeparmentUserList(departmentIds: any[]) {
  try {
    const pArr = departmentIds.map((id: number) => {
      return getDepartmentUserList(id, { current: 1, size: 100 })
    })

    const [...res] = await Promise.all(pArr)

    const ids: Set<number> = new Set()
    res.forEach((item: any) => {
      item.data.users.forEach((row: any) => {
        ids.add(row.id)
      })
    })

    return Array.from(ids)
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleStatMethodChange(value: any) {
  getList()
}

const handleFilterChange = async (type: string, value: any) => {
  if (type === 'company') {
    queryParams.userIds = []
    queryParams.department = []
    const [dictCaUsers, dictDepartment] = await Promise.all([getCompanyOnboardUsers('ca', companyId.value), getCompanyDepartment(companyId.value)])
    filter.CAUsers = dictCaUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    filter.departmentTreeData = _processDepartmentData(dictDepartment.data)
  }

  if (type === 'department') {
    const res = await fetchDeparmentUserList(value)
    queryParams.userIds = res || []
  }

  if (type === 'userIds') {
    queryParams.department = []
  }

  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const res = await getStat('workload', {
      startTime,
      endTime,
      ids: isAdmin.value ? queryParams.userIds : queryUserIds.value,
      statsTalentCount: queryParams.statsTalentCount
    }, companyId.value)

    const heads = res.data.heads.map((item: any) => {
      return {
        title: item.headName,
        dataIndex: item.headKey,
        key: item.headKey,
        sorter: noView.includes(item.headKey) ? false : (a: any, b: any) => a[item.headKey] - b[item.headKey],
      }
    })

    columnsList.value = heads

    const summary: any = {}
    const list = res.data.rows.map((item: any) => {
      const o: any = {}
      item.columns.forEach((row: any) => {
        o[row.headKey] = row.value
        if (summary[row.headKey] !== undefined && !isNaN(row.value)) {
          summary[row.headKey] = Number(row.value) + Number(summary[row.headKey])
        } else {
          summary[row.headKey] = row.value
        }
        o[row.headKey + 'Query'] = row.query
      })

      return o
    })

    // console.log('heads:', heads)
    summary.companyUser = '汇总'
    dataListSummary.info = summary
    // list.push(summary)

    dataList.value = list
    status.searchLoading = false
  } catch (err) {
    status.searchLoading = false
  }
}

const detailQuery = reactive({
  query: '',
  key: ''
})

// 处理表格数据的点击事件
const handleClickRow = async (column: any, record: any) => {
  const query = record[column.key + 'Query']
  detailQuery.query = query
  detailQuery.key = column.key
  detailTitle.value = record.companyUser + '-' + column.title
  pagination.current = 1
  pagination.pageSize = 20

  status.showDetailed = true
  const res = await getDetail(detailQuery, pagination)

  detailHeader.value = res.header
  detailList.value = res.list
  pagination.total = res.total
}

const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize

  const res = await getDetail(detailQuery, pagination)

  detailHeader.value = res.header
  detailList.value = res.list
  pagination.total = res.total
}


/**
 * 这里处理的是CA统计报表中的职位统计一列，然后返回表格所需要的数据配置。
 * @param listData 
 */
async function processPositionCount(listData: any) {
  const { data } = await getJobRequireHeaderIndex()

  const nData = data.filter((item: any) => {
    return !['bd', 'ca'].includes(item.key)
  })

  const header = nData.map((item: any, index: number) => {
    const key = item.key ? item.key.toLocaleLowerCase() : `key_${index}`
    const column = {
      ...item,
      title: item.name,
      key: key,
      dataIndex: key
    }
    if (item.key === 'jobTitle') return Object.assign({}, column, { fixed: 'left', width: 200 })
    else return column
  })

  header.unshift({ title: '', key: 'key_priority', dataIndex: 'key_priority', fixed: 'left', width: 60 })
  header.splice(2, 0, { title: '开始时间', key: 'startDate', dataIndex: 'positionStartDate' })

  const list = listData.objects.map((item: any) => {
    let o: any = {}
    item.properties.forEach((row: any) => {
      const key = row.key.toLocaleLowerCase()
      if (o[key] === undefined) {
        o[key] = row.valueName
      } else {
        o[key] = `${o[key]},${row.valueName}`
      }
    })
    o['customerName'] = item.customerName
    o['id'] = item.id
    o['priority'] = item?.priority
    o['positionStartDate'] = item.positionStartDate
    return o
  })

  return {
    header,
    list,
    total: listData.total
  }
}

/**
 * 这里处理的是“addInterviewCount”和“currentInterviewCount”两个报表的数据
 * 这两个报表列取的task数据是面试task的数据，不是主流程的数据，因此会存在一个parentTask的数据。 
 * @param detailQuery 
 * @param pagination 
 */
async function processInterviewCount(listData: any) {


  const sortedList = listData.objects.sort((a: any, b: any) => {
    return a.talent.id - b.talent.id
  })

  const header = await getStatDetailHeader()

  let prevRecord: any = null
  const list = sortedList.map((item: any) => {

    let rowspan = 1

    // 对面试记录进行排序，并简历TaskId和第几轮面试的map
    const interviewList = item.childTasks.sort((a: any, b: any) => {
      const aTime = dayjs(a.task.localVariables.startDate).valueOf()
      const bTime = dayjs(b.task.localVariables.startDate).valueOf()
      return aTime - bTime
    })
    const interviewIndexMap = new Map()
    interviewList.forEach((item: any, index: number) => {
      interviewIndexMap.set(item.task.id, index + 1)
    })

    // 处理当前节点最新情况的数据。
    let latestTaskName = item.parentLatestTask && item.parentLatestTask.task.name
    if (latestTaskName === '面试') {
      const interviewTask = interviewList[interviewList.length - 1]
      console.log(interviewTask, '最新的面试')
      latestTaskName = `${latestTaskName} - ${item.childTasks.length}面 ${interviewTask.task.localVariables.interviewResult ? '· 通过' : ''}`
    }

    const tableRecord = {
      span: rowspan,
      talentId: item.talent.id,
      talentName: item.talent.realName,
      customerFullName: item.customer && item.customer.customerFullName,
      customerId: item.customer && item.customer.id,
      jobName: item.jobRequirement && item.jobRequirement.processName,
      jobId: item.jobRequirement && item.jobRequirement.id,
      interviewRecord: {
        time: item.task.localVariables.startDate,
        interviewType: item.task.localVariables.interviewType,
        result: item.task.localVariables.interviewResult,
        index: interviewIndexMap.get(item.task.id)
      },
      interviewCreateTime: dayjs(item.task.createTime).format('YYYY-MM-DD HH:mm'),
      latestTaskName: latestTaskName,
      latestTaskId: item.parentLatestTask && item.parentLatestTask.task.id,
      caName: item.parentLatestTask && item.parentLatestTask.suggestUser.realName,
      caId: item.parentLatestTask && item.parentLatestTask.suggestUser.id,
      pmName: item.pmName,
      processStartTime: dateFormat(item.process.startTime, 'YYYY-MM-DD HH:mm')
    }

    // 判断是否和上一条是相同的人，并且在相同的Job里。
    // 这里使用jobrequirementId可能有坑，这个要求是一个人不能在同一个job里出现两次。
    if (prevRecord && prevRecord.talentId === tableRecord.talentId && prevRecord.jobId === tableRecord.jobId) {
      // 表示跨row
      prevRecord.span += 1
      tableRecord.span = 0
    } else {
      // 记录下本次记录，用于下一次进行对比
      prevRecord = tableRecord
    }

    return tableRecord
  })

  return {
    header: header.data,
    list,
    total: listData.total
  }
}

async function processOtherCount(listData: any) {
  const header = await getStatDetailHeader()
  // 删除第四列，单条面试记录一列
  header.data.splice(3, 2)

  const list = listData.objects.map((item: any) => {

    const interviewList = item.childTasks.sort((a: any, b: any) => {
      const aTime = dayjs(a.task.localVariables.startDate).valueOf()
      const bTime = dayjs(b.task.localVariables.startDate).valueOf()
      return aTime - bTime
    })

    let latestTaskName = item.latestTask && item.latestTask.name
    if (latestTaskName === '面试') {
      const interviewTask = interviewList[interviewList.length - 1]
      latestTaskName = `${latestTaskName} - ${item.childTasks.length}面 ${interviewTask.task.localVariables.interviewResult ? '· 通过' : ''}`
    }

    return {
      talentId: item.talent.id,
      talentName: item.talent.realName,
      customerFullName: item.customer && item.customer.customerFullName,
      customerId: item.customer && item.customer.id,
      jobName: item.jobRequirement && item.jobRequirement.processName,
      jobId: item.jobRequirement && item.jobRequirement.id,
      latestTaskName: latestTaskName,
      latestTaskId: item.latestTask && item.latestTask.id,
      caName: item.suggestUser && item.suggestUser.realName,
      caId: item.suggestUser && item.suggestUser.id,
      pmName: item.pmName,
      processStartTime: dateFormat(item.process.startTime, 'YYYY-MM-DD HH:mm')
    }
  })

  return {
    header: header.data,
    list,
    total: listData.total
  }
}

async function getDetail(detailQuery: { query: string, key: string }, pagination: any) {
  status.detailLoading = true
  let result: { header: any[], list: any[], total: number } = { header: [], list: [], total: 0 }

  try {
    // 通过点击进来的query，来获取数据，query是通过数据返回来的查询参数。
    const res = await getStatDetail('workload', {
      queryStr: detailQuery.query,
      current: pagination.current,
      size: pagination.pageSize,
    }, companyId.value)

    if (detailQuery.key === 'positionCount') {
      console.log('processPositionCount')
      result = await processPositionCount(res.data)
    } else if (detailQuery.key === 'addInterviewCount' || detailQuery.key === 'currentInterviewCount') {
      console.log('interViewCount')
      result = await processInterviewCount(res.data)
    } else {
      console.log('otherCount')
      result = await processOtherCount(res.data)
    }
  } catch (err: any) {
    message.error(err.message)
  }

  status.detailLoading = false
  return result
}

async function getSubordinateUserIds () {
  try {
    const res = await getMySubordinates()
    queryUserIds.value = res.data.userIds
  } catch (err: any) {
    message.error(err.message)
  }
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      if (detailQuery.key !== 'positionCount') {
        status.showTalentDetail = true
        talentId.value = record.talentId
        jobId.value = record.jobId
      } else {
        status.showDetailed = false
        router.push({
          path: `/job/${record.id}/detail`
        })
      }
    }
  }
}

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

function daysFrom(date: string) {
  const now = dayjs()
  const d = dayjs(date)
  return now.diff(d, 'day')
}

onMounted(async ()=>{
  await getSubordinateUserIds()
  await getList()
})

</script>
<template lang="pug">
.ca-statistics-page
  section.statistics-search-filter
    a-row(:gutter="[16, 16]")
      a-col(:span="4" v-if="isAdmin && userStore.companyId === 1")
        FilterItem(label="")
          a-select(
            v-model:value="companyId"
            placeholder="选择公司"
            @change="(value:any) => { handleFilterChange('company', value) }"
          )
            template(v-for="(item, index) in filter.companies")
              a-select-option(:value="item.id" :label="`${item.companyName}`")
                .fullName {{ item.companyName }}
      a-col(:span="4" v-if="isAdmin")
        FilterItem(label="")
          a-tree-select(
            v-model:value="queryParams.department"
            show-search
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="选择部门"
            allow-clear
            multiple
            tree-default-expand-all
            :max-tag-count="1"
            :tree-data="filter.departmentTreeData"
            @change="(value:any) => { handleFilterChange('department', value) }"
          )
      a-col(:span="4")
        FilterItem(label="")
          a-select(
            allow-clear,
            show-search
            placeholder="选择CA"
            mode="multiple"
            max-tag-count="responsive"
            v-model:value="queryParams.userIds",
            :filter-option="filterOption"
            :dropdownMatchSelectWidth="false",
            :options="filter.CAUsers",
            @change="(value:any) => { handleFilterChange('userIds', value) }"
          )

      a-col(:span="7")
        FilterItem(label="时间")
          a-range-picker(
            v-model:value="queryParams.date"
            style="width: 100%"
            :bordered="false"
            :presets="filter.ranges"
            @change="(value:any) => { handleFilterChange('dateType', value) }"
          )
      a-col(:span="5")
        FilterItem()
          a-radio-group(button-style="solid" v-model:value="queryParams.statsTalentCount" @change="handleStatMethodChange")
            a-radio-button(:value="false") 统计人次
            a-radio-button(:value="true") 统计人数

  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1200, y: winHeightStore.value - 390 }" 
        :pagination="false"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          .pointer(
            v-if="!noView.includes(column.key)"
            @click="handleClickRow(column, record)"
          ) {{ text }}

        template(#summary v-if="isAdmin")
          a-table-summary(fixed)
            a-table-summary-row
              a-table-summary-cell {{ dataListSummary.info.startTime }}
              a-table-summary-cell {{ dataListSummary.info.endTime }}
              a-table-summary-cell 汇总
              a-table-summary-cell {{ dataListSummary.info.recommendPmCount }}
              a-table-summary-cell {{ dataListSummary.info.positionCount }}
              a-table-summary-cell {{ dataListSummary.info.customerCount }}
              a-table-summary-cell {{ dataListSummary.info.interviewCount }}
              a-table-summary-cell {{ dataListSummary.info.addInterviewCount }}
              a-table-summary-cell {{ dataListSummary.info.currentInterviewCount }}
              a-table-summary-cell {{ dataListSummary.info.salaryCount }}
              a-table-summary-cell {{ dataListSummary.info.salaryEndCount }}
              a-table-summary-cell {{ dataListSummary.info.offerCount }}
              a-table-summary-cell {{ dataListSummary.info.inspectCount }}
              a-table-summary-cell {{ dataListSummary.info.hiredCount }}
              a-table-summary-cell {{ dataListSummary.info.keepCount }}
              a-table-summary-cell {{ dataListSummary.info.obsoleteCount }}

          //- dataListSummary.info

  a-modal(
    v-model:visible="status.showDetailed"
    :title="detailTitle"
    width="80%"
    :footer="null"
    @ok="() => status.showDetailed = false"
  )
    a-spin(:spinning="status.detailLoading")
      a-table(
        v-if="detailHeader.length && detailQuery.key !== 'positionCount'"
        :scroll="{ y: winHeightStore.value - 370 }" 
        :columns="detailHeader" 
        :data-source="detailList" 
        :pagination="pagination"
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(v-if="column.dataIndex === 'talentName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.talentId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'customerFullName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.customerId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'jobName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.jobId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'latestTaskName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.latestTaskId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'caName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.caId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'interviewRecord'")
            div {{ record.interviewRecord.index }}面 · {{ record.interviewRecord.interviewType }} {{ record.interviewRecord.result ? ` · ${record.interviewRecord.result}` : ''  }}
            div(style="color:#999;") {{ record.interviewRecord.time }}

      a-table(
        v-if="detailHeader.length && detailQuery.key === 'positionCount'"
        :columns="detailHeader" 
        :data-source="detailList" 
        :scroll="{ x: 1200 }" 
        :pagination="pagination" 
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
        rowKey="id"
      )
        template(#bodyCell="{ column, record }")
          template(v-if="column.dataIndex === 'jobtitle'")
            .position-title {{ record.jobtitle }}
            .customer-name(style="color: #999;") {{ record.customerName }}

          template(v-if="column.dataIndex === 'key_priority'")
            JobPriority(:priority="record.priority")

          template(v-if="column.dataIndex === 'positionStartDate'")
            div {{ record.positionStartDate  }}
            div 距今: {{ daysFrom(record.positionStartDate) }}天

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" :jobId="jobId" @close="status.showTalentDetail = false")

</template>
<style lang="scss" scoped>
.ca-statistics-page {
  section.statistics-search-filter {
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .pointer {
    cursor: pointer;
  }
}
</style>