<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getOffer, getOfferHeader } from '@/api/stat'
import dayjs from 'dayjs'
import { useWinHeightStore } from '@/store/winHeight.store'
import { getDateRanges } from '@/utils/util'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const winHeightStore = useWinHeightStore()
const queryParams = reactive({
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  dateType: 1
})
const filter = reactive({
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,
  showTalentDetail: false
})

const columnsList = ref([])
const dataList = ref([])
const dataListSummary = reactive({
  isCompleteDesc: '汇总',
  monthly: '',
  yearly: '',
  expectCommission: ''
})

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const columns: any = await getOfferHeader()

    columnsList.value = columns.data

    const res = await getOffer({
      endTime,
      startTime,
      // endTime: '2023-02-16',
      // startTime: '2021-12-14',
      ids: []
      // ids: JobRequirements.data
    })

    const summary: any = {
      isCompleteDesc: '汇总',
      monthly: {},
      yearly: {},
      expectCommission: {}
    }

    const list = res.data.objects.map((item: any) => {
      const salaryUnit = item.salaryUnit ? item.salaryUnit : 'monthly'
      const currencyType = item.currencyType ? item.currencyType : 'RMB'
      const commissionCurrencyType = item.commissionCurrencyType ? item.commissionCurrencyType : 'RMB'

      if (summary[salaryUnit][currencyType]) {
        summary[salaryUnit][currencyType] = summary[salaryUnit][currencyType] + Number(item.salary)
      } else {
        summary[salaryUnit][currencyType] = Number(item.salary)
      }

      if (summary.expectCommission[commissionCurrencyType]) {
        summary.expectCommission[commissionCurrencyType] = summary.expectCommission[commissionCurrencyType] + Number(item.expectCommission)
      } else {
        summary.expectCommission[commissionCurrencyType] = Number(item.expectCommission)
      }

      return {
        isCompleteDesc: item.isCompleteDesc,
        talentName: item.talent.realName,
        talentId: item.talent.id,
        jobId: item.jobRequirement.id,
        customerFullName: item.customer && item.customer.customerFullName,
        processName: item.jobRequirement && item.jobRequirement.processName,
        caName: item.suggestUser && item.suggestUser.realName,
        pmName: item.pmName,
        latestTaskName: item.latestTask && item.latestTask.name,
        offerCreateTime: item.task && dateFormat(item.task.createTime),
        salary: item.salary,
        payMonth: item.payMonth,
        expectOnboardingDate: item.expectOnboardingDate,
        onboardingDate: item.onboardingDate,
        overInsuranceDate: item.overInsuranceDate,
        expectCommission: item.expectCommission,
        obsoleteReason: item.obsoleteReason,
        currencyType,
        salaryUnit,
        commissionCurrencyType,
      }
    })
    const monthlyStr = JSON.stringify(summary.monthly)
    const yearlyStr = JSON.stringify(summary.yearly)
    const expectCommissionStr = JSON.stringify(summary.expectCommission)

    dataListSummary.monthly = monthlyStr.replace(/[{}"]/g, '').replace(/[:]/g, ' ');
    dataListSummary.yearly = yearlyStr.replace(/[{}"]/g, '').replace(/[:]/g, ' ');
    dataListSummary.expectCommission = expectCommissionStr.replace(/[{}"]/g, '').replace(/[:]/g, ' ');

    dataList.value = list

    // dataList.value = res.data
    status.searchLoading = false
  } catch (err) {
    console.log(err)
    status.searchLoading = false
  }
}
getList()

const talentDetailParams = reactive({
  talentId: 0,
  jobId: 0
})
function showTalentDetail(talentId:number, jobId: number) {
  status.showTalentDetail = true
  talentDetailParams.talentId = talentId
  talentDetailParams.jobId = jobId
}
function jumpToJobRequirementDetail(jobId: number) {
  router.push(`/job/${jobId}/detail`)
}
</script>
<template lang="pug">
.ca-statistics-page
  section.statistics-search-filter
    .search-filter
      a-form-item(label="时间")
        a-range-picker(
          v-model:value="queryParams.date"
          :bordered="false"
          :presets="filter.ranges"
          @change="(value:any) => { handleFilterChange('dateType', value) }"
        )

  //-列表
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1500, y: winHeightStore.value - 400 }" 
        :pagination="false"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(
            v-if="column.key === 'salary' && record.salaryUnit === 'monthly'" 
          ) {{ record.currencyType }} {{ record.salary }}月 x {{ record.payMonth }}
          template(
            v-if="column.key === 'salary' && record.salaryUnit === 'yearly'" 
          ) {{ record.currencyType }} {{ record.salary }}年

          template(
            v-if="column.key === 'expectCommission'" 
          ) {{ record.commissionCurrencyType }} {{ record.expectCommission }}

          template(v-if="column.key === 'talentName'")
            a(@click="()=> showTalentDetail(record.talentId, record.jobId)") {{ record.talentName  }}
          template(v-if="column.key === 'processName'")
            a(@click="()=> jumpToJobRequirementDetail(record.jobId)") {{ record.processName }}

        template(#summary)
          a-table-summary(fixed)
            a-table-summary-row
              a-table-summary-cell 汇总：
              a-table-summary-cell(class="bold" :col-span="11") 
                div 总数: {{ dataList.length }}
                div 月薪资: {{ dataListSummary.monthly }}
                div 年薪资: {{ dataListSummary.yearly }}
                div 预计佣金汇总: {{ dataListSummary.expectCommission }}

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentDetailParams.talentId" :jobId="talentDetailParams.jobId" @close="status.showTalentDetail = false")

</template>
<style lang="scss" scoped>
.ca-statistics-page {
  section.statistics-search-filter {
    background-color: #fff;
    padding: 24px 16px 0;
    border-radius: 8px;
    margin-bottom: 16px;

    .search-filter {
      display: flex;
      margin: 0 -8px;
      justify-content: flex-start;

      >* {
        padding: 0 8px;
        width: 33%;
      }

      :deep(.ant-form-item) {
        flex-wrap: nowrap;
      }

      :deep(.ant-select-selector) {
        background-color: #f9f9f9;
        border-color: transparent;
      }

      :deep(.ant-picker) {
        background-color: #f9f9f9 !important;
      }

      :deep(.ant-select-selection-item) {
        color: #FF9111;
      }

      :deep(label) {
        background-color: #f9f9f9;
        padding-left: 8px;
      }
    }
  }

  .pointer {
    cursor: pointer;
  }

  .pagination {
    padding: 16px;
    background: white;
    text-align: right;
  }

  .bold {
    font-weight: bold;
  }
}
</style>