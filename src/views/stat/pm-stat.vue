<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getStat, getJobRequirements, getStatDetail, getStatDetailHeader } from '@/api/stat'
import dayjs from 'dayjs'
import { useWinHeightStore } from '@/store/winHeight.store'
import { getDateRanges } from '@/utils/util'

import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import { message } from 'ant-design-vue'
import JobPriority from "@/components/app/job-priority.vue"


const winHeightStore = useWinHeightStore()

const noView = ['endTime', 'startTime', 'jobRequirement', 'customer', 'pm']

const queryParams = reactive({
  date: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
  dateType: 1
})

const filter = reactive({
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,
  fetching: false,
  showDetailed: false,
  detailLoading: false,
  showTalentDetail: false
})

const columnsList = ref<any[]>([])
const dataList = ref([])
const dataListSummary = reactive({ info: {} })

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: true
})

const detailTitle = ref('')
const detailHeader = ref([] as any[])
const detailList = ref([])
const talentId = ref<number>()
const jobId = ref<number>()

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}

function handleTableChange(pagination: any, filters: any, sorter: any, { currentDataSource }: any) {
  dataListSummary.info = getSummary(currentDataSource)
}

function getSummary(dataset: any) {
  const summary: any = {}
  const customerCounter = new Set()
  const jobCounter = new Set()

  dataset.forEach((item: any) => {
    Object.keys(item).forEach((key: any) => {

      if (key === 'customer') { customerCounter.add(item[key]) }
      if (key === 'jobRequirement') { jobCounter.add(item[key]) }

      if (summary[key] !== undefined && !isNaN(item[key])) {
        summary[key] = Number(item[key]) + Number(summary[key])
      } else {
        summary[key] = item[key]
      }
    })
  })
  summary.customerObjectCount = customerCounter.size
  summary.jobObjectCount = jobCounter.size
  return summary
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    const timeRange = { endTime, startTime }
    const JobRequirements = await getJobRequirements(timeRange)
    const res = await getStat('jobs', { endTime, startTime, ids: JobRequirements.data })

    // 处理数据部分，并统计汇总
    const summary: any = {}

    // 统计客户,职位，PM人员
    const customerCounter = new Set()
    const jobCounter = new Set()
    const pmCounter = new Set()

    // 处理并汇总数据，提取列中所有的客户做为filter
    const list = res.data.rows.map((item: any) => {
      const rowData: any = {}
      item.columns.forEach((row: any) => {

        // 如果是客户，将客户加入到set中， 用于统计数量和筛选。
        if (row.headKey === 'customer') {
          rowData['customerObject'] = row.object
          customerCounter.add(row.value)
        }

        // 如果是职位，加入到职位的set中，用于统计数量。
        if (row.headKey === 'jobRequirement') {
          rowData['jobObject'] = row.object
          jobCounter.add(row.value)
        }

        // 如果是PM，加入到PM的set中，用于统计数量。 和筛选。
        if (row.headKey === 'pm') {
          row.object.forEach((pm: any) => {
            pmCounter.add(pm.realName)
          })
        }

        rowData[row.headKey] = row.value

        if (summary[row.headKey] !== undefined && !isNaN(row.value)) {
          summary[row.headKey] = Number(row.value) + Number(summary[row.headKey])
        } else {
          summary[row.headKey] = row.value
        }

        rowData[row.headKey + 'Query'] = row.query
      })

      return rowData
    }).sort((a: any, b: any) => {
      return a.customerObject.id - b.customerObject.id
    })

    // 处理表头部分
    const columnConfig: any[] = []
    columnConfig.push({key: 'jobPriority', title: '优先级', width: 80})
    res.data.heads.forEach((item: any) => {

      if(item.headKey === 'passInterviewCount') return

      const filterOptions = { filters: null as any, onFilter: null as any, filterSearch: true }
      let width = null
      if (item.headKey === 'customer') {
        filterOptions.filters = []
        customerCounter.forEach((customer: any) => {
          filterOptions.filters.push({
            text: customer,
            value: customer
          })
        })
        filterOptions.onFilter = (value: any, record: any) => record.customer === value
      }

      if (item.headKey === 'pm') {
        filterOptions.filters = []
        pmCounter.forEach((pm: any) => {
          filterOptions.filters.push({
            text: pm,
            value: pm
          })
        })
        filterOptions.onFilter = (value: any, record: any) => record.pm.split(',').includes(value)
      }

      // 不显示开始时间和结束时间，因为这个信息在筛选器中已经存在。
      if (item.headKey === 'startTime' || item.headKey === 'endTime') {
        return
      }

      if (item.headKey === 'jobStartTime') {
        width = '120px'
      }

      columnConfig.push({
        title: item.headName,
        dataIndex: item.headKey,
        key: item.headKey,
        ...filterOptions,
        sorter: (() => {
          if (noView.includes(item.headKey)) return false
          else if (item.headKey === 'jobStartTime') return (a: any, b: any) => { return new Date(a[item.headKey]).getTime() - new Date(b[item.headKey]).getTime() }
          else return (a: any, b: any) => a[item.headKey] - b[item.headKey]
        })(),
        width
      })
    })

    columnsList.value = columnConfig
    summary.customerObjectCount = customerCounter.size
    summary.jobObjectCount = jobCounter.size
    dataList.value = list
    dataListSummary.info = summary
    status.searchLoading = false
  } catch (err: any) {
    message.error(err.message)
    status.searchLoading = false
  }
}
getList()

let curQueryStr = ''
let curActionKey = ''
// 显示报表明细
const handleClickRow = async (column: any, record: any) => {
  const queryStr = record[column.key + 'Query']
  curQueryStr = queryStr

  curActionKey = column.key

  detailTitle.value = record.jobRequirement + '-' + column.title
  pagination.current = 1
  pagination.pageSize = 20
  pagination.total = 0
  status.showDetailed = true

  const res = await getDetail(queryStr)
  pagination.total = res.total
  detailList.value = res.list
  detailHeader.value = res.header
}

// 报表明细分野
const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  const res = await getDetail(curQueryStr)
  pagination.total = res.total
  detailList.value = res.list
  detailHeader.value = res.header
}

// 获取报表明细
const getDetail = async (queryStr: string) => {
  status.detailLoading = true

  try {
    const res = await getStatDetail('jobs', {
      queryStr,
      current: pagination.current,
      size: pagination.pageSize,
    })

    const header = await getStatDetailHeader()

    const list = res.data.objects.map((item: any) => {

      let latestTaskName = item.latestTask && item.latestTask.name
      let latestTaskId = item.latestTask && item.latestTask.id
      let caName = item.suggestUser && item.suggestUser.realName
      let caId = item.suggestUser && item.suggestUser.id

      if (curActionKey === 'addInterviewCount' || curActionKey === 'currentInterviewCount') {
        latestTaskName = item.parentLatestTask && item.parentLatestTask.task.name
        latestTaskId = item.parentLatestTask && item.parentLatestTask.task.id

        caName = item.parentLatestTask && item.parentLatestTask.suggestUser.realName
        caId = item.parentLatestTask && item.parentLatestTask.suggestUser.id
      }

      return {
        talentId: item.talent.id,
        talentName: item.talent.realName,
        customerFullName: item.customer && item.customer.customerFullName,
        customerId: item.customer && item.customer.id,
        jobName: item.jobRequirement && item.jobRequirement.processName,
        jobId: item.jobRequirement && item.jobRequirement.id,
        latestTaskName,
        latestTaskId,
        caName,
        caId,
        pmName: item.pmName,
        processStartTime: dateFormat(item.process.startTime, 'YYYY-MM-DD HH:mm')
      }
    })

    status.detailLoading = false

    return {
      header: header.data,
      list,
      total: res.data.total
    }
  } catch (err) {
    status.detailLoading = false
    return {
      header: [],
      list: [],
      total: 0
    }
  }
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      status.showTalentDetail = true
      talentId.value = record.talentId
      jobId.value = record.jobId
    }
  }
}

function getTimeSpan(time: string) {
  const now = dayjs()
  const startTime = dayjs(time)
  const diff = now.diff(startTime, 'day')
  return diff + '天'
}

</script>
<template lang="pug">
.ca-statistics-page
  section.statistics-search-filter
    .search-filter
      a-form-item(label="时间")
        a-range-picker(
          v-model:value="queryParams.date"
          :bordered="false"
          :presets="filter.ranges"
          @change="(value:any) => { handleFilterChange('dateType', value) }"
        )

  //-列表
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1500, y: winHeightStore.value - 380 }" 
        :pagination="false"
        rowClassName="clickable",
        @change="handleTableChange"
      )
        template(#bodyCell="{ text, record, index, column }")

          template(v-if="column.key === 'jobPriority'")
            JobPriority(:priority="record.jobObject.priority")

          template(v-else-if="column.key === 'jobStartTime'")
            span(v-if="record.jobStartTime") {{ record.jobStartTime }} <br>距今: {{ getTimeSpan(record.jobStartTime) }}

          template(v-else)
            .pointer(
              v-if="!noView.includes(column.key)"
              @click="handleClickRow(column, record)"
            ) {{ text }}

        template(#summary)
          a-table-summary(fixed)
            a-table-summary-row
              a-table-summary-cell 汇总
              a-table-summary-cell {{ dataListSummary.info.jobObjectCount }}
              a-table-summary-cell {{ dataListSummary.info.customerObjectCount }}
              a-table-summary-cell -
              a-table-summary-cell -
              a-table-summary-cell {{ dataListSummary.info.recommendPmCount }}
              a-table-summary-cell {{ dataListSummary.info.customerCount }}
              a-table-summary-cell {{ dataListSummary.info.interviewCount }}
              a-table-summary-cell {{ dataListSummary.info.addInterviewCount }}
              a-table-summary-cell {{ dataListSummary.info.currentInterviewCount }}
              //- a-table-summary-cell {{ dataListSummary.info.passInterviewCount }}
              a-table-summary-cell {{ dataListSummary.info.salaryCount }}
              a-table-summary-cell {{ dataListSummary.info.offerCount }}
              a-table-summary-cell {{ dataListSummary.info.inspectCount }}
              a-table-summary-cell {{ dataListSummary.info.hiredCount }}
              a-table-summary-cell {{ dataListSummary.info.keepCount }}
              a-table-summary-cell {{ dataListSummary.info.obsoleteCount }}

  //- 报表明细
  a-modal(
    v-model:open="status.showDetailed"
    :title="detailTitle"
    width="900px"
    :footer="null"
    @ok="() => status.showDetailed = false"
  )
    a-spin(:spinning="status.detailLoading")
      a-table(
        :scroll="{ y: winHeightStore.value - 370 }" 
        :columns="detailHeader" 
        :data-source="detailList" 
        :pagination="pagination"
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(v-if="column.dataIndex === 'talentName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.talentId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'customerFullName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.customerId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'jobName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.jobId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'latestTaskName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.latestTaskId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'caName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.caId }}
              .pointer {{ text }}

  //- 人才详情
  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" :jobId="jobId" @close="status.showTalentDetail = false")
</template>
<style lang="scss" scoped>
.ca-statistics-page {
  section.statistics-search-filter {
    background-color: #fff;
    padding: 24px 16px 0;
    border-radius: 8px;
    margin-bottom: 16px;

    .search-filter {
      display: flex;
      margin: 0 -8px;
      justify-content: flex-start;

      >* {
        padding: 0 8px;
        width: 33%;
      }

      :deep(.ant-form-item) {
        flex-wrap: nowrap;
      }

      :deep(.ant-select-selector) {
        background-color: #f9f9f9;
        border-color: transparent;
      }

      :deep(.ant-picker) {
        background-color: #f9f9f9 !important;
      }

      :deep(.ant-select-selection-item) {
        color: #FF9111;
      }

      :deep(label) {
        background-color: #f9f9f9;
        padding-left: 8px;
      }
    }
  }

  .pointer {
    cursor: pointer;
  }

  .pagination {
    padding: 16px;
    background: white;
    text-align: right;
  }
}
</style>