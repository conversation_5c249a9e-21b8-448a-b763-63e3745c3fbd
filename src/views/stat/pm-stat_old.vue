<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTimeQuantum, getJobStatus } from '@/api/dictionary'
import { getStat, getStatDetail, getStatDetailHeader } from '@/api/stat'
import { getJobRequireSearch, POSITION_STATUS } from '@/api/job'
import dayjs from 'dayjs'
import { useWinHeightStore } from '@/store/winHeight.store'

import TalentDetailModal from '@/components/app/talent-detail-modal.vue'

const winHeightStore = useWinHeightStore()

const getDateRanges = () => {
  const curDate = dayjs().subtract(1, 'day')
  
  return {
    '昨天': [curDate, curDate],
    '本周': [
      dayjs().startOf('week'),
      dayjs().endOf('week')
    ],
    '上周': [
      dayjs().startOf('week').subtract(1, 'day').startOf('week'),
      dayjs().startOf('week').subtract(1, 'day').endOf('week'),
    ],
    '本月': [
      dayjs().startOf('month'),
      dayjs().endOf('month'),
    ],
    '上月': [
      dayjs().startOf('month').subtract(1, 'day').startOf('month'),
      dayjs().startOf('month').subtract(1, 'day'),
    ]
  }
}

const queryParams = reactive({
  priority: '',
  status: '',
  jobRequirementIds: [],
  date: [
    dayjs().subtract(1, 'day'),
    dayjs().subtract(1, 'day')
  ],
  dateType: 1
})
const filter = reactive({
  jobStatus: [] as any,
  jobRequirements: [] as any,
  timeQuantum: [] as any,
  ranges: getDateRanges()
})

const status = reactive({
  searchLoading: false,
  fetching: false,
  showDetailed: false,
  detailLoading: false,
  showTalentDetail: false
})

const columnsList = ref([])
const dataList = ref([])

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const detailTitle = ref('')
const detailHeader = ref([] as any[])
const detailList = ref([])
const talentId = ref<number>()

const initFilter = async () => {
  const [ dictTimeQuantum, dictJobStatus ] = await Promise.all([getTimeQuantum(), getJobStatus()])

  filter.timeQuantum = dictTimeQuantum.data
  filter.jobStatus = dictJobStatus.data
}
initFilter()
// 用于做条件筛选的项目名称
const handleSearchJobList = async (val: string) => {
  try {
    status.fetching = true
  
    // 检索条件
    const params: any = {
      priority: queryParams.priority,
      name: val,
      current: 1,
      size: 20
    }

    if (queryParams.status) {
      params.status = queryParams.status
    }

    const { data } = await getJobRequireSearch(params)

    filter.jobRequirements = data.jobRequirements.map((item: any, index: number) => { return { value: item.id, label: item.processName } })

    status.fetching = false
  } catch (err) {
    status.fetching = false
  }
}
handleSearchJobList('')

const jobPagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})
// 分页获取报表信息
const getPaginationJobList = async () => {
  // console.log('pageInfo', current)
  // jobPagination.current = current

  try {
    // 检索条件
    const params: any = {
      priority: queryParams.priority,
      name: '',
      current: 1,
      size: 20
    }

    if (queryParams.status) {
      params.status = queryParams.status
    }

    const { data } = await getJobRequireSearch(params)

    jobPagination.total = data.total

    const ids = data.jobRequirements.map((item: any) => item.id)

    return ids
  } catch (err) {
    return []
  }
}

// 筛选条件修改
const handleFilterChange = async (type: string, value: any) => {
  if (type === 'status') {
    queryParams.jobRequirementIds = []
    handleSearchJobList('')
  }

  if (type === 'jobRequirementIds') {
    queryParams.status = ''
  }

  if (type === 'priority') {
    filter.jobRequirements = []
  }

  jobPagination.total = 0
  jobPagination.current = 1

  getList()
}

const dateFormat = (time: any, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format)
}
const getDateTime = () => {
  let startTime = ''
  let endTime = ''
  const curDate = dayjs().subtract(1, 'day')

  switch (queryParams.dateType) {
    case 1:
      startTime = dateFormat(curDate)
      endTime = dateFormat(curDate)
      break;
    case 2:
      startTime = dateFormat(dayjs().startOf('week'))
      endTime = dateFormat(dayjs().startOf('week').add(6, 'day'))
      break;
    case 3:
      startTime = dateFormat(dayjs().startOf('week').subtract(1, 'day'))
      endTime = dateFormat(dayjs().startOf('week').subtract(7, 'day'))
      break;
    case 4:
      startTime = dateFormat(dayjs().startOf('month'))
      endTime = dateFormat(dayjs().startOf('month').add(1, 'month').subtract(1, 'day'))
      break;
    case 5:
      startTime = dateFormat(dayjs().startOf('month').subtract(1, 'month'))
      endTime = dateFormat(dayjs().startOf('month').subtract(1, 'day'))
      break;
    default:
      break;
  }

  return {
    startTime,
    endTime
  }
}

// 获取报表
const getList = async () => {
  status.searchLoading = true
  // const { startTime, endTime } = getDateTime()

  const startTime = dateFormat(queryParams.date[0])
  const endTime = dateFormat(queryParams.date[1])

  try {
    let ids: number[] = []

    if (queryParams.jobRequirementIds.length) {
      ids = queryParams.jobRequirementIds
    } else {
      ids = await getPaginationJobList()
    }

    const res = await getStat('jobs', {
      endTime,
      startTime,
      ids
    })

    const heads = res.data.heads.map((item: any) => {
      return {
        title: item.headName,
        dataIndex: item.headKey,
        key: item.headKey,
      }
    })

    columnsList.value = heads

    const list = res.data.rows.map((item: any) => {
      const o:any = {}
      item.columns.forEach((row: any) => {
        o[row.headKey] = row.value
        o[row.headKey + 'Query'] = row.query
      })

      return o
    })

    dataList.value = list

    // dataList.value = res.data
    status.searchLoading = false
  } catch (err) {
    status.searchLoading = false
  }
}
getList()

let curQueryStr = ''
let curActionKey = ''
// 显示报表明细
const handleClickRow = async (column: any, record: any) => {
  const queryStr = record[column.key + 'Query']
  curQueryStr = queryStr

  curActionKey = column.key

  detailTitle.value = record.jobRequirement + '-' + column.title
  pagination.current = 1
  pagination.total = 0
  status.showDetailed = true

  const res = await getDetail(queryStr)

  pagination.total = res.total

  detailList.value = res.list
  detailHeader.value = res.header
}

// 报表明细分野
const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current

  const res = await getDetail(curQueryStr)

  pagination.total = res.total

  detailList.value = res.list
  detailHeader.value = res.header
}

// 获取报表明细
const getDetail = async (queryStr: string) => {
  status.detailLoading = true

  try {
    const res = await getStatDetail('jobs', {
      queryStr,
      current: pagination.current,
      size: pagination.pageSize,
    })

    const header = await getStatDetailHeader()

    const list = res.data.objects.map((item: any) => {
        let latestTaskName = item.latestTask && item.latestTask.name
        let latestTaskId = item.latestTask && item.latestTask.id
        let caName = item.suggestUser && item.suggestUser.realName
        let caId = item.suggestUser && item.suggestUser.id

        if (curActionKey === 'addInterviewCount' || curActionKey === 'currentInterviewCount') {
          latestTaskName = item.parentLatestTask && item.parentLatestTask.task.name
          latestTaskId = item.parentLatestTask && item.parentLatestTask.task.id

          caName = item.parentLatestTask && item.parentLatestTask.suggestUser.realName
          caId = item.parentLatestTask && item.parentLatestTask.suggestUser.id
        }

      return {
        talentId: item.talent.id,
        talentName: item.talent.realName,
        customerFullName: item.customer && item.customer.customerFullName,
        customerId: item.customer && item.customer.id,
        jobName: item.jobRequirement && item.jobRequirement.processName,
        jobId: item.jobRequirement && item.jobRequirement.id,
        latestTaskName,
        latestTaskId,
        caName,
        caId,
        pmName: item.pmName,
        processStartTime: dateFormat(item.process.startTime, 'YYYY-MM-DD HH:mm')
      }
    })

    status.detailLoading = false

    return {
      header: header.data,
      list,
      total: res.data.total
    }
  } catch (err) {
    status.detailLoading = false
    return {
      header: [],
      list: [],
      total: 0
    }
  }
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      status.showTalentDetail = true
      talentId.value = record.talentId
    }
  }
}

const noView = ['endTime', 'startTime', 'jobRequirement', 'customer', 'pm']

</script>
<template lang="pug">
.ca-statistics-page
  section.statistics-search-filter
    .search-filter
      a-form-item(label="优先级")
        a-select(
          @change="(value) => { handleFilterChange('priority', value)} ",
          allow-clear,
          :dropdownMatchSelectWidth="false",
          v-model:value="queryParams.priority",
        )
          a-select-option(:value="10") 高优先级(P0)
          a-select-option(:value="5") 常规(P1)
          a-select-option(:value="1") 低优先级(P2)

      a-form-item(label="项目状态")
        a-select(
          allow-clear,
          v-model:value="queryParams.status",
          :dropdownMatchSelectWidth="false",
          :options="filter.jobStatus",
          @change="(value:any) => { handleFilterChange('status', value) }"
        )

      a-form-item(label="项目名称")
        a-select(
          allow-clear,
          show-search
          mode="multiple"
          max-tag-count="responsive"
          v-model:value="queryParams.jobRequirementIds",
          :dropdownMatchSelectWidth="false",
          :filter-option="false"
          :options="filter.jobRequirements",
          @search="handleSearchJobList"
          @change="(value:any) => { handleFilterChange('jobRequirementIds', value) }"
        )
          template(v-if="status.fetching" #notFoundContent)
            a-spin

      a-form-item(label="时间")
        a-range-picker(
          v-model:value="queryParams.date"
          :bordered="false"
          :presets="filter.ranges"
          @change="(value:any) => { handleFilterChange('dateType', value) }"
        )

        //- a-select(
        //-   v-model:value="queryParams.dateType",
        //-   :dropdownMatchSelectWidth="false",
        //-   :options="filter.timeQuantum",
        //-   @change="(value:any) => { handleFilterChange('dateType', value) }"
        //- )

  //-列表
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1500, y: winHeightStore.value - 330 }" 
        :pagination="false"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          .pointer(
            v-if="!noView.includes(column.key)"
            @click="handleClickRow(column, record)"
          ) {{ text }}
      .pagination(v-if="!queryParams.jobRequirementIds.length")
        a-pagination(
          v-model:current="jobPagination.current"
          :total="jobPagination.total"
          :showSizeChanger="false"
          @change="getList"
        )

  //- 报表明细
  a-modal(
    v-model:open="status.showDetailed"
    :title="detailTitle"
    width="900px"
    :footer="null"
    @ok="() => status.showDetailed = false"
  )
    a-spin(:spinning="status.detailLoading")
      a-table(
        v-if="columnsList.length" 
        :columns="detailHeader" 
        :data-source="detailList" 
        :pagination="pagination"
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(v-if="column.dataIndex === 'talentName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.talentId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'customerFullName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.customerId }}
              .pointer {{ text }}
          
          template(v-if="column.dataIndex === 'jobName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.jobId }}
              .pointer {{ text }}

          template(v-if="column.dataIndex === 'latestTaskName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.latestTaskId }}
              .pointer {{ text }}
          
          template(v-if="column.dataIndex === 'caName'")
            a-popover(placement="top")
              template(#content)
                div ID: {{ record.caId }}
              .pointer {{ text }}

  //- 人才详情
  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")
</template>
<style lang="scss" scoped>
.ca-statistics-page {
  section.statistics-search-filter{
    background-color: #fff;
    padding: 24px 16px 0;
    border-radius: 8px;
    margin-bottom: 16px;

    .search-filter {
      display: flex;
      margin: 0 -8px;
      justify-content: flex-start;

      >* {
        padding: 0 8px;
        width: 33%;
      }

      :deep(.ant-form-item) {
        flex-wrap: nowrap;
      }

      :deep(.ant-select-selector) {
        background-color: #f9f9f9;
        border-color: transparent;
      }

      :deep(.ant-picker) {
        background-color: #f9f9f9 !important;
      }

      :deep(.ant-select-selection-item) {
        color: #FF9111;
      }

      :deep(label) {
        background-color: #f9f9f9;
        padding-left: 8px;
      }
    }
  }

  .pointer {
    cursor: pointer;
  }

  .pagination {
    padding: 16px;
    background: white;
    text-align: right;
  }
}
</style>