<template lang="pug">
mixin filters
  .filters
    a-row(:gutter="[12,12]")
      a-col(:xl="8" :lg="12" :md="12" :sm="24" :xs="24")
        FilterItem()
          a-select(
            allow-clear
            show-search
            :filter-option="false"
            placeholder="客户"
            v-model:value="params.customerIds"
            :dropdownMatchSelectWidth="false"
            :options="options.customers"
            @search="fetchCustomerList"
            @change="handleFilterChange"
          )

      a-col(:xl="8" :lg="12" :md="12" :sm="24" :xs="24")
        FilterItem(label="")
          a-range-picker(
            v-model:value="params.offerDateRange"
            style="width: 100%"
            :bordered="false"
            :presets="options.dateRanges"
            @change="handleFilterChange"
            :placeholder="['OFFER日期', 'OFFER日期']"
          )

      a-col(:xl="8" :lg="12" :md="12" :sm="24" :xs="24")
        FilterItem(label="")
          a-range-picker(
            v-model:value="params.onboardingDateRange"
            style="width: 100%"
            :bordered="false"
            :presets="options.dateRanges"
            @change="handleFilterChange"
            :placeholder="['入职日期', '入职日期']"
          )

      a-col(:xl="8" :lg="12" :md="12" :sm="24" :xs="24")
        FilterItem(label="")
          a-range-picker(
            v-model:value="params.overInsureDateRange"
            style="width: 100%"
            :bordered="false"
            :presets="options.dateRanges"
            @change="handleFilterChange"
            :placeholder="['过保日期', '过保日期']"
          )

      a-col(:xl="8" :lg="12" :md="12" :sm="24" :xs="24")
        FilterItem(label="")
          a-input-search(v-model:value="params.talentRealName" placeholder="候选人姓名" @search="handleFilterChange")
mixin operation-button
  .operation-area
    a-button(@click="handleDownloadReceivable" type="primary" ) 导出应收数据
mixin table-data
  a-table(
    :pagination="pagination"
    :columns="columns"
    :dataSource="tableData"
    :size="'small'"
    :loading="status.loading"
    @change="handleTableChange"
    @resizeColumn="handleResizeColumn"
    :scroll="{ x: 2200 }"
  )
    template(#bodyCell="{column, record}")
      template(v-if="column.key === 'talentRealName'")
        a(@click="() => handleShowTalent(record.talentId)") {{ record.talentRealName }}
      template(v-if="column.key === 'invoiceAmount'")
        a-tooltip(placement="topLeft")
          template(#title)
            <span>总开票金额 = 已开票金额 - 已作废开票金额</span>
          span {{ record.invoiceAmount }}

.offer-receivable-stat-page
  +filters
  +operation-button
  +table-data

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="selectedTalentId" @close="status.showTalentDetail = false")

</template>

<script lang="ts" setup>
import { getOfferReceivableStat, downloadOfferReceivableStat } from '@/api/stat'
import { message } from 'ant-design-vue'
import { onActivated, reactive, ref } from 'vue'
import FilterItem from '@/components/ui/filter-item.vue'
import { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { getCustomerList } from '@/api/customer'
import { getDateRanges } from '@/utils/util'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'

const options = reactive({
  customers: [],
  dateRanges: getDateRanges(),
})

const dates = reactive({
  offerDateRange: [] as Dayjs[],
  onboardingDateRange: [] as Dayjs[],
  overInsureDateRange: [] as Dayjs[],
})

const params = reactive({
  customerIds: undefined,
  talentRealName: undefined,
  offerDateRange: [] as Dayjs[],
  onboardingDateRange: [] as Dayjs[],
  overInsureDateRange: [] as Dayjs[]
})

const tableData = ref([])
const summary = ref({})
const columns = ref([
  { title: '客户名称', dataIndex: 'customerFullName', key: 'customerFullName', width: 120, ellipsis: true, resizable: true, fixed:true },
  { title: '职位名称', dataIndex: 'processName', key: 'processName', width: 120, ellipsis: true, resizable: true, fixed:true },
  { title: '候选人', dataIndex: 'talentRealName', key: 'talentRealName', width: 120, ellipsis: true, resizable: true, fixed:true },
  { title: 'OFFER日期', dataIndex: 'offerDate', key: 'offerDate', width: 120 },
  { title: '薪资', dataIndex: 'offerAmount', key: 'offerAmount', width: 180 },
  { title: '入职日期', dataIndex: 'onBoardingDate', key: 'onBoardingDate', width: 120 },
  { title: '过保日期', dataIndex: 'overInsuranceDate', key: 'overInsuranceDate', width: 120 },
  { title: '开票货币', dataIndex: 'currencyTypeStr', key: 'currencyTypeStr', width: 80, align: 'center' },
  { title: '已开票金额', dataIndex: 'invoiceAmount', key: 'invoiceAmount', width: 120, sorter: amountSorter('invoiceAmount'), align: 'right'},
  { title: '已回款金额', dataIndex: 'statementTotalAmount', key: 'statementTotalAmount', width: 120, sorter: amountSorter('statementTotalAmount'), align: 'right' },
  { title: '应收金额', dataIndex: 'receivableTotalAmount', key: 'receivableTotalAmount', width: 120, sorter: amountSorter('receivableTotalAmount'), align: 'right' },
  { title: '未逾期应收', dataIndex: 'onTimeReceivable', key: 'onTimeReceivable', width: 120, sorter: amountSorter('onTimeReceivable'), align: 'right' },
  {
    title: '已逾期应收', children: [
      { title: '7天', dataIndex: 'overDueReceivable7Days', key: 'overDueReceivable7Days', width: 120, sorter: amountSorter('overDueReceivable7Days'), align: 'right' },
      { title: '30天', dataIndex: 'overDueReceivable30Days', key: 'overDueReceivable30Days', width: 120, sorter: amountSorter('overDueReceivable30Days'), align: 'right' },
      { title: '60天', dataIndex: 'overDueReceivable60Days', key: 'overDueReceivable60Days', width: 120, sorter: amountSorter('overDueReceivable60Days'), align: 'right' },
      { title: '90天', dataIndex: 'overDueReceivable90Days', key: 'overDueReceivable90Days', width: 120, sorter: amountSorter('overDueReceivable90Days'), align: 'right' },
    ]
  },
])

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const status = reactive({
  loading: false,
  showTalentDetail: false,
})

function handleFilterChange() {
  console.log(params)
  pagination.current = 1
  fetchStats()
}

function getFormateDate(date: string) {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}

function amountSorter(key: string) {
  return (a: any, b: any) => {
    const aAmount = Number(a[key].split(' ')[0])
    const bAmount = Number(b[key].split(' ')[0])
    return aAmount - bAmount
  }
}

function getOfferAmount(record: any) {
  const variables = record.offerTask?.localVariables
  if (!variables) return ''
  const amount = variables.salary
  const currencyKey = variables.currencyType
  let timeUnit = variables.salaryUnit
  if (variables.salaryUnit === 'monthly') timeUnit = '月薪'
  if (variables.salaryUnit === 'yearly') timeUnit = '年薪'
  return `${amount} ${currencyKey} ${timeUnit}`
}

function getOnboardingDate(record: any) {
  const variables = record.offerTask?.localVariables
  if (!variables) return ''
  return variables.onboardingDate
}

function getOverinsureDate(record: any) {
  const variables = record.offerTask?.localVariables
  if (!variables) return ''
  return variables.overInsuranceDate
}

async function fetchCustomerList(name: string = '') {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    isSearchProject: true,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })
  options.customers = list
}

const selectedTalentId = ref()
function handleShowTalent(talentId: number) {
  selectedTalentId.value = talentId
  status.showTalentDetail = true
}

function handleResizeColumn(width: number, col: any) {
  col.width = width;
}

function handleTableChange(pageInfo: any, filters: any, sorter: any) {
  if (pagination.current == pageInfo.current) return
  pagination.current = pageInfo.current
  fetchStats()
}

function getDisplayAmount(amount: number, currencyType: string) {
  return `${(amount / 100).toLocaleString(undefined, {minimumFractionDigits: 2})}`
}

async function processDatas(list: any) {
  const result = list.map((item: any) => {
    return {
      customerFullName: item.customerFullName,
      processName: item.jobRequirement.processName,
      currencyTypeStr: item.currencyTypeStr,
      talentRealName: item.talentRealName,
      talentId: item.offerTask?.talentId,
      offerDate: item.offerDate,
      onBoardingDate: item.hireDate,
      overInsuranceDate: item.keepDate ? dayjs(item.keepDate).format('YYYY-MM-DD') : '',
      offerAmount: getOfferAmount(item),
      invoiceAmount: getDisplayAmount(item.invoiceTotalAmount + item.invoiceDiscardAmount, item.currencyTypeStr),
      discardInvoiceAmount: getDisplayAmount(item.invoiceDiscardAmount, item.currencyTypeStr),
      statementTotalAmount: getDisplayAmount(item.statementTotalAmount, item.currencyTypeStr),
      receivableTotalAmount: getDisplayAmount(item.receivableTotalAmount, item.currencyTypeStr),
      onTimeReceivable: getDisplayAmount(item.onTimeReceivable, item.currencyTypeStr),
      overDueReceivable7Days: getDisplayAmount(item.overDueReceivable7Days, item.currencyTypeStr),
      overDueReceivable30Days: getDisplayAmount(item.overDueReceivable30Days, item.currencyTypeStr),
      overDueReceivable60Days: getDisplayAmount(item.overDueReceivable60Days, item.currencyTypeStr),
      overDueReceivable90Days: getDisplayAmount(item.overDueReceivable90Days, item.currencyTypeStr)
    }
  })
  return result
}

async function fetchStats() {
  status.loading = true
  try {
    const res = await getOfferReceivableStat({
      current: pagination.current,
      size: pagination.pageSize,
      customerIds: params.customerIds ? [params.customerIds] : undefined,
      talentRealName: params.talentRealName ? params.talentRealName : undefined,
      offerStartDate: params.offerDateRange && params.offerDateRange[0] ? params.offerDateRange[0].format('YYYY-MM-DD') : undefined,
      offerEndDate: params.offerDateRange && params.offerDateRange[1] ? params.offerDateRange[1].format('YYYY-MM-DD') : undefined,
      overInsureStartDate: params.overInsureDateRange && params.overInsureDateRange[0] ? params.overInsureDateRange[0].format('YYYY-MM-DD') : undefined,
      overInsureEndDate: params.overInsureDateRange && params.overInsureDateRange[1] ? params.overInsureDateRange[1].format('YYYY-MM-DD') : undefined,
      hireStartDate: params.onboardingDateRange && params.onboardingDateRange[0] ? params.onboardingDateRange[0].format('YYYY-MM-DD') : undefined,
      hireEndDate: params.onboardingDateRange && params.onboardingDateRange[1] ? params.onboardingDateRange[1].format('YYYY-MM-DD') : undefined,
    })
    pagination.total = res.data.total
    tableData.value = await processDatas(res.data.financeReceivableStats)
    summary.value = res.data.financeReceivableSummary
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

function handleDownloadReceivable() {

}

onActivated(() => {
  fetchStats()
})
</script>

<style lang="sass" scoped>
.filters
  background-color: #fff
  border-radius: 8px
  padding: 16px
  margin-bottom: 16px
.operation-area
  margin: 20px 0
  text-align: right
</style>