<template lang="pug">
.test-customer-edit-page
  a-page-header(title="客户编辑文件回显测试", sub-title="测试客户编辑组件的文件回显功能")
  
  .page-content
    a-card(title="客户编辑组件测试")
      .test-controls
        a-space
          a-input-number(v-model:value="testCustomerId" placeholder="输入客户ID" :min="1")
          a-button(type="primary" @click="openCustomerEdit" :disabled="!testCustomerId") 打开客户编辑
          a-button(@click="mockCustomerData") 模拟客户数据
          a-button(@click="clearLogs") 清空日志
      
      .test-results(v-if="showEditor")
        h3 客户编辑组件
        customer-basic-update(
          :customerId="testCustomerId"
          @close="closeEditor"
          @update="handleUpdate"
        )
    
    a-card(title="调试日志", style="margin-top: 16px")
      .log-container
        .log-item(v-for="(log, index) in logs" :key="index" :class="log.type")
          span.timestamp {{ log.timestamp }}
          span.message {{ log.message }}
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CustomerBasicUpdate from '@/components/app/customer-basic-update.vue'
import { message } from 'ant-design-vue'

const testCustomerId = ref<number>(3276) // 默认测试ID
const showEditor = ref(false)

interface LogItem {
  timestamp: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

const logs = ref<LogItem[]>([])

const addLog = (message: string, type: LogItem['type'] = 'info') => {
  logs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
  console.log(`[${type.toUpperCase()}] ${message}`)
}

const openCustomerEdit = () => {
  if (!testCustomerId.value) {
    message.error('请输入客户ID')
    return
  }
  
  addLog(`打开客户编辑，ID: ${testCustomerId.value}`, 'info')
  showEditor.value = true
}

const closeEditor = () => {
  addLog('关闭客户编辑组件', 'info')
  showEditor.value = false
}

const handleUpdate = () => {
  addLog('客户信息更新成功', 'success')
  showEditor.value = false
}

const mockCustomerData = () => {
  // 模拟一个有文件的客户数据
  const mockData = {
    id: 9999,
    customerFullName: '测试客户',
    customerFileObjects: [
      {
        fileId: 'mock_file_1',
        fileName: '测试文件1.pdf',
        fileUrl: 'http://example.com/file1.pdf',
        fileSize: 1024000
      },
      {
        fileId: 'mock_file_2',
        fileName: '测试文件2.doc',
        fileUrl: 'http://example.com/file2.doc',
        fileSize: 2048000
      }
    ]
  }
  
  addLog('模拟客户数据已准备，包含2个文件', 'success')
  addLog(`文件1: ${mockData.customerFileObjects[0].fileName}`, 'info')
  addLog(`文件2: ${mockData.customerFileObjects[1].fileName}`, 'info')
  
  testCustomerId.value = mockData.id
}

const clearLogs = () => {
  logs.value = []
}

// 监听控制台输出
const originalConsoleLog = console.log
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

console.log = (...args) => {
  originalConsoleLog(...args)
  const message = args.join(' ')
  if (message.includes('文件') || message.includes('setFileList') || message.includes('回显')) {
    addLog(`Console: ${message}`, 'info')
  }
}

console.error = (...args) => {
  originalConsoleError(...args)
  const message = args.join(' ')
  addLog(`Error: ${message}`, 'error')
}

console.warn = (...args) => {
  originalConsoleWarn(...args)
  const message = args.join(' ')
  addLog(`Warning: ${message}`, 'warning')
}

onMounted(() => {
  addLog('文件回显测试页面初始化完成', 'success')
  addLog('可以输入客户ID并点击"打开客户编辑"来测试文件回显功能', 'info')
})
</script>

<style scoped lang="scss">
.test-customer-edit-page {
  padding: 24px;
  
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .test-controls {
    margin-bottom: 24px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 6px;
  }
  
  .test-results {
    margin-top: 16px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
  }
  
  .log-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    background: #fafafa;
    
    .log-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .timestamp {
        width: 100px;
        color: #999;
        font-family: monospace;
      }
      
      .message {
        flex: 1;
        margin-left: 12px;
        word-break: break-all;
      }
      
      &.info .message {
        color: #333;
      }
      
      &.success .message {
        color: #52c41a;
      }
      
      &.warning .message {
        color: #faad14;
      }
      
      &.error .message {
        color: #f5222d;
      }
    }
  }
}
</style>
