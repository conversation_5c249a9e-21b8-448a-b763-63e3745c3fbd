<!--
 * @Author: sx <EMAIL>
 * @Date: 2022-10-25 16:18:49
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-11-04 16:07:41
 * @FilePath: \itp-operation-web\src\views\wechat\industry-details.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { editArticle, setArticle, getArticle } from '@/api/wechat'
import { API_URL } from '@/api/customer'
import { useUserStore } from '@/store/user.store'
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue'

import quillEditor from '@/components/quill-editor/quill-editor.vue'
import PositionSelector from '@/components/app/position-selector.vue'


const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

function goBack() {
  router.go(-1)
}

interface DataForm {
  title: string;
  subTitle: string;
  tag: string;
  tags: string[];
  iconUrl: '',
  icon: any,
  positionIds: any[],
  content: any;
}

const status = reactive({
  loading: false,
  btnLoading: false,
  upLoading: false,

  showPositionSelector: false,
})

const dataFormRef = ref()
const dataForm = reactive<DataForm>({
  title: '',
  subTitle: '',
  tag: '',
  tags: [],
  iconUrl: '',
  icon: '',
  positionIds: [],
  content: '',
});

const columnsList = [
  {
    title: '职位ID',
    dataIndex: 'id',
    key: 'id'
  },
  {
    title: '职位名称',
    dataIndex: 'positionTitle',
    key: 'positionTitle',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
];

onMounted(() => {
  handleReset()

  fetch()
})

const fetch = async () => {
  const knowledgeId = route.params.id
  if (!knowledgeId) {
    dataFormRef.value.resetFields()
    return
  }

  try {
    status.loading = true

    const { data } = await getArticle(knowledgeId)

    dataForm.title = data.title
    dataForm.subTitle = data.subTitle
    dataForm.tags = data.tags
    if (data.icon) {
      const iconArr = data.icon.split(',')
      dataForm.icon = data.icon
      dataForm.iconUrl = iconArr[1]
    }
    dataForm.content = data.content

    dataForm.positionIds = data.positions.map((item: any) => item.position)

    status.loading = false
  } catch (err) {
    status.loading = false
  }
}

const handleClickAddTag = async () => {
  if (!dataForm.tag) {
    message.error('不能为空！')
    return
  }

  dataForm.tags.push(dataForm.tag)

  dataForm.tag = ''
}

const hanldeClickDelTag = async (index:number) => {
  dataForm.tags.splice(index, 1)
}

// 上传logo
const handleUploadLogo = (info: any) => {
  if (info.file.status === 'uploading') {
    status.upLoading = true
  } else if (info.file.status === 'done') {
    const fileResponse: any = info.file.response.data
    status.upLoading = false
    dataForm.iconUrl = fileResponse.fileAbsolutePath
    dataForm.icon = `${fileResponse.id},${fileResponse.fileAbsolutePath}`
  }
}

// 选择职位回调
const handelPositionSelect = async (positions: any[]) => {
  const positionSet = new Set()
  dataForm.positionIds.forEach((item, index) => {
    positionSet.add(item.id)
  })

  positions.forEach((item, index) => {
    if (!positionSet.has(item.id)) {
      dataForm.positionIds.push(item)
    }
  })

  // console.log('dataForm.positionIds', dataForm.positionIds)

  status.showPositionSelector = false
}

// 删除职位
const handleClickItemDel = (index: number) => {
  // console.log('111', item)
  dataForm.positionIds.splice(index, 1)
}


// 添加 
const handleClickSave = async () => {
  try {
    status.btnLoading = true

    await dataFormRef.value.validate()

    const positionIds = dataForm.positionIds.map(item => item.id)

    const params = {
      ...dataForm,
      positionIds
    }
    
    await setArticle(params)

    goBack()

    status.btnLoading = false

  } catch (err) {
    status.btnLoading = false
  }
}

// 修改
const handleClickEdit = async () => {
  try {
    status.btnLoading = true

    await dataFormRef.value.validate()

    const positionIds = dataForm.positionIds.map((item: any) => item.id)

    const params = {
      ...dataForm,
      positionIds
    }

    await editArticle(route.query.id, params)

    goBack()

    status.btnLoading = false

  } catch (err) {
    status.btnLoading = false
  }
}

const handleReset = () => {
  // dataForm.title = ''
  // dataForm.subTitle = ''
  // dataForm.tag = ''
  // dataForm.tags = []
  // dataForm.iconUrl = ''
  // dataForm.icon = ''
  // dataForm.positionIds = []
  // dataForm.content = ''
  dataFormRef.value.resetFields()
}

</script>


<template lang="pug">
.industry-details
  a-spin(:spinning="status.loading")
    a-form(:model="dataForm" ref="dataFormRef" :label-col="{ style: { width: '70px' } }")
      a-form-item(label="标题" name="title" :rules="[{ required: true, message: '不能为空!' }]")
        a-input(v-model:value="dataForm.title" class="w-200" allow-clear)
      
      a-form-item(label="标签")
        a-input(v-model:value="dataForm.tag" class="w-200" allow-clear)
        a-button(@click="handleClickAddTag" class="add-tag") 添加
        div(class="tags")
          a-tag(v-for="(item, index) in dataForm.tags" class="tags-item" :key="item" closable @close="hanldeClickDelTag(index)") {{ item }}

      a-form-item(label="Logo")
        a-upload.logo_uploader(
          name="image",
          accept="png,jpg,jpeg",
          :show-upload-list="false",
          :action="API_URL.LOGO_UPLOAD",
          :headers="{'Authorization': userStore.token}",
          @change="handleUploadLogo"
        )
          a-avatar.avatar(
            v-if="dataForm.iconUrl",
            :src="dataForm.iconUrl",
            alt="avatar"
            shape="square"
            :size="140"
          )
          div.upload-area(v-else)
            LoadingOutlined(v-if="status.upLoading")
            PlusOutlined(v-else)

      a-form-item(label="介绍" name="subTitle" :rules="[{ required: true, message: '不能为空!' }]")
        a-textarea(v-model:value="dataForm.subTitle" class="w-600" allow-clear)
      
      a-form-item(label="内容")
        quill-editor(v-model:value="dataForm.content")
      
      a-form-item(label="推荐职位")
        a-button(@click="status.showPositionSelector = true") 添加职位

    a-table(
      v-if="dataForm.positionIds.length > 0"
      :columns="columnsList" 
      :data-source="dataForm.positionIds" 
    )
      template(#bodyCell="{ column, record, index }")
        template(v-if="column.key==='action'")
          a-button(type="link" danger @click="handleClickItemDel(index)") 删除

    .form-action
      a-space()
        a-button(v-if="route.query.id" @click="handleClickEdit" type="primary" :loading="status.btnLoading") 修改
        a-button(v-else @click="handleClickSave" type="primary" :loading="status.btnLoading") 新增

  PositionSelector(v-model:visible="status.showPositionSelector" :selected="dataForm.positionIds" @select="handelPositionSelect")

</template>

<style lang="scss" scoped>
.industry-details{
  background: white;
  padding: 28px 24px;

  .form-action{
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px;
    text-align: right;
  }

  .add-tag{
    margin-left: 15px;
  }

  .tags{
    margin-top: 10px;
  
    &-item{
      background: rgba(255, 145, 17, 0.1) !important;
      color: #FF9111;
      border: 0 none;
      height: 22px;
      line-height: 22px;
    }
  }

  .w-200{
    width: 200px;
  }

  .w-600{
    width: 600px;
  }

  .upload-area {
    width: 140px;
    height: 140px;
    border: 1px dotted #999;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
