<!--
 * @Author: sx <EMAIL>
 * @Date: 2022-10-25 15:40:53
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-12-05 14:30:57
 * @FilePath: \itp-operation-web\src\views\wechat\Industry-knowledge.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getArticleList, delArticle, showArticle, hideArticle } from '@/api/wechat'
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)

// 检索条件
const queryParams = reactive({
  status: 1
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: false
})

const tsbs = [
  {
    key: 1,
    tab: '已上线'
  },
  {
    key: 0,
    tab: '已下线'
  }
]

const columnsList = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: '200px'
  },
  {
    title: '介绍',
    dataIndex: 'subTitle',
    key: 'subTitle',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: '210px'
  },
];

const dataList = ref([] as any[])

onMounted(() => {
  fetch()
})

const handleFilterChange = (val: number) => {
  queryParams.status = val
  fetch()
}

const fetch = async () => {
  loading.value = true

  const { data} = await getArticleList({
    size: pagination.pageSize,
    current: pagination.current,
    status: queryParams.status
  })

  pagination.total = data.total

  dataList.value = data.articles
  loading.value = false
}

const pageChange = (page: any) => {
  pagination.current = page.current
  fetch()
}

// 删除
const handleClickItemDel = async (params:any ) => {
  await delArticle(params.id)

  message.success('操作成功');

  await fetch()
}

// 下线
const handleClickItemHide = async (params:any ) => {
  await hideArticle(params.id)

  message.success('操作成功');

  await fetch()
}

  // 上线
  const handleClickItemShow = async (params:any ) => {
  await showArticle(params.id)

  message.success('操作成功');

  await fetch()
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      router.push({
        path: `/wechat/industry/knowledge/${record.id}/detail`
      })
    }
  }
}
</script>


<template lang="pug">
.industry-knowledge 
  a-page-header(
    title="行业知识",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="background-color: #fff"
  )
    template(#extra)
      a-button(type="primary", ghost, @click="$router.push(`/wechat/industry/knowledge/create`)") 添加文章
        template(#icon)
          PlusOutlined

  a-spin(:spinning="loading")
    .activity-search
      .activity-search-head
        a-tabs(v-model:activeKey="queryParams.status")
          //- a-tab-pane(v-for="item in tsbs" :key="item.key" :tab="item.tab")
        
          template(#renderTabBar)
            .activity-tab-head
              a-space.position-tab-item(:size="24")
                h4(@click="handleFilterChange(1)" :class="{'active': queryParams.status === 1}") 已上线
                h4(@click="handleFilterChange(0)" :class="{'active': queryParams.status === 0}") 已下线

    a-table(
      :columns="columnsList" 
      :data-source="dataList" 
      :pagination="pagination"
      :customRow="handleCustomRow"
      @change="(pagination) => {pageChange(pagination)}"
      rowClassName="clickable"
    )
      template(#bodyCell="{ column, record  }")
        template(v-if="column.key==='action'")
          a(type="link") 查看详情
          
          a-popconfirm(v-if="record.status === 1" placement="top" ok-text="Yes" cancel-text="No" @confirm="handleClickItemHide(record)")
            template(#title) 确定要下线此文章吗?
            a-button(type="link" danger @click.stop="() => {}") 下线
          
          a-popconfirm(v-else placement="top" ok-text="Yes" cancel-text="No" @confirm="handleClickItemShow(record)")
            template(#title) 确定要上线此文章吗?
            a-button(type="link"  @click.stop="() => {}") 上线

          a-popconfirm(placement="top" ok-text="Yes" cancel-text="No" @confirm="handleClickItemDel(record)")
            template(#title) 确定要删除此文章吗?
            a-button(type="link" danger @click.stop="() => {}") 删除
</template>

<style lang="scss" scoped>
.industry-knowledge{
  background: white;

  :deep(.clickable) {
    cursor: pointer;
  }
}

.activity-search {
  background-color: #fff;
  border-radius: 6px;

  &-head {
    justify-content: space-between;
    align-items: center;

    .activity-tab-head {
      padding: 24px;
    }

    .position-tab-item {
      h4 {
        font-size: 18px;
        transition: all .2s;
        cursor: pointer;
        margin: 0;
      }

      .active {
        color: #FF9111;
        transition: all .2s;
      }
    }

    &__title {
      font-weight: bold;
      font-size: 16px;
    }
  }
}
</style>
