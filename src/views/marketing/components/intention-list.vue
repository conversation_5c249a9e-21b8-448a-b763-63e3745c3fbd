\<template lang="pug">
.intention-list-component

  .intention-list
    a-spin(:spinning="status.loading")
      a-timeline(v-if="intentionList.length")
        a-timeline-item(v-for="(item, index) in intentionList" :key="index")
          .intention-item
            p.time {{item.time}}
            .desc(v-if="item.intention")
              span {{item.intention.action}}: 
              router-link(v-if="item.intention.link && item.intention.target", :to="item.intention.link" @click="()=>{$emit('close')}") {{item.intention.target}}
              span(v-else-if="item.intention.target") {{item.intention.target}}
              span(v-else)

      template(v-else)
        a-empty

  .intention-actions
    a-button(@click="$emit('close')" type="primary") 关闭

</template>

<script lang="ts" setup>
import { getIntentionListByCandidateId } from '@/api/marketing'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs';
import { onMounted, reactive, ref, toRefs } from 'vue'
import { parseIntention } from '../utils/intention-helper';

const props = defineProps({
  candidateId: Number
})
const { candidateId } = toRefs(props)

const emit = defineEmits(['close'])

const status = reactive({
  loading: true
})

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const intentionList = ref<any[]>([])

async function fetchIntentionList(candidateId: number) {
  if (typeof candidateId === 'undefined') return

  status.loading = true
  try {
    const res = await getIntentionListByCandidateId(candidateId, {
      current: pagination.current,
      size: pagination.pageSize
    })

    intentionList.value = res.data.intentions.map((item: any, index: number) => {
      return {
        id: item.id,
        intention: parseIntention(item),
        companyUserName: item.companyUserName,
        time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm')
      }
    })
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handlePageChange(page: any) {
  pagination.current = page.current
  if (candidateId?.value) fetchIntentionList(candidateId?.value)
}

async function handleClose() {
  emit('close')
}

onMounted(() => {
  if (candidateId?.value) fetchIntentionList(candidateId.value)
})

</script>

<style lang="scss" scoped>
.intention-list {
  padding: 24px;
  position: relative;

  .intention-item {
    .time {
      color: #999;
    }
  }
}

.intention-actions {
  padding: 12px 24px;
  border-top: 1px solid #f9f9f9;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: right;
}
</style>