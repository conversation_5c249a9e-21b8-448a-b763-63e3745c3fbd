<template lang="pug">
.followup-list-component

  .followup-list
    a-spin(:spinning="status.loading")
      a-timeline(v-if="followupList.length")
        a-timeline-item(v-for="(item, index) in followupList")
          .intention-item
            p.title {{formatDate(item.createTime)}} {{item.assignee.realName}}
            p.desc {{ item.comment }}
      template(v-else)
        a-empty

    .pagination(v-if="(pagination.total / pagination.pageSize) > 1")
      a-pagination(v-model:current="pagination.current" :total="pagination.total" :pageSize="pagination.pageSize" show-less-items @change="handlePageChange")

  .intention-actions
    a-button(@click="$emit('close')") 关闭

</template>

<script lang="ts" setup>
import { getCandidateFollowups } from '@/api/marketing'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref, toRefs } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  candidateId: Number,
})
const { candidateId } = toRefs(props)

const emit = defineEmits(['close'])

const status = reactive({
  loading: true
})

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const followupList = ref<any[]>([])

async function fetchfollowupList(candidateId: number) {
  if (typeof candidateId === 'undefined') return

  status.loading = true
  try {
    const res = await getCandidateFollowups(candidateId, { current: pagination.current, size: pagination.pageSize })
    followupList.value = res.data.followups
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

async function handlePageChange(page: any) {
  pagination.current = page
  if (candidateId?.value) fetchfollowupList(candidateId.value)
}

async function handleClose() {
  emit('close')
}

onMounted(() => {
  if (candidateId?.value) fetchfollowupList(candidateId.value)
})

</script>

<style lang="scss" scoped>
.followup-list {
  padding: 24px;
  position: relative;
}

.intention-actions {
  padding: 12px 24px;
  border-top: 1px solid #f9f9f9;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: right;
}
</style>