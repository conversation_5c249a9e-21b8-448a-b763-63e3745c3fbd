<template lang="pug">
mixin page-header-section
  a-page-header(
    title="线索详情",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="padding: 0 0 8px;"
  )

mixin candidate-follow-up
  .follow-up
    .follow-up__head
      a-tabs(v-model:activeKey="followUpTab")
        a-tab-pane(:key="1" tab="跟进记录")
        //- a-tab-pane(:key="2" tab="我的沟通记录")
        template(#renderTabBar)
          a-space.position-info__head(:size="24")
            h4(@click="()=>{ handleFollowUpTabChange(1) }" :class="{'active': followUpTab === 1}") 跟进记录
            //- h4(@click="()=>{ handleFollowUpTabChange(2) }" :class="{'active': followUpTab === 2}") 我的沟通记录

    a-spin(:spinning="status.followUpLoading")
      .follow-up__textarea
        a-textarea.follow-up__textarea-input(v-model:value="followForm.comment" placeholder="添加跟进记录" :auto-size="{ minRows: 4, maxRows: 8 }" )
        a-button(@click="handleNewFollowUp") 提交

      .follow-up__comments
        .comments-item(v-for="(item, index) in followups" v-if="followups.length")
          .comments-item__comment {{item.comment}}
          a-space(:size="12")
            .comments-item__name(v-if="item.assignee") {{item.assignee.realName}}
            .comments-item__date {{formatTime(item.createTime)}}
        .comment-empty(v-else)
          a-empty(description="暂无跟进数据")
        .comment-pagination(v-if="(followUpPagination.total / followUpPagination.pageSize) > 1")
          a-pagination(v-model:current="followUpPagination.current" :total="followUpPagination.total" :pageSize="followUpPagination.pageSize" show-less-items @change="handleFollowupPageChange")

mixin candidate-detail-section
  a-spin(:spinning="status.candidateLoading")
    .candidate-detail-section
      .candidate-detail-head
        .candidate-detail-head-avatar
          a-avatar(:size="64" :src="candidateSummary.avatar ")
        .candidate-detail-head-content
          .name {{candidateSummary.name}} {{candidateSummary.mobile}}
          .info
            a-space(:size="24")
              span(v-if="candidateSummary.customer") 公司: {{candidateSummary.customer}}
              span(v-if="candidateSummary.position") 职位: {{candidateSummary.position}}

mixin intention-list-section
  a-spin(:spinning="status.intentionLoading")
    .intention-list-section
      a-table(:columns="columnsConfig" :data-source="intentionList" :pagination="intentionPagination" @change="(page) => handlePageChange(page)")
        template(#bodyCell="{ column, record }")
          template(v-if="column.key==='intention'")
            .intention(v-if="record.intention")
              span {{`${record.intention.action}: `}}
              router-link(v-if="record.intention.link && record.intention.target", :to="record.intention.link") {{record.intention.target}}
              span(v-else-if="record.intention.target") {{record.intention.target}}
              span(v-else)

mixin duplicate-talent-list
  .duplicate-talent-list
    //- p 当前未关联人才，以下是疑似相关人才，或者您可以通过上传简历新增人才。
    a-row(:gutter="[16, 16]")
      a-col(:span="12" v-for="(talent, index) in duplicateTalentList")
        .duplicate-talent-item
          .talent-avatar
            a-avatar(:src="talent.photoUrl" :size="56")
              span {{talent.realName}}
          .talent-info
            .talent-basic-info
              span.name {{talent.realName}}
              span.extra {{ talent.extra.join(' | ') }}
            .talent-contact-info
              span {{ talent.contact.join(' ') }}
            .talent-education-info(v-if="talent.education.length")
              img.icon(src="@/assets/icons/icon-education.svg")
              span {{ talent.education.join(' ') }}
            .talent-experience-info(v-if="talent.experience.length")
              img.icon(src="@/assets/icons/icon-company.svg")
              span {{ talent.experience.join(' ') }}
          .talent-action
            a-space(:size="8")
              a-button(type="text" size="small" @click="()=>{$router.push(`/talent/${talent.id}/detail`)}") 人才详情
              a-button(type="primary" size="small" @click="()=>{bindTalent(talent)}") 关联人才

      a-col(:span=" duplicateTalentList.length ? 12 : 24")
        .resume-uploader
          a-upload-dragger(
            name="file",
            :before-upload="beforeUpload",
            :multiple="false",
            :showUploadList="false",
            :action="uploadRequestInfo.action",
            :headers="uploadRequestInfo.headers",
            @change="handleUploadChange",
          )
            p.upload-drag-icon
              ContainerOutlined
            p.upload-drag-text 新增并关联人才, 请在这里上传简历
            p.upload-drag-hint 支持{{ supportExtension.join('、') }}格式，单个简历大小不超过6MB。

mixin talent-detail-section
  a-spin(:spinning="status.talentLoading")
    .talent-detail-section
      template(v-if="talent")
        .talent-detail-content
          talent-detail(:talent="talent" :attachments="attachments" @update="handleTalentUpdateType")
          a-affix(:offset-bottom="0")
            .talent-action
              a-space(:size="16")
                a-button(@click="()=>{unbindTalent(talent)}") 解除关联
                a-button(@click="handleJoinJob" type="primary") 加入职位
      template(v-else)
        +duplicate-talent-list

.candidate-detail-page
  +page-header-section

  a-row(:gutter="[16,16]")
    a-col(:span="16")
      +candidate-detail-section

      .candidate-detail-tabs
        a-tabs(v-model:activeKey="detailTab")
          a-tab-pane(:key="1")
            +intention-list-section

          a-tab-pane(:key="2")
            +talent-detail-section

          template(#renderTabBar)
            a-space.candidate-detail-tabs-head(:size="24")
              h4(@click="()=>{ handleDetailTabChange(1) }" :class="{'active': detailTab === 1}") 意向列表
              h4(@click="()=>{ handleDetailTabChange(2) }" :class="{'active': detailTab === 2}") 人才详情

    a-col(:span="8")
      +candidate-follow-up

  jobSelector(v-model:visible="status.showJobSelecor" @select="handleSelectJob" :loading="status.joinJob" :multi="true")

  a-drawer(v-model:open="drawer.show" :title="drawer.title" :destroyOnClose="true" :width="480" :bodyStyle="{padding: 0}")
    component(:is="drawer.component" v-bind="drawer.params" @close="drawer.show = false" @update="handleTalentUpdated")

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false")
</template>

<script lang="ts" setup>
import { addCandidateFollowup, getCandidateDetail, getCandidateFollowups, getIntentionListByCandidateId, getCandidateTalentInfo } from '@/api/marketing'
import { ConsoleSqlOutlined, ContainerOutlined } from '@ant-design/icons-vue'
import { API_URL, getAllTalentList, getTalentDetail } from '@/api/talent/talent'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { Modal } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/user.store'
import { useMemoStore } from '@/store/memo.store'
import { parseIntention } from '../utils/intention-helper'
import talentDetail from '@/components/app/talent-detail.vue'
import jobSelector from '@/components/app/job-selector.vue'
import dayjs from 'dayjs'
import { addTalentToJobPool } from '@/api/job'
import talentBasicInfoEdit from '@/components/app/talent-update.vue'
import { getAgeFromBirthday, getShortDate } from '@/utils/string'
import { dictionary } from '@/api/dictionary'
import { createCandidateTalentResumeBind, removeCandidateTalentResumeBind } from '@/api/marketing'
import { getTalentResumeAttachments } from '@/api/resume'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'


const route = useRoute()
const candidateId = ref(Number(route.params.id))
const userStore = useUserStore()

const uploadRequestInfo = {
  action: API_URL.RESUME_UPLOAD,
  headers: { Authorization: userStore.token }
}

const status = reactive({
  followUpLoading: false,
  candidateLoading: false,
  uploading: false,
  showJobSelecor: false,
  joinJob: false,
  talentLoading: false,
  intentionLoading: false,
  showTalentDetail: false,
})

const followForm = reactive({ comment: '' })
const followUpTab = ref(1)
const followups = ref<any[]>([])
const detailTab = ref(1)
const talent = ref()

const followUpPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

function formatTime(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

function formatDate(date: string) {
  if (date) return dayjs(date).format('YYYY.MM')
  else return '至今'
}

const degreeMap = new Map()
async function initDegreeDict() {
  try {
    const dictDegree = await dictionary.getDegree()
    Object.entries(dictDegree.data).forEach((item: any, index: number) => {
      const [key, value] = item
      degreeMap.set(Number(key), value)
    })
  } catch (err: any) {
    message.error(`初始化失败，请重试。${err.message}`)
  }
}

const duplicateTalentList = ref<any[]>([])
async function fetchDuplicateTalent(mobile: string) {
  try {
    const res = await getAllTalentList({ mobileNumber: mobile }, {current: 1, size: 20})
    processTalentData(res.data.records)
  } catch (err: any) {
    message.error(err.message)
  }
}

function getGenderStr(gender: number) {
  switch (gender) {
    case 1:
      return '男'
    case 2:
      return '女'
    default:
      return '性别-未知'
  }
}

function processTalentData(talents: any[]) {
  duplicateTalentList.value = talents.map((talent: any, index: number) => {

    const gender = getGenderStr(talent.talentBase.gender)
    const age = getAgeFromBirthday(talent.talentBase.birthday)
    const degree = talent.talentBase.latestDegreeStr
    const mobileArea = talent.talentBase.mobileArea
    const mobile = talent.talentBase.mobileNumber
    const email = talent.talentBase.email

    const firstEducation = talent.talentEducations[0]
    const firstExperience = talent.talentExperiences[0]

    return {
      id: talent.talentBase.id,
      photoUrl: talent.talentBase.photoUrl,
      realName: talent.talentBase.realName,
      extra: [gender, age, degree],
      contact: [`${mobileArea} ${mobile}`, email],
      education: firstEducation ? [
        `${formatDate(firstEducation.fromDate)} - ${formatDate(firstEducation.toDate)}`,
        firstEducation.schoolName,
        degreeMap.get(firstEducation.degree)
      ] : ['暂无教育相关数据'],
      experience: firstExperience ? [
        `${formatDate(firstExperience.fromDate)} - ${formatDate(firstExperience.toDate)}`,
        firstExperience.companyName,
        firstExperience.position
      ] : ['暂无经历相关数据'],
    }
  })
}

async function fetchCandidateFollowup(candidateId: number) {
  status.followUpLoading = true
  try {
    const res = await getCandidateFollowups(candidateId, {
      current: followUpPagination.current,
      size: followUpPagination.pageSize
    })
    followups.value = res.data.followups
    followUpPagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.followUpLoading = false
}

async function handleFollowupPageChange(page: any) {
  followUpPagination.current = page
  fetchCandidateFollowup(candidateId.value)
}

const supportExtension = ['txt', 'pdf', 'doc', 'docx', 'mht', 'jpg', 'jpeg', 'png']

function beforeUpload(fileObject: File) {
  const fileExtention = fileObject.name?.split('.').pop()
  if (!supportExtension.includes(fileExtention!)) {
    message.error('抱歉，暂不支持您上传的文件格式。')
    return false
  }
  return true
}

const memoStore = useMemoStore()
const router = useRouter()

function handleUploadChange(info: any) {
  const uploadStatus = info.file.status
  if (uploadStatus === 'uploading') {
    status.uploading = true
  }

  if (uploadStatus === 'done') {
    status.uploading = false
    const response = info.file.response
    memoStore.set('talent-resume-parse', response.data)
    router.push(`/talent/create?candidate_id=${candidateId.value}`)
    message.success(`文件${info.file.name}上传成功。`)
  }

  if (uploadStatus === 'error') {
    message.error(`抱歉，文件${info.file.name}上传失败，请稍后重试。`)
  }
}

async function handleJoinJob() {
  status.showJobSelecor = true
}

async function handleSelectJob(jobs: any[]) {
  status.joinJob = true
  try {
    const talentId = talent.value.talent.id
    const tasks = jobs.map((item, index) => {
      return addTalentToJobPool(item.id, talentId)
    })
    const res = await Promise.all(tasks)
    message.success(`人才加入职位成功！`)
  } catch (err: any) {
    message.error(err.message)
  }
  status.joinJob = false
  status.showJobSelecor = false
}

async function handleFollowUpTabChange(tab: number) {
  followUpTab.value = tab
}

async function handleDetailTabChange(tab: number) {
  detailTab.value = tab
}

async function handleNewFollowUp() {
  status.followUpLoading = true
  try {
    const res = await addCandidateFollowup(candidateId.value, followForm.comment)
    fetchCandidateFollowup(candidateId.value)
    followForm.comment = ''
  } catch (err: any) {
    message.error(err.message)
  }
  status.showJobSelecor = false
}

function handlePageChange(page: any) {
  intentionPagination.current = page.current
  fetchCandidateIntentionList(candidateDetail.value.id)
}

const attachments = ref<any[]>([])

async function fetchTalentDetail(candidateId: number) {
  try {
    const candidateTalentInfo = await getCandidateTalentInfo(candidateId)
    if (candidateTalentInfo.data) {
      const talentInfoRes = await getTalentDetail(candidateTalentInfo.data.talentId)
      const attachmentsRes = await getTalentResumeAttachments(candidateTalentInfo.data.talentId)
      attachments.value = attachmentsRes.data
      talent.value = talentInfoRes.data
    } else {
      talent.value = null
    }
  } catch (err: any) {
    message.error(err.message)
  }
}

const intentionPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

const columnsConfig = [
  { title: '时间', key: 'time', dataIndex: 'time' },
  { title: '意向', key: 'intention', dataIndex: 'intention' },
  // { title: '来源', key: 'channel', dataIndex: 'channelStr' },
  // { title: '归属', key: 'username', dataIndex: 'companyUserName' },
  // { title: '操作', key: 'action', dataIndex: 'action' },
]

const intentionList = ref<any[]>([])
async function fetchCandidateIntentionList(candidateId: any) {
  status.intentionLoading = true
  try {
    const res = await getIntentionListByCandidateId(candidateId, {
      current: intentionPagination.current,
      size: intentionPagination.pageSize
    })

    intentionList.value = res.data.intentions.map((item: any, index: number) => {
      return {
        id: item.id,
        companyUserName: item.companyUserName,
        intention: parseIntention(item),
        time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm')
      }
    })
    intentionPagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.intentionLoading = false
}

const candidateDetail = ref<any>({})

const candidateSummary = ref({
  mobile: '',
  name: '',
  avatar: '',
  position: '',
  customer: ''
})

async function initTelentSection(candidateId: number) {
  status.talentLoading = true
  await fetchTalentDetail(candidateId)
  if (!talent.value) {
    await initDegreeDict()
    await fetchDuplicateTalent(candidateDetail.value.mobile)
  }
  status.talentLoading = false
}

async function fetchCandidateDetail(candidateId: number) {
  status.candidateLoading = true
  try {
    const res = await getCandidateDetail(candidateId)
    candidateDetail.value = res.data
    const info = JSON.parse(res.data.extra)
    candidateSummary.value.mobile = candidateDetail.value.mobile
    candidateSummary.value.name = info.name
    candidateSummary.value.position = info.position
    candidateSummary.value.customer = info.customer
    candidateSummary.value.avatar = `${import.meta.env.VITE_VUE_APP_ATTACHMENT_BASE_URL}/${info.avatar}`
  } catch (err: any) {
    message.error('数据存在问题，请联系开发定位，非常感谢。')
  }
  status.candidateLoading = false
}

const drawer = reactive({
  show: false,
  title: '',
  component: null as any,
  params: {} as any
})

function getTalentEducationInfo(talent: any) {
  const educationInfo = talent.talentEducations[0]
  if (educationInfo) {
    return `${formatDate(educationInfo.fromDate)} - ${formatDate(educationInfo.toDate)} ${educationInfo.schoolName} ${degreeMap.get(educationInfo.degree) || ''}`
  } else {
    return ''
  }
}

function getTalentExperienceInfo(talent: any) {
  const experienceInfo = talent.talentExperiences[0]
  if (experienceInfo) {
    return `${formatDate(experienceInfo.fromDate)} - ${formatDate(experienceInfo.toDate)} ${experienceInfo.companyName} ${experienceInfo.position}`
  } else {
    return ''
  }
}

async function handleTalentUpdated() {
  drawer.show = false
  fetchTalentDetail(candidateDetail.value.id)
}

function handleTalentUpdateType(type: string) {
  drawer.show = true
  drawer.component = shallowRef(talentBasicInfoEdit)
  drawer.title = '编辑人才基本信息'
  drawer.params = { talentId: talent.value.talent.id, section: [type] }
}

function unbindTalent(talent: any) {
  Modal.confirm({
    title: '解除关联确认',
    content: `确定要解除人才[${talent.realName}]和当前线索的关联？`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await removeCandidateTalentResumeBind(candidateId.value)
        message.success('成功解除关联！')
        drawer.show = false
        initTelentSection(candidateId.value)
      } catch (err: any) {
        message.error(err.message)
      }
    },
  })
}

function bindTalent(talent: any) {
  Modal.confirm({
    title: '关联人才确认',
    content: `确定要将人才[${talent.realName}]关联到当前线索？`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await createCandidateTalentResumeBind({ candidateId: candidateId.value, talentId: talent.id })
        message.success('人才关联成功！')
        drawer.show = false
        fetchTalentDetail(candidateId.value)
      } catch (err: any) {
        message.error(err.message)
      }
    },
  })
}

const talentId = ref<number>()
function showTalentDetail(tid: number) {
  talentId.value = tid
  status.showTalentDetail = true
}

onMounted(() => {
  candidateId.value = Number(route.params.id)
  fetchCandidateDetail(candidateId.value)
  fetchCandidateFollowup(candidateId.value)
  fetchCandidateIntentionList(candidateId.value)
  initTelentSection(candidateId.value)
})

</script>

<style lang="scss" scoped>
.follow-up-section {
  background-color: #fff;
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.follow-up {
  background-color: #fff;
  border-radius: 8px;

  &__head {
    padding: 24px 24px 0 24px;
    position: relative;

    h4 {
      font-size: 20px;
      position: relative;
      line-height: 24px;
      color: #ddd;
      margin: 0;
      cursor: pointer;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }

  &__textarea {
    padding: 16px 24px 24px;
    text-align: right;
  }

  &__textarea-input {
    background-color: #f9f9f9;
    border: none;
    border-radius: 4px;
    outline: none;
    margin-bottom: 16px;
  }

  &__comments {

    .comment-pagination {
      padding: 16px 24px;
      text-align: center;
    }

    .comments-item {
      padding: 24px;
      border-top: 1px solid #f0f0f0;

      &__comment {
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      &__name {
        color: #999;
      }

      &__date {
        color: #999;
      }
    }
  }
}

.candidate-detail-section {
  background-color: #fff;
  width: 100%;
  border-radius: 8px;

  .candidate-detail-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 24px;
    display: flex;
    align-items: center;

    .candidate-detail-head-avatar {
      margin-right: 16px;
    }

    .candidate-detail-head-content {

      .name {
        font-size: 20px;
        font-weight: bold;
        line-height: 32px;
      }

      .info {
        color: #999;
        line-height: 32px;
      }
    }
  }
}

.candidate-detail-tabs {
  margin: 16px 0;
  background-color: #fff;
  border-radius: 8px;

  .candidate-detail-tabs-head {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  h4 {
    font-size: 20px;
    position: relative;
    line-height: 24px;
    color: #ddd;
    margin: 0;
    cursor: pointer;

    &.active {
      color: #FF9111;
      transition: all .2s;
    }
  }
}

.talent-detail-section {

  .talent-detail-content {
    position: relative;

    .talent-action {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
      text-align: right;
      background-color: #fff;
      width: 100%;
    }
  }
}

.resume-uploader {
  border-radius: 8px;

  .upload-drag-icon {
    color: #ff9111;
    font-size: 32px;
    font-weight: bold;
  }

  .upload-drag-text {
    font-size: 14px;
    font-weight: bold;
  }

  .upload-drag-hint {
    font-size: 12px;
    color: #999;
    padding: 0 32px;
  }
}

.duplicate-talent-list {
  padding: 16px;

  .duplicate-talent-item {
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    padding: 16px;
    padding-left: 88px;
    position: relative;

    .talent-avatar {
      position: absolute;
      left: 16px;
      top: 16px;
    }

    .talent-action {
      // border-top: 1px solid #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
      text-align: right;
    }

    .talent-info {
      font-size: 12px;
      line-height: 28px;

      .talent-basic-info {
        color: #444;

        .name {
          font-weight: bold;
          font-size: 16px;
        }

        .extra {
          color: #999;
          margin-left: 16px;
        }
      }

      img.icon {
        display: inline-block;
        height: 16px;
        vertical-align: middle;
        margin-right: 8px;
      }
    }

    .talent-basic-info,
    .talent-contact-info,
    .talent-education-info,
    .talent-experience-info {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      span {
        vertical-align: middle;
      }

    }
  }
}
</style>