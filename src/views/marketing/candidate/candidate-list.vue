<script setup lang="ts">
import { reactive, shallowRef } from "@vue/reactivity"
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { onBeforeRouteLeave, useRouter } from "vue-router"
import { getCandidateList, getCandidateListByStaffId, updateCandidateOwner } from '@/api/marketing'
import IntentionList from '../components/intention-list.vue'
import FollowupList from '../components/followup-list.vue'
import { useUserStore } from '@/store/user.store'
import { parseIntention } from '../utils/intention-helper'
import staffSelector from '@/components/app/staff-selector.vue'
import dayjs from "dayjs"

const router = useRouter()

const status = reactive({
  initFilter: false,
  listLoading: false,
  showDrawer: false,
  showStaffSelector: false,
})

const selectedTab = ref(1)

const myCandidatePagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: false
})

const allCandidatePagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: false
})

const drawer = reactive({
  title: 'asdlfkjas',
  component: null as any,
  props: null as any,
})

enum CANDIDATE_LIST_TYPE {
  ALL = 'ALL', MY = 'MY'
}

const myCandidateList = ref<any[]>([])
const allCandidateList = ref<any[]>([])

const userStore = useUserStore()
const staffId = userStore.id

function handlePageChange(page: any, type: CANDIDATE_LIST_TYPE) {
  if (CANDIDATE_LIST_TYPE.ALL === type) {
    allCandidatePagination.current = page.current
    fetchAllCandidateList()
  }

  if (CANDIDATE_LIST_TYPE.MY === type) {
    myCandidatePagination.current = page.current
    fetchMyCandidateList()
  }
}

const columnsConfig = [
  { title: '用户信息', key: 'user', width: 176 },
  { title: '最新意向', key: 'intention', dataIndex: 'intention' },
  { title: '最近跟进记录', key: 'followup' },
  { title: '归属', key: 'username', dataIndex: 'companyUserName', width: 96 },
  { title: '操作', key: 'action', dataIndex: 'action', fixed: 'right', width: 272 },
]

function getExtraInfo(extra: string) {
  try {
    const info = JSON.parse(extra)
    return info
  } catch (err: any) {
    return {}
  }
}

// 获取职位列表 
async function fetchMyCandidateList() {
  status.listLoading = true
  try {
    const res = await getCandidateListByStaffId(userStore.id, {
      size: myCandidatePagination.pageSize,
      current: myCandidatePagination.current
    })
    myCandidateList.value = res.data.candidate.map((item: any, index: number) => {
      const info = getExtraInfo(item.extra)
      const candidateInfo = getCandidateInfo(item)
      const intention = item.latestIntention ? parseIntention(item.latestIntention) : { action: '注册', time: candidateInfo.time }
      return {
        id: item.id,
        companyUserName: item.currentCompanyUser?.realName,
        mobile: item.mobile,
        name: item.name || info.name,
        avatar: `${import.meta.env.VITE_VUE_APP_ATTACHMENT_BASE_URL}/${info.avatar}`,
        intention,
        followup: item.latestFollowup
      }
    })

    myCandidatePagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

function getCandidateInfo(candidate: any) {
  return {
    action: '注册',
    time: dayjs(candidate.createTime).format('YYYY-MM-DD HH:mm')
  }
}

async function fetchAllCandidateList() {
  status.listLoading = true
  try {
    const res = await getCandidateList({
      size: allCandidatePagination.pageSize,
      current: allCandidatePagination.current
    })
    allCandidateList.value = res.data.candidate.map((item: any, index: number) => {
      const info = getExtraInfo(item.extra)
      const candidateInfo = getCandidateInfo(item)
      const intention = item.latestIntention ? parseIntention(item.latestIntention) : { action: '注册', time: candidateInfo.time }
      return {
        id: item.id,
        companyUserName: item.currentCompanyUser?.realName,
        mobile: item.mobile,
        name: item.name || info.name,
        avatar: info.avatar ? `${import.meta.env.VITE_VUE_APP_ATTACHMENT_BASE_URL}/${info.avatar}` : null,
        intention,
        followup: item.latestFollowup
      }
    })

    allCandidatePagination.total = res.data.total
  } catch (err: any) {
    console.log(err)
    message.error(err.message)
  }
  status.listLoading = false
}

const selectedCandidate = ref<any>()
function handleCandidateAssign(candidate: any) {
  selectedCandidate.value = candidate
  status.showStaffSelector = true
}

async function setCandidateOwner(candidateId: number, companyUserId: number) {
  try {
    const res = await updateCandidateOwner(candidateId, companyUserId)
    message.success('更新线索归属成功！')
  } catch (err: any) {
    message.error(err.message)
  }
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

async function assignToStaff(staffs: any[]) {
  const staff = staffs[0]
  await setCandidateOwner(selectedCandidate.value.id, staff.id)
  status.showStaffSelector = false
  fetchAllCandidateList()
}

function handleTabClick(tab: number) {
  selectedTab.value = tab
  if (tab === 1) {
    fetchMyCandidateList()
  } else {
    fetchAllCandidateList()
  }
}

function handlShowIntention(candidateId: number) {
  status.showDrawer = true
  drawer.title = '意向列表'
  drawer.component = shallowRef(IntentionList)
  drawer.props = { candidateId: candidateId }
}

async function handleShowFollowup(candidateId: number) {
  status.showDrawer = true
  drawer.title = '跟进记录'
  drawer.component = shallowRef(FollowupList)
  drawer.props = { candidateId: candidateId }
}

onMounted(() => {
  fetchMyCandidateList()
})

onBeforeRouteLeave(() => {
  status.showDrawer = false
  status.showStaffSelector = false
})
</script>
          
<template lang="pug">
mixin page-header-section
  a-page-header(
    title="线索管理",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="padding: 0 0 8px;"
  )

mixin filter-section
  .intention-filter
    a-spin(:spinning="status.initFilter")

      //- 省份选择部分
      .intention-filter-item
        .intention-filter-item__title 渠道
        .intention-filter-item__selector
          .intention-filter-item__option
            a-tree-select(
              :tree-data="filter.areas",
              :fieldNames="{ label: 'title', value: 'id' }",
              :style="{ width: '300px' }",
              allow-clear
              placeholder="在下拉列表中选择渠道",
              @change="handleChannelChange"
            )

mixin intention-search-list
  a-spin(:spinning="status.listLoading")
    .intention-search
      .intention-search-head
        a-tabs(v-model:activeKey="selectedTab")
          a-tab-pane(:key="1" tab="我的线索")
            a-table(:columns="columnsConfig" :data-source="myCandidateList" :scroll="{ x: 1200 }" :pagination="myCandidatePagination" @change="(page) => handlePageChange(page, CANDIDATE_LIST_TYPE.MY)")
              template(#bodyCell="{ column, record }")
                template(v-if="column.key==='user'")
                  .user-info
                    a-avatar.user-info__avatar(:src="record.avatar")
                    .user-info__info
                      .user-info__name {{record.name}}
                      .user-info__mobile {{record.mobile}}
                template(v-if="column.key==='followup'")
                  .follow-up(v-if="record.followup")
                    .time {{ formatDate(record.followup.createTime) }} {{record.followup.assignee.realName}}
                    .content {{ record.followup.comment }}
                  .no-follow-up(v-else) 无
                template(v-if="column.key==='intention'")
                  .intention(v-if="record.intention")
                    .time {{ record.intention.time }}
                    span {{`${record.intention.action} `}}
                    router-link(v-if="record.intention.link && record.intention.target", :to="record.intention.link") {{record.intention.target}}
                    span(v-else-if="record.intention.target") {{record.intention.target}}
                    span(v-else)
                template(v-if="column.key==='action'")
                  a-space(:size="12")
                    a(@click="()=> handlShowIntention(record.id)") 意向日志
                    a(@click="()=> handleShowFollowup(record.id)") 跟进记录
                    a(@click="$router.push(`/marketing/candidate/${record.id}/detail`)") 查看详情

          a-tab-pane(:key="2" tab="全部线索")
            a-table(:columns="columnsConfig" :data-source="allCandidateList" :scroll="{ x: 1200 }" :pagination="allCandidatePagination" @change="(page) => handlePageChange(page, CANDIDATE_LIST_TYPE.ALL)")
              template(#bodyCell="{ column, record }")
                template(v-if="column.key==='user'")
                  .user-info
                    a-avatar.user-info__avatar(:src="record.avatar") 
                    .user-info__info
                      .user-info__name {{record.name}}
                      .user-info__mobile {{record.mobile}}
                template(v-if="column.key==='followup'")
                  .follow-up(v-if="record.followup")
                    .time {{ formatDate(record.followup.createTime) }} {{record.followup.assignee.realName}}
                    .content {{ record.followup.comment }}
                  .no-follow-up(v-else) 无
                template(v-if="column.key==='intention'")
                  .intention(v-if="record.intention")
                    .time {{ record.intention.time }}
                    span {{`${record.intention.action} `}}
                    router-link(v-if="record.intention.link && record.intention.target", :to="record.intention.link") {{record.intention.target}}
                    span(v-else-if="record.intention.target") {{record.intention.target}}
                    span(v-else)
                template(v-if="column.key==='action'")
                  a-space(:size="12")
                    a(@click="()=> handlShowIntention(record.id)") 意向日志
                    a(@click="() => handleShowFollowup(record.id)") 跟进记录
                    a(@click="$router.push(`/marketing/candidate/${record.id}/detail`)") 查看详情
                    template(v-if="!record.companyUserName")
                      a(type="link" @click="() => handleCandidateAssign(record)") 分配
          template(#renderTabBar)
            a-space.position-tab-item(:size="24")
              h4(@click="handleTabClick(1)" :class="{'active': selectedTab === 1}") 我的线索
              h4(@click="handleTabClick(2)" :class="{'active': selectedTab === 2}") 全部线索

      .intention-search-list

.intention-list-page
  +page-header-section
  +intention-search-list

  a-drawer(v-model:open="status.showDrawer" :title="drawer.title" :destroyOnClose="true" :width="480" :bodyStyle="{padding: 0}")
    component(:is="drawer.component" @close="status.showDrawer = false" v-bind="drawer.props")

  staffSelector(:visible="status.showStaffSelector" :multi="false" @select="(staffs) => {assignToStaff(staffs)}")
</template>
          
          
<style lang="scss" scoped>
.intention-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }
}

.intention-filter {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;

  &-item {
    display: flex;
    margin-bottom: 10px;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      padding: 0px 0;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 10px;
      line-height: 32px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }

    .active {
      color: #FF9111;
    }
  }
}

.intention-search {
  background-color: #fff;
  border-radius: 6px;

  &-head {
    // border-bottom: 1px solid #e8e8e8;
    justify-content: space-between;
    align-items: center;

    .intention, .follow-up {
      .time {
        color: #999;
      }
    }

    .position-tab-item {
      padding: 24px;

      h4 {
        font-size: 18px;
        transition: all .2s;
        cursor: pointer;
        margin: 0;
        color: #ddd;
      }

      .active {
        color: #FF9111;
        transition: all .2s;
      }
    }

    &__title {
      font-weight: bold;
      font-size: 16px;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;

  &__avatar {
    margin-right: 8px;
  }

  &__info {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
</style>