<template lang="pug">
mixin position-push-head-section
  a-page-header(
    title="推广详情",
    sub-title="",
    @back="()=>{$router.go(-1)}",
    style="padding: 0 0 8px;"
  )

mixin postion-push-detail-section
  a-spin(:spinning="status.loading")
    section.postion-push-detail-section
      .position-push__head
        .position-push__name {{detail.title}}
        .position-push__desc {{detail.comment}}

      .position-push__body
        .position(v-for="(item, index) in detail.positionSuggestions")
          a-row(:gutter="[16,16]")
            a-col(:span="16")
              router-link.position__title(:to="`/position/${item.position.id}/detail`") {{item.position.positionTitle}}
              .position__info
                a-space(:size="16")
                  span 公司: 
                    router-link(:to="`/customer/${item.position.customer.id}/detail`") {{item.position.customer.customerFullName}}
                  span 工作地: {{item.position.areaStr || '未知'}}
                  span 薪资: {{ formatSalary(item.position) }}
              .position__comment 推荐语：{{item.comment}}
            a-col.position__sider(:span="8")
              img(:src="item.qrimage")

mixin postion-push-sider-section
  section.postion-push-sider-section
    h2 职位列表二维码

    .position-push-sider-body
      img(:src="qrcode.aggregationCode")

.position-push-detail-page
  +position-push-head-section
  a-row(:gutter="[16,16]")
    a-col(:span="16")
      +postion-push-detail-section
    a-col(:span="8")
      +postion-push-sider-section
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { getChannelList } from '@/api/dictionary'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/user.store'
import { getPositionAggregationDetail } from '@/api/marketing'
import { message } from 'ant-design-vue'
import qrcodelib from 'qrcode'
import querystring from 'query-string'
import { formatSalary } from '@/utils/salary-helper'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const postionAggregationId = ref<number>(Number(route.params.id))

const WECHAT_PAGES = {
  POSITION_AGGREGATION_PAGE_BASE: 'https://e.smartdeer.co/mp/pages/member/direct-interview',
  POSITION_SINGLE_PAGE_BASE: 'https://e.smartdeer.co/mp/pages/member/job-progress'
}

const status = reactive({
  loading: false,
  qrcode: false,
})

const qrcode = reactive({
  aggregationCode: null as any
})

const detail = ref<any>({})

async function fetchPositionPushDetail(postionAggregationId: number) {
  status.loading = true
  try {
    const res = await getPositionAggregationDetail(postionAggregationId)
    detail.value = res.data
    detail.value.positionSuggestions = res.data.positionSuggestions.map((item: any, index: number) => {
      const suggestion = Object.assign({}, item, { qrimage: null })
      generateAggregationMiniprogramCode(suggestion)
      return suggestion
    })
    detail.value.positionSuggestions.sort((a:any, b:any) => {
      return a.sort - b.sort
    })
    generateAggregationQRCode()
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function generateAggregationQRCode() {
  status.qrcode = true
  try {
    const query = querystring.stringify({
      a: postionAggregationId.value,
      sid: detail.value.companyUserId,
      sn: detail.value.companyUserName
    })

    qrcodelib.toDataURL(`${WECHAT_PAGES.POSITION_AGGREGATION_PAGE_BASE}?${query}`, { margin: 0 }, (err, url) => {
      qrcode.aggregationCode = url
    })

  } catch (err: any) {
    message.error(err.message)
  }
  status.qrcode = false
}

async function generateAggregationMiniprogramCode(suggestion: any) {
  try {
    const query = querystring.stringify({
      p: suggestion.id,
      sid: detail.value.companyUserId,
      sn: detail.value.companyUserName
    })
    qrcodelib.toDataURL(`${WECHAT_PAGES.POSITION_SINGLE_PAGE_BASE}?${query}`, { margin: 0, width: 100 }, (err, url) => {
      suggestion.qrimage = url
    })
  } catch (err: any) {
    message.error(err.message)
  }
}

onMounted(() => {
  postionAggregationId.value = Number(route.params.id)
  fetchPositionPushDetail(postionAggregationId.value)
})

function getAnnualSalary(position: any) {
  const start = (position.salaryFrom * position.salaryCalMonth * 1000) / 10000
  const to = (position.salaryTo * position.salaryCalMonth * 1000) / 10000
  return `${start}-${to}万`
}
</script>
      
<style lang="scss" scoped>
.postion-push-detail-section {
  background-color: #fff;
  border-radius: 8px;

  .position-push {
    &__head {
      border-bottom: 1px solid #f0f0f0;
      padding: 24px;
    }

    &__name {
      font-size: 24px;
      font-weight: bold;
    }

    &__desc {
      color: #888;

    }

    &__body {
      padding: 24px;
      margin: -12px 0;
    }
  }
}

.postion-push-detail-section {
  background-color: #fff;
  border-radius: 8px;
  // padding: 24px;
}

.postion-push-sider-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;

  .position-push-sider-body {
    text-align: center;

    img {
      display: inline-block;
    }
  }
}

.position {
  background-color: #f9f9f9;
  margin: 12px 0;
  padding: 16px;

  &__title {
    font-size: 18px;
    font-weight: bold;
  }

  &__sider {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    img {
      width: 100px;
      height: 100px;
    }
  }

  &__comment {
    color: #999;
    margin: 8px 0;
  }
}
</style>