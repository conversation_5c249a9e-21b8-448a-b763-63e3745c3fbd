<script setup lang="ts">
import { reactive } from "@vue/reactivity"
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from "vue-router"
import { getChannelList } from '@/api/dictionary'
import { getPositionAggregationList, getPositionAggregationListByStaffId } from "@/api/marketing"
import { useUserStore } from "@/store/user.store"

interface OAny {
  [propName: string]: any;
}

const router = useRouter()

const status = reactive({
  initFilter: false,
  listLoading: false
})

const selectedTab = ref(1)

const allActivityPagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const myActivityPagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const allActivityList = ref<any[]>([])
const myActivityList = ref<any[]>([])

enum LIST_TYPE {
  ALL = 'ALL', MY = 'MY'
}

function handlePageChange(page: any, type: LIST_TYPE) {
  if (type === LIST_TYPE.ALL) {
    allActivityPagination.current = page.current
    fetchAllActivityList()
  } else {
    myActivityPagination.current = page.current
    fetchMyActivityList()
  }
}

const columnsConfig = [
  { title: '推广', key: 'title', dataIndex: 'title' },
  { title: '归属', key: 'company_user', dataIndex: 'companyUserName' },
  { title: '打开UV', key: 'uv', dataIndex: ['properties', 'uv'] },
  { title: '注册', key: 'registerCount', dataIndex: ['properties', 'registerCount'] },
  { title: '面试申请', key: 'interviewCount', dataIndex: ['properties', 'interviewCount'] },
  { title: '接受邀请', key: 'agreeInviteCount', dataIndex: ['properties', 'agreeInviteCount'] },
  { title: '操作', key: 'action', dataIndex: 'action' },
]
// 获取职位列表 
async function fetchAllActivityList() {
  status.listLoading = true
  try {
    const res = await getPositionAggregationList({
      size: allActivityPagination.pageSize,
      current: allActivityPagination.current
    })
    allActivityList.value = res.data.aggregations
    allActivityPagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

const userStore = useUserStore()

async function fetchMyActivityList() {
  status.listLoading = true
  try {
    const res = await getPositionAggregationListByStaffId(userStore.id, {
      size: myActivityPagination.pageSize,
      current: myActivityPagination.current
    })
    myActivityList.value = res.data.aggregations
    myActivityPagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

onMounted(() => {
  fetchMyActivityList()
})

function handleTabClick(tab: number) {
  selectedTab.value = tab
  if (tab === 1) {
    fetchMyActivityList()
  } else {
    fetchAllActivityList()
  }
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      router.push({
        path: `/marketing/activity/${record.id}/detail`
      })
    }
  }
}
</script>
      
<template lang="pug">
mixin page-header-section
  a-page-header(
    title="推广列表",
    sub-title="",
    @back="() =>{$router.go(-1)}",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      a-button(type="primary" @click="$router.push('/marketing/activity/create')") 新增推广

mixin activity-search-list
  a-spin(:spinning="status.listLoading")
    .activity-search
      .activity-search-head
        a-tabs(v-model:activeKey="selectedTab")
          a-tab-pane(:key="1" tab="我的推广")

            a-table(
              :columns="columnsConfig" 
              :data-source="myActivityList" 
              :scroll="{ x: 1200 }" 
              :pagination="myActivityPagination"
              :customRow="handleCustomRow"
              @change="(page) =>handlePageChange(page, LIST_TYPE.MY)"
              rowClassName="clickable"
            )
              template(#bodyCell="{ column, record }")
                template(v-if="column.dataIndex==='action'")
                  a-button(type="link") 查看详情

          a-tab-pane(:key="2" tab="全部推广")
            a-table(
              :columns="columnsConfig" 
              :data-source="allActivityList" 
              :scroll="{ x: 1200 }" 
              :pagination="allActivityPagination"
              :customRow="handleCustomRow"
              @change="(page) =>handlePageChange(page, LIST_TYPE.ALL)"
            )
              template(#bodyCell="{ column, record }")
                template(v-if="column.dataIndex==='action'")
                  a-button(type="link" @click.stop="$router.push(`/marketing/activity/staff/${record.companyUserId}/list`)") 查看用户列表
                  a-button(type="link") 详情
          template(#renderTabBar)
            .activity-tab-head
              a-space.position-tab-item(:size="24")
                h4(@click="handleTabClick(1)" :class="{'active': selectedTab === 1}") 我的推广
                h4(@click="handleTabClick(2)" :class="{'active': selectedTab === 2}") 全部推广

.activity-list-page
  +page-header-section
  +activity-search-list
</template>
      
      
<style lang="scss" scoped>
.activity-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }

  :deep(.clickable) {
    cursor: pointer;
  }
}

.activity-filter {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;

  &-item {
    display: flex;
    margin-bottom: 10px;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      padding: 0px 0;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 10px;
      line-height: 32px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }

    .active {
      color: #FF9111;
    }
  }
}

.activity-search {
  background-color: #fff;
  border-radius: 6px;

  &-head {
    justify-content: space-between;
    align-items: center;

    .activity-tab-head {
      padding: 24px;
    }

    .position-tab-item {
      h4 {
        font-size: 18px;
        transition: all .2s;
        cursor: pointer;
        margin: 0;
      }

      .active {
        color: #FF9111;
        transition: all .2s;
      }
    }

    &__title {
      font-weight: bold;
      font-size: 16px;
    }
  }
}
</style>