<template lang="pug">
mixin position-invite-head-section
  a-page-header(
    title="创建面试邀请",
    sub-title="",
    @back="goBack",
    style="padding: 0 0 8px;"
  )
    //- template(#extra)
    //-   a-button(type="primary" @click="$router.push('/customer/create')") 新增职位推荐

mixin position-invite-form
  .position-invite
    a-form.position-invite-form(:model="form" ref="formInstance")
      a-row
        a-col.form-label(:span="4") 标题
        a-col(:span="20")
          a-form-item()
            a-input(v-model:value="form.title")

        a-col.form-label(:span="4") 描述
        a-col(:span="20")
          a-form-item()
            a-textarea(v-model:value="form.comment")

        a-col.form-label(:span="4") 推荐职位
        a-col(:span="20")
          a-form
            draggable(
              v-model="selectedPosition"
              item-key="id"
            )
              template(#item="{element, index}")
                .position-item__card
                  .position-item__title {{ element.positionTitle }}
                  .position-item__info
                    a-space(:size="8")
                      span 工作地: {{element.areaStr}}
                      span 年薪: {{formatSalary(element)}}

                  a-form-item.position-item__form
                    a-textarea(placeholder="职位推荐语" :rows="4" v-model:value="element.comment")

                    template(#extra v-if="element.error")
                      .error {{element.error}}
                  .position-item__extra
                    a-space
                      a-button.help-icon(shape="circle" type="text")
                        template(#icon)
                          ExclamationCircleOutlined

                      a-popconfirm(
                        title="删除职位推荐"
                        ok-text="删除"
                        cancel-text="取消"
                        @confirm="remove(element)"
                      )
                        a-button(shape="circle" type="text")
                          template(#icon)
                            CloseCircleOutlined(:style="{color: 'red'}")

          a-form-item()
            a-button(@click="status.showPositionSelector = true") 添加职位

    .position-invite-form__footer
      a-button(@click="submit") 确定

.page-add-position-invite
  template(v-if="status.finish")
    a-result(title="创建成功" status="success")
      template(#extra)
        a-button(type="primary" @click="() => { status.finish = false }") 继续添加
        a-button(type="primary" ghost @click="() => {$router.push('/marketing/activity/list')}") 查看推广列表
  template(v-else)
    +position-invite-head-section
    +position-invite-form

  PositionSelector(v-model:visible="status.showPositionSelector" :selected="selectedPosition" @select="handelPositionSelect")
</template>

<script setup lang="ts">
import { ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { reactive, ref } from 'vue'
import { getChannelList } from '@/api/dictionary'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user.store'
import PositionSelector from '@/components/app/position-selector.vue'
import draggable from 'vuedraggable'
import { createPositionPush } from '@/api/marketing'
import { message } from 'ant-design-vue'
import { formatSalary } from '@/utils/salary-helper'

const router = useRouter()
const userStore = useUserStore()
const formInstance = ref()
const form = reactive({
  title: '',
  comment: '',
  positions: [] as any[]
})

const status = reactive({
  channelLoading: false,
  saving: false,
  showPositionSelector: false,
  finish: false,
})

function goBack() {
  router.go(-1)
}

const list = ref<any[]>([])

const selectedPosition = ref<any[]>([])

async function fetchChannelList() {
  status.channelLoading = false
  const res = await getChannelList({
    current: 1,
    size: 20
  })
  status.channelLoading = false
}

function getAnnualSalary(position: any) {
  const start = (position.salaryFrom * position.salaryCalMonth * 1000) / 10000
  const to = (position.salaryTo * position.salaryCalMonth * 1000) / 10000
  return `${start}-${to}万`
}

async function handelPositionSelect(positions: any[]) {
  const positionSet = new Set()
  selectedPosition.value.forEach((item, index) => {
    positionSet.add(item.id)
  })

  positions.forEach((item, index) => {
    if (!positionSet.has(item.id)) {
      selectedPosition.value.push(Object.assign({}, item, { comment: '' }))
    }
  })

  status.showPositionSelector = false
}

async function remove(position: any) {
  const index = selectedPosition.value.indexOf(position)
  selectedPosition.value.splice(index, 1)
}

async function submit() {
  let err = false
  selectedPosition.value.forEach((item) => {
    if (!item.comment) { item.error = '请填写推荐语'; err = true }
    else item.error = ''
  })
  if (err) return

  const positions = selectedPosition.value.map((item, index) => {
    return {
      positionId: item.id,
      comment: item.comment,
      sort: index + 1
    }
  })

  form.positions = positions

  try {
    const res = await createPositionPush(form)
    message.success('面试邀请创建成功！')
    status.finish = true
    form.comment = ''
    form.positions = []
    form.title = ''
    selectedPosition.value = []
  } catch (err: any) {
    message.error(err.message)
  }
}

</script>
      
<style lang="scss" scoped>
.position-invite {
  width: 50%;
  background-color: #fff;
  border-radius: 8px;

  &-form {
    padding: 24px;

    &__footer {
      padding: 12px 24px;
      border-top: 1px solid #f8f8f8;
      text-align: right;
    }
  }

  .form-label {
    line-height: 32px;
  }
}

.position-item {
  border-bottom: 1px solid #f8f8f8;
  margin-bottom: 12px;

  &__card {
    position: relative;
    border: 1px solid #f8f8f8;
    padding: 16px;
    margin-bottom: 12px;
    background-color: #f8f8f8;
  }

  &__title {
    font-weight: bold;
  }

  &__form {
    padding-top: 12px;

    .error {
      color: #F9470D;
    }

  }

  &__extra {
    position: absolute;
    right: 12px;
    top: 12px;

    .help-icon {
      cursor: help;
    }
  }
}
</style>
