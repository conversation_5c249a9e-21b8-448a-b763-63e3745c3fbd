<script setup lang="ts">
import { reactive } from "@vue/reactivity"
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from "vue-router"
import { getChannelList } from '@/api/dictionary'
import { getPositionAggregationList, getPositionAggregationListByStaffId } from "@/api/marketing"
import { getCompanyUserDetail } from "@/api/system/users"

const router = useRouter()
const route = useRoute()

const staff = ref<any>({ realName: '-' })
const staffId = ref<number>(Number(route.params.id))

const status = reactive({
  initFilter: false,
  listLoading: false
})

const selectedTab = ref(1)

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const list = ref<any[]>([])

const columnsConfig = [
  { title: '推广', key: 'title', dataIndex: 'title' },
  { title: '归属', key: 'company_user', dataIndex: 'companyUserName' },
  { title: '打开UV', key: 'uv', dataIndex: ['properties', 'uv'] },
  { title: '注册', key: 'registerCount', dataIndex: ['properties', 'registerCount'] },
  { title: '面试申请', key: 'interviewCount', dataIndex: ['properties', 'interviewCount'] },
  { title: '接受邀请', key: 'agreeInviteCount', dataIndex: ['properties', 'agreeInviteCount'] },
  { title: '操作', key: 'action', dataIndex: 'action' },
]
// 获取职位列表 
async function fetchAggregationList() {
  status.listLoading = true
  try {
    const res = await getPositionAggregationListByStaffId(
      staffId.value, {
      size: pagination.pageSize,
      current: pagination.current
    })
    list.value = res.data.aggregations
    pagination.total = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.listLoading = false
}

async function fetchStaffDetail(staffId: number) {
  try {
    const res = await getCompanyUserDetail(staffId)
    staff.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

function goBack() {
  router.go(-1)
}

function pageChange(pageInfo: any) {
  pagination.current = pageInfo.current
  fetchAggregationList()
}

function handleTabClick(tab: number) {
  selectedTab.value = tab
}

onMounted(() => {
  staffId.value = Number(route.params.id)
  list.value = []
  fetchAggregationList()
  fetchStaffDetail(staffId.value)
})


</script>
        
<template lang="pug">
mixin page-header-section
  a-page-header(
    :title="`${staff.realName}的推广列表`",
    sub-title="",
    @back="goBack",
    style="padding: 0 0 8px;"
  )

mixin filter-section
  .activity-filter
    a-spin(:spinning="status.initFilter")

      //- 省份选择部分
      .activity-filter-item
        .activity-filter-item__title 渠道
        .activity-filter-item__selector
          .activity-filter-item__option
            a-tree-select(
              :tree-data="filter.areas",
              :fieldNames="{ label: 'title', value: 'id' }",
              :style="{ width: '300px' }",
              allow-clear
              placeholder="在下拉列表中选择渠道",
              @change="handleChannelChange"
            )

mixin activity-search-list
  a-spin(:spinning="status.listLoading")
    .activity-search
      .activity-search-list
        a-table(:columns="columnsConfig" :data-source="list" :scroll="{ x: 1200 }" :pagination="pagination" @change="pageChange")
          template(#bodyCell="{ column, record }")
            template(v-if="column.dataIndex==='action'")
              a-button(type="link" @click="$router.push(`/marketing/activity/${record.id}/detail`)") 查看详情


.activity-list-page
  +page-header-section
  +activity-search-list
</template>
        
        
<style lang="scss" scoped>
.activity-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }
}

.activity-filter {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;

  &-item {
    display: flex;
    margin-bottom: 10px;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      padding: 0px 0;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 10px;
      line-height: 32px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }

    .active {
      color: #FF9111;
    }
  }
}

.activity-search {
  background-color: #fff;
  border-radius: 6px;

  &-head {
    height: 74px;
    // border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;

    .position-tab-item {
      h4 {
        font-size: 18px;
        transition: all .2s;
        cursor: pointer;
      }

      .active {
        color: #FF9111;
        transition: all .2s;
      }
    }

    &__title {
      font-weight: bold;
      font-size: 16px;
    }
  }
}
</style>