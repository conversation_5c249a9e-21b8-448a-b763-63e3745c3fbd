import { formatSalary } from "@/utils/salary-helper"
import dayjs from 'dayjs'

enum SALARY_UNIT {
  CNY = 0,
  USD = 1
}

enum SALARY_CALC_UNIT {
  DAY = 1,
  MONTH = 0,
  YEAR = 2
}

interface Salary {
  // 薪资描述信息
  salaryTo: number
  salaryFrom: number
  salaryTime: number
  salaryTimeUnit: SALARY_CALC_UNIT
  salaryUnit: SALARY_UNIT
}

interface Intention extends Salary, PositionIntention {
  candidateId: number

  // 意向类型
  kind: INTENTION_KIND

  // 描述sender和reciever双方的角色类型
  // 1 从C到CA
  // 2 从CA到C
  sendType: number

  // 用来描述意向的发出者信息和意向的表达状态
  senderId: number
  senderName: string
  senderStatus: number

  // 用来描述意向的接收者信息和意向的表达状态
  receiverId: number
  receiverName: string
  receiverStatus: number

  // 用来描述意向表达的对象
  object: Position | Customer | Aggregation,
  objectId: number
  objectType: OBJECT_TYPE

  // object 的 kind意向，接收和发出者双方的状态。
  // 如：对[curtomer]的[感兴趣]这个意向,是sender[ca]发给revicever[候选者]，sender的状态是[已发出]，reciever的状态是[接受]
  // 如：对[position]的[面试]这个意向，是sender[ca]发给reciever[候选者], sender的状态是[已发出], recicver的状态是[拒绝]

  createTime: number,
}

interface Position extends Salary {
  id: number
  positionTitle: string
  customer: Customer
}

interface Customer {
  id: number
  customerFullName: string
}

// 求职意向
interface PositionIntention extends Salary {
  workStatusStr: string,
  functionStr: string,
}

interface Aggregation {
  position: Position
}

interface ActionInfo {
  action: string,
  link: string | null,
  target: string | null,
  time: string,
}

enum INTENTION_KIND {
  // 主动
  INITIATIVE = 0,
  // 想要面试
  INTERVIEW = 1,
  // 感兴趣,想要了解更多
  INTREREST = 2,
  // 收藏
  COLLECT = 3,
}

enum SEND_TYPE {
  FROM_C_TO_B = 1,
  FROM_B_TO_C = 2
}

enum SEND_STATUS {
  DEFAULT = 0,
  INBOX = 1,
  AGREED = 2,
  REFUSE = 3,
}

// SELF(0, "self")
// COMPANY(1, "company"), 
// POSITION(2, "position"),
// POSITION_AGGREGATION(3, "positionAggregation"),
// POSITION_AGGREGATION_SUGGESTION(4, "positionAggregationSuggestion")

enum OBJECT_TYPE {
  AGGREGATION_POSITION = 4,
  NORMAL_POSITON = 2,
  NORMAL_CUSTOMER = 1,
  POSITION_INTENTION = 0,
}

function getPositionDetailLink(position: Position) {
  if (position) return `/position/${position.id}/detail`
  else return null
}

function getCustomerDetailLink(customer: Customer) {
  if (customer) return `/customer/${customer.id}/detail`
  else return null
}

function getCandidateDetailLink(candidateId: number) {
  if (candidateId) return `/marketing/candidate/${candidateId}/detail`
  return null
}

function formatPositionInfo(position: Position) {
  if (position) {
    return `${position.customer.customerFullName} | ${position.positionTitle} | ${formatSalary(position)}`
  } else {
    return '职位已删除'
  }
}

function formatCustomerInfo(customer: Customer) {
  if (customer) {
    return `${customer.customerFullName}`
  } else {
    return '客户已删除'
  }
}

function formatIntentionInfo(intention: Intention) {
  return `${intention.functionStr} | ${formatSalary(intention)} | ${intention.workStatusStr}`
}

function aggregationPositionInterview(aggregation: Aggregation, intention: Intention): ActionInfo | null {
  let action = ''
  if (intention.sendType === SEND_TYPE.FROM_B_TO_C) {
    if (intention.receiverStatus === SEND_STATUS.AGREED) {
      action = '接受邀请'
    } else if (intention.receiverStatus === SEND_STATUS.REFUSE) {
      action = '拒绝邀请'
    } else if (intention.receiverStatus === SEND_STATUS.INBOX) {
      action = '已收到邀请'
    } else {
      action = '无效意向'
    }
  } else if (intention.sendType === SEND_TYPE.FROM_C_TO_B) {
    action = '申请面试'
  }

  if (!aggregation.position) console.log(intention)

  return {
    action,
    target: formatPositionInfo(aggregation.position),
    link: getPositionDetailLink(aggregation.position),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function positionInterview(position: Position, intention: Intention): ActionInfo | null {
  let action = ''
  if (intention.sendType === SEND_TYPE.FROM_B_TO_C) {
    action = intention.receiverStatus === SEND_STATUS.AGREED ? '接受邀请' : '拒绝邀请'
  } else if (intention.sendType === SEND_TYPE.FROM_C_TO_B) {
    action = '申请面试'
  }
  return {
    action,
    target: formatPositionInfo(position),
    link: getPositionDetailLink(position),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function aggregationPositionInterest(aggregation: Aggregation, intention: Intention): ActionInfo | null {
  const position = aggregation.position
  return {
    action: '想了解职位详细',
    target: formatPositionInfo(position),
    link: getPositionDetailLink(position),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function postionCollect(position: Position, intention: Intention) : ActionInfo | null {
  return {
    action: '收藏职位',
    target: formatPositionInfo(position),
    link: getPositionDetailLink(position),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function positionInterest(position: Position, intention: Intention) : ActionInfo | null {
  return {
    action: '想了解职位详细',
    target: formatPositionInfo(position),
    link: getPositionDetailLink(position),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function customerInterest(customer: Customer, intention: Intention) : ActionInfo | null {
  return {
    action: '想了解公司详情',
    target: formatCustomerInfo(customer),
    link: getCustomerDetailLink(customer),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function customerCollect(customer: Customer, intention: Intention) : ActionInfo | null {
  return {
    action: '收藏公司',
    target: formatCustomerInfo(customer),
    link: getCustomerDetailLink(customer),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

function positionIntentionInitiative(intention: Intention) : ActionInfo | null {
  return {
    action: '完善求职意向',
    target: formatIntentionInfo(intention),
    link: getCandidateDetailLink(intention.candidateId),
    time: dayjs(intention.createTime).format('YYYY-MM-DD HH:mm')
  }
}

export function parseIntention(intention: Intention): ActionInfo | null {
  try {
    // 面试邀请，通过职位聚合
    if (intention.kind === INTENTION_KIND.INTERVIEW && intention.objectType === OBJECT_TYPE.AGGREGATION_POSITION)
      return aggregationPositionInterview(intention.object as Aggregation, intention)

    // 面试邀请，通过普通职位
    if (intention.kind === INTENTION_KIND.INTERVIEW && intention.objectType === OBJECT_TYPE.NORMAL_POSITON)
      return positionInterview(intention.object as Position, intention)

    // 对职位感兴趣，通过职位聚合
    if (intention.kind === INTENTION_KIND.INTREREST && intention.objectType === OBJECT_TYPE.AGGREGATION_POSITION)
      return aggregationPositionInterest(intention.object as Aggregation, intention)

    // 对职位感兴趣，通过职位聚合
    if (intention.kind === INTENTION_KIND.INTREREST && intention.objectType === OBJECT_TYPE.NORMAL_CUSTOMER)
      return customerInterest(intention.object as Customer, intention)

    // 对职位感兴趣，通过职位聚合
    if (intention.kind === INTENTION_KIND.INTREREST && intention.objectType === OBJECT_TYPE.NORMAL_POSITON)
      return positionInterest(intention.object as Position, intention)

    // 普通公司收藏
    if (intention.kind === INTENTION_KIND.COLLECT && intention.objectType === OBJECT_TYPE.NORMAL_CUSTOMER)
      return customerCollect(intention.object as Customer, intention)

    // 普通职位收藏
    if (intention.kind === INTENTION_KIND.COLLECT && intention.objectType === OBJECT_TYPE.NORMAL_POSITON)
      return postionCollect(intention.object as Position, intention)

    // 用户主动更新职位意向
    if (intention.kind === INTENTION_KIND.INITIATIVE && intention.objectType === OBJECT_TYPE.POSITION_INTENTION)
      return positionIntentionInitiative(intention)
  } catch (err: any) {
    console.log(err)
    return null
  }

  return null
}