import { SiteAdapter } from '@/service/multi/interface/site-adapter'
import { defineStore } from 'pinia'
import { unionGlobalTalentSearch } from '@/api/talent/talent'

const useDataStore = defineStore('GlobalTalentDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 1,
      pageSize: 20
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page },
    setListItem(id:number, item:any) {
      const index = this.list.findIndex((listItem: any) => listItem.id === id)
      if (index !== -1) {
        this.list[index] = { ...this.list[index], ...item }
      }
    },
    setPageSize(pageSize: number) {this.pageSize = pageSize}
  }
})

class GlobalTalentService implements SiteAdapter {
  private store
  private lastSearchParams: any = {}

  constructor() {
    this.store = useDataStore()
  }

  public get isLogin() {
    return this.store.isLogin
  }

  public get total() {
    return this.store.total
  }

  public get list() {
    return this.store.list
  }

  public get loading() {
    return this.store.loading
  }

  private getData(searchParams: any) {
    return unionGlobalTalentSearch(searchParams)
  }

  async checkLogin(): Promise<boolean> {
    this.store.setIsLogin(true)
    return true
  }

  async fetch(searchParams: any): Promise<any> {
    try {
      if (!this.isLogin) return []
      this.store.setLoading(true)

      this.lastSearchParams = searchParams
      searchParams.pageNumber = this.store.page
      searchParams.pageSize = this.store.pageSize
      const res = await this.getData(searchParams)
      const talents: any[] = res.data.talents.map(( item:any ) => ({
        ...item,
        site: 7,
        resumeUrl: item.id.toString()
      }))

      this.store.setTotal(res.data.size)
      this.store.setList(talents)
      this.store.setLoading(false)

      return talents
    } catch (err: any) {
      this.store.setLoading(false)
      return []
    }
  }

  public updateTalent(talent: any) {
    this.store.setListItem(talent.id, talent)
  }

  public nextPage() {
    this.store.setPage(this.store.page + 1)
    return this.fetch(this.lastSearchParams)
  }

  public prevPage() {
    this.store.setPage(this.store.page - 1)
    return this.fetch(this.lastSearchParams)
  }
}

export default GlobalTalentService