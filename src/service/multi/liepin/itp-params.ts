export const getItpQueryDto = (searchParams:any, page:number) => {
  return {
    search_id: searchParams.search_id,
    last_rank: page,
    search_count: searchParams.pageSize,
    position_keywords: searchParams.keyword,
    position_keywords_any: searchParams.match_any_keyword,
    fields: searchParams.fields,
    work_age_from: Number(searchParams.yoe_min),
    work_age_to: Number(searchParams.yoe_max),
    overseas_work: searchParams.oversea_work_experience,
    only_last_position: searchParams.only_last_job,
    cities: searchParams.current_cities,
    companies: searchParams.companies,
    only_last_company: searchParams.only_last_company,
    schools: searchParams.school,
    education_degree: Number(searchParams.degreeValue),
    major: searchParams.major,
    is_211: searchParams.edu_985_211,
    is_985: searchParams.edu_985_211,
    national_unified: searchParams.is_degree_upgrade,
    age_from: Number(searchParams.age_min),
    age_to: Number(searchParams.age_max),
    phone: searchParams.phone,
    email: searchParams.email,
    name: searchParams.name,
    gender: null
  }
}