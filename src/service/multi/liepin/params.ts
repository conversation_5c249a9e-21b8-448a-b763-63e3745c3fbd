
/**
 * 这个是猎聘的搜索参数， 有些参数是已经实现的， 有些是未实现的， 有些是未明确含义的
 * 先用定义的方式列在这里（方便高亮）， 有需要的时候再实现
 */
const a = {

  // --------------已有参数----------------
  // 关键字
  "keyword": "",
  // 目前城市 '010,020' 020上海 010北京
  "wantDqs": "", "nowDqs":"010,020",  
  // 年龄范围
  "ageLow":"28", "ageHigh":"40",
  // 性别
  "sex":"1",
  // 教育经历， 030-硕士
  "eduLevels": ["030"],
  // 工作年限
  "workYearsLow":"3", "workYearsHigh":"8",
  // 当前行业
  "industrys":"H0021,H0017",
  // 海外教育经历
  "studyAbroad": false,
  // 海外工作经验
  "workAbroad": false,
  // 当前页码， 从0开始， 即0表示第一页
  "curPage": 0,


  // --------------有价值的未实现的参数----------------
  // 职位名称
  "jobName": "",
  // 格式”华为,小米“
  "compName": "",
  // 公司筛选要求，0-不限，1-目前，2-过往
  "compPeriod": "0",
  // 统招要求, 040-统招本科, 030-统招硕士, 010-统招博士
  "eduLevelTzCode":"040",
  // 院校要求，1-985, 2-211, 3-双一流
  "schoolKindList":["1"],
  // 语言， 1-英语
  "languageSkills":["1"],
  // 期望薪资范围，万
  "wantSalaryLow":"20","wantSalaryHigh":"40",
  // 目前年薪，万
  "nowSalaryLow": "", "nowSalaryHigh": "",
  // 当前职位, ---- string， 未字典Code。
  "nowJobTitles": "",
  // 期望职位
  "wantJobTitles": "",
  // 管理经验
  "managerial": false,
  // 期望行业
  "wantIndustry": "",
  // 毕业院校
  "school": "",
  // 专业名称
  "major": "",
  // 关键字类型，1-任意关键字，0-全部关键字
  "anyKeyword": "0",


  // --------------未实现的参数，未明确含义----------------
  "onlyLatestWorkExp": "",
  "graduate": false,
  "eduLevelTz": false,
  "userHope": "",
  "modifytimeType": "",
  "languageContent": "",
  "nowCompKind": "",
  "resLanguage": "",
  "filterViewed": false,
  "filterChat": false,
  "filterDownload": false,
  "resumetype": "0",
  "sortType": "0",
  "skId": "",
  "fkId": "",
  "searchType": "0",
  "pageSize": "",
  "jobPeriod": "0",
  "version": "",
  "activeStatus": "",
  "pushId": "",
  "csId": ""
}

interface TSearchParams {
  keyword: string;
  eduLevels: string[];
  workYearsLow: string;
  workYearsHigh: string;
  studyAbroad: boolean;
  workAbroad: boolean;
  sex: string;
  ageLow: string;
  ageHigh: string;
  nowDqs: string;
  wantDqs: string;
  industrys: string;
  curPage: number;
  pageSize: number;
}

interface DegreeMap {
  [key: string]: string[];
}

function getDegree(degree: string) {
  const degreeMap: DegreeMap = {
    "0": [],
    "2": ["0100"],
    "3": ["060", "0100"],
    "4": ["060", "0100"],
    "5": ["050", "040", "030", "010"],
    "6": ["040", "030", "010"],
    "7": ["030", "010"],
    "8": ["030", "010"],
    "9": ["010"],
  };
  return degreeMap[degree];
}

interface GenderMap {
  [key: string]: string;
}

function getGender(gender: string) {
  const genderMap: GenderMap = {
    "0": "",
    "1": "1",
    "2": "0",
  };
  return genderMap[gender];
}

function getLocation(location: string[]) {
  const locationArr = [
    { key: "上海", value: "020" },
    { key: "广州", value: "050020" },
    { key: "北京", value: "010" },
    { key: "武汉", value: "170020" },
    { key: "咸宁", value: "170130" },
    { key: "湖北省", value: "170" },
    { key: "杭州", value: "070020" },
    { key: "成都", value: "280020" },
  ];
  let locationParam: string[] = [];
  location.forEach((item) => {
    for (let i = 0; i < locationArr.length; i++) {
      if (locationArr[i].key === item) {
        locationParam.push(locationArr[i].value);
      }
    }
  });
  return locationParam.join(",");
}

function getIndustry(industries: string[]) {
  const industryArr = [
    { key: "电信/通信技术", value: "H0018" },
    { key: "房地产", value: "H0027" },
    { key: "建筑业", value: "H0027" },
    { key: "金融", value: "H04" },
    { key: "游戏", value: "H0001" },
    { key: "区块链", value: "H0011" },
    { key: "云计算/大数据", value: "H0008" },
  ];
  let industryParam: string[] = [];
  industries.forEach((item) => {
    for (let i = 0; i < industryArr.length; i++) {
      if (industryArr[i].key === item) {
        industryParam.push(industryArr[i].value);
      }
    }
  });
  return industryParam.join(",");
}

export default function buildParams(originParams: any, page:number): TSearchParams {
  const searchParams: TSearchParams = {
    keyword: "",
    eduLevels: [],
    workYearsLow: "",
    workYearsHigh: "",
    studyAbroad: false,
    workAbroad: false,
    sex: "",
    ageLow: "",
    ageHigh: "",
    nowDqs: "",
    wantDqs: "",
    industrys: "",
    curPage: 0,
    pageSize: 100,
  }

  searchParams.keyword = originParams.keyword.join(" ")
  searchParams.eduLevels = getDegree(originParams.degreeValue)
  searchParams.workYearsLow = originParams.yoe_min
  searchParams.workYearsHigh = originParams.yoe_max
  searchParams.ageLow = originParams.age_min
  searchParams.ageHigh = originParams.age_max
  searchParams.industrys = getIndustry(originParams.industries)
  searchParams.nowDqs = getLocation(originParams.current_cities)
  searchParams.wantDqs = getLocation(originParams.expect_cities)
  searchParams.studyAbroad = originParams.overseas_education
  searchParams.workAbroad = originParams.oversea_work_experience
  searchParams.sex = getGender(originParams.gender)
  searchParams.curPage = page

  return searchParams
}
