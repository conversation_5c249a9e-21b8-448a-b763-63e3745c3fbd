import { urlEncodeFormData } from "../utils";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'

class Liepin {
  private _isLogin: boolean;

  constructor() {
    this._isLogin = false;
  }

  private getData(searchParams: any) {
    const formData = new FormData();
    formData.append("version", "V5");
    formData.append("searchParamsInputVo", JSON.stringify(searchParams));
    return window.RecruitSdk.request({
      url: "https://api-h.liepin.com/api/com.liepin.searchfront4r.h.search-resumes",
      method: "POST",
      data: urlEncodeFormData(formData),
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        needOrigin: "https://h.liepin.com",
        needReferer: "https://h.liepin.com",
        "X-Fscp-Std-Info": '{"client_id": "x"}',
        "X-Fscp-Trace-Id": "x",
        "X-Fscp-Version": "1.1",
        "X-Requested-With": "XMLHttpRequest",
        "X-Client-Type": "web",
      },
    });
  }

  private getLoginUser() {
    return window.RecruitSdk.request({
      url: "https://api-h.liepin.com/api/com.liepin.userh.get-personal-hcome-info",
      headers: {
        needOrigin: "https://h.liepin.com",
        needReferer: "https://h.liepin.com",
        "X-Fscp-Std-Info": '{"client_id": "x"}',
        "X-Fscp-Trace-Id": "x",
        "X-Fscp-Version": "1.1",
        "X-Requested-With": "XMLHttpRequest",
        "X-Client-Type": "web",
      },
      method: "POST",
    });
  }

  async checkLogin() {
    try {
      const res = await this.getLoginUser()

      this._isLogin = res.data.flag === 1
      if (this._isLogin) {
        console.log("猎聘登录成功")
      } else {
        console.log("请登录猎聘")
      }
    } catch (err: any) {
      console.error("猎聘登录失败", err);
    }

    return this._isLogin;
  }

  async fetch(queryParams: any): Promise<SearchResult> {
    const searchResult: SearchResult = {
      total: 0,
      list: [],
      maxRank: 0
    }

    if (!this._isLogin) {
      return searchResult;
    }

    try {
      const params = buildParams(queryParams, 0);

      const res = await this.getData(params);

      if (res) {
        searchResult.total = res.data.data.totalCnt
        searchResult.list = res.data.data.resList
      }
      return searchResult;
    } catch (err: any) {
      console.error("err", err);
      return searchResult;
    }
  }

  beforeOpen() {}
}

export default Liepin;
