import { urlEncodeFormData } from "../utils"
import buildParams from "./params"
import { getItpQueryDto } from './itp-params'
import { SearchResult } from '@/service/multi'
import { defineStore } from 'pinia'
import { SiteAdapter } from "../interface/site-adapter"
import { getMultiSearchTalentList, unionSearchTranslateParams } from "@/api/talent/talent"
import recruitSdk from "@/assets/js/RecruitSdk"

const useLiepinDataStore = defineStore('liepinDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 0,
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page },
  },
})

class Liepin implements SiteAdapter {
  private store;
  private lastSearchParams: any = {}
  private lastItpSearchParams:any = {}

  constructor() {
    this.store = useLiepinDataStore()
  }

  public get isLogin() { return this.store.isLogin }
  public get total() { return this.store.total }
  public get list() { return this.store.list }
  public get loading() { return this.store.loading }
  public get page() { return this.store.page }

  private async translateSearchParams(searchParams: any, page: number) {
    const res = await unionSearchTranslateParams(searchParams)
    const requestParams = res.data[2]
    const params = JSON.parse(requestParams)
    params.curPage = page
    return params
    
    // return buildParams(searchParams, page)
    // return searchParams
  }

  private getLoginUser() {
    return recruitSdk.request({
      url: "https://api-h.liepin.com/api/com.liepin.userh.get-user-base-info",
      headers: {
        needOrigin: "https://h.liepin.com",
        needReferer: "https://h.liepin.com",
        "X-Fscp-Std-Info": '{"client_id": "x"}',
        "X-Fscp-Trace-Id": "x",
        "X-Fscp-Version": "1.1",
        "X-Requested-With": "XMLHttpRequest",
        "X-Client-Type": "web",
      },
      method: "POST",
    })
  }

  private getData(searchParams: any) {
    const formData = new FormData();
    formData.append("version", "V5");
    formData.append("searchParamsInputVo", JSON.stringify(searchParams));
    return recruitSdk.request({
      url: "https://api-h.liepin.com/api/com.liepin.searchfront4r.h.search-resumes",
      method: "POST",
      data: urlEncodeFormData(formData),
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        needOrigin: "https://h.liepin.com",
        needReferer: "https://h.liepin.com",
        "X-Fscp-Std-Info": '{"client_id": "x"}',
        "X-Fscp-Trace-Id": "x",
        "X-Fscp-Version": "1.1",
        "X-Requested-With": "XMLHttpRequest",
        "X-Client-Type": "web",
      },
    })
  }

  async checkLogin(): Promise<boolean> {
    const res = await this.getLoginUser()
    this.store.setIsLogin(res.data.flag === 1)
    return this.isLogin
  }

  async beforeOpen(item: any) {
    console.log('item',item)
  }

  getResumeUrl() {
    return 'https://h.liepin.com/search/getConditionItem#session'
  }

  async fetch(searchParams: any, itpSerachParams:any, page: number = 0): Promise<any> {
    try {
      // 如果没有登录，则直接返回
      if (!this.isLogin) return []

      // 设置状态
      this.store.setLoading(true)
      this.store.setPage(page)

      // 转换参数
      this.lastSearchParams = searchParams
      this.lastItpSearchParams = itpSerachParams
      // const params = await this.translateSearchParams(searchParams, page)

      searchParams.curPage = page
      searchParams.pageSize = 100
      
      // 请求数据
      const res = await this.getData(searchParams)
      const liepingRawSearchResult = res.data.data.resList

      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = {
        lieDataDto: liepingRawSearchResult,
        queryDto: itpSerachParams,
      }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)
      const { aggregate } = aggregateListData.data

      // total从原始搜索结果中得到，list从aggregate中得到
      this.store.setTotal(res.data.data.totalCnt)
      this.store.setList(aggregate['2'] || [])
      this.store.setLoading(false)

      return liepingRawSearchResult
    } catch (err: any) {
      this.store.setLoading(false)
      return []
    }
  }

  async nextPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page + 1)
  }
  async prevPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page - 1)
  }
}

export default Liepin
