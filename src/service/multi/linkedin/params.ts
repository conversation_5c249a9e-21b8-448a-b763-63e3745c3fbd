interface DegreeMap {
  [key: string]: string;
}

function getDegree(degree: string) {
  const degreeMap: DegreeMap = {
    "0": "0,1,2,3,4",
    "1": "1,2,3,4",
    "2": "2,3,4",
    "3": "3,4",
    "4": "",
    "5": "0",
    "6": "0,1,2,3,4",
  };
  return degreeMap[degree];
}

function getSchoolIds(school: string[]) {
  const schoolMap = [
    { key: "华中科技大学", value: 81930 },
    { key: "电子科技大学", value: 149370 },
    { key: "西安电子科技大学", value: 24531 },
    { key: "上海交通大学", value: 27413 },
    { key: "清华大学", value: 14022 },
    { key: "北京大学", value: 20289 },
    { key: "清华大学", value: 14022 },
    { key: "复旦大学", value: 22859 },
  ];
  let schoolParam: number[] = [];
  school.forEach((item) => {
    for (let i = 0; i < schoolMap.length; i++) {
      if (schoolMap[i].key === item) {
        schoolParam.push(schoolMap[i].value);
      }
    }
  });
  return schoolParam.join(",");
}

function getIndustry(industry: string[]) {
  const industryMap = [
    { key: "软件开发", value: 4 },
    { key: "技术、信息和网络", value: 6 },
    { key: "电信业", value: 8 },
    { key: "IT服务与咨询", value: 96 },
    { key: "半导体制造业", value: 7 },
  ];
  let industryParam: number[] = [];
  industry.forEach((item) => {
    for (let i = 0; i < industryMap.length; i++) {
      if (industryMap[i].key === item) {
        industryParam.push(industryMap[i].value);
      }
    }
  });
  return industryParam.join(",");
}

function getCompanyIds(company: string[]) {
  const companyMap = [
    { key: "腾讯", value: 166328 },
    { key: "华为", value: 3014 },
    { key: "字节跳动", value: 6575553 },
    { key: "阿里巴巴集团", value: 3839570 },
    { key: "海思", value: 1026474 },
    { key: "微软", value: 1035 },
    { key: "米高蒲志", value: 3476 },
    { key: "Hays", value: 3486 },
    { key: "Hudson", value: 36011769 },
  ];
  let companyParam: number[] = [];
  company.forEach((item) => {
    for (let i = 0; i < companyMap.length; i++) {
      if (companyMap[i].key === item) {
        companyParam.push(companyMap[i].value);
      }
    }
  });
  return companyParam.join(",");
}

function getLocation(location: string[]) {
  const locationMap = [
    { key: "中国", value: ********* },
    { key: "广东省", value: ********* },
    { key: "上海市", value: ********* },
    { key: "深圳", value: ********* },
    { key: "湖北省", value: ********* },
    { key: "武汉", value: ********* },
    { key: "武汉市", value: ********* },
  ];
  let locationParam: number[] = [];
  location.forEach((item) => {
    for (let i = 0; i < locationMap.length; i++) {
      if (locationMap[i].key === item) {
        locationParam.push(locationMap[i].value);
      }
    }
  });
  return locationParam.join(",");
}

function getWorkTimes(yoeMin: number, yoeMax: number) {
  let workTimes = "";
  if (yoeMin === 0 && yoeMax === 1) {
    workTimes = "0";
  } else if (yoeMin === 1 && yoeMax === 3) {
    workTimes = "1";
  } else if (yoeMin === 3 && yoeMax === 5) {
    workTimes = "2";
  } else if (yoeMin === 5 && yoeMax === 10) {
    workTimes = "3";
  } else if (yoeMin === 10 && yoeMax > 10) {
    workTimes = "4";
  }
  return workTimes;
}

interface TSearchParams {
  keywords: string;
  positions: string;
  worktimes: string;
  cities: string;
  provinces: string;
  only_last_company: boolean;
  allcompanies: string;
  schools: string;
  industries: string;
  start: number;
  count: number;
}

export default function buildParams(originParams: any, page:number): TSearchParams {
  const searchParams: TSearchParams = {
    keywords: "",
    positions: "",
    worktimes: "",
    cities: "",
    provinces: "",
    only_last_company: false,
    allcompanies: "",
    schools: "",
    industries: "",
    start: page,
    count: 40,
  };

  console.log(originParams)
  searchParams.keywords = originParams.keywords.join(" ");
  searchParams.positions = originParams.positionName.join(" ");
  searchParams.worktimes = getWorkTimes(
    originParams.workAgeFrom,
    originParams.workAgeTo
  );
  searchParams.provinces = getLocation(originParams.current_cities);
  searchParams.cities = getLocation(originParams.current_cities);
  searchParams.only_last_company = originParams.only_last_company;
  searchParams.allcompanies = getCompanyIds(originParams.companies);
  searchParams.schools = getSchoolIds(originParams.school);
  searchParams.industries = getIndustry(originParams.industries);
  searchParams.start = page * 5;

  return searchParams;
}
