import { defineStore } from "pinia";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'
import { SiteAdapter } from "../interface/site-adapter";
import { getItpQueryDto } from "../liepin/itp-params";
import { getMultiSearchTalentList } from "@/api/talent/talent";
import recruitSdk from "@/assets/js/RecruitSdk"
import MD5 from 'crypto-js/md5';

// Custom function to encode only special characters while preserving Chinese
function encodeSpecialChars(str: string): string {
  return str.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, (match) => {
    return encodeURIComponent(match);
  });
}

const useDataStore = defineStore('LinkedinDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 0,
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) {
      console.log("Check Linkedin Login: ", isLogin)
      this.isLogin = isLogin
    },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page }
  },
})

class LinkedInService implements SiteAdapter {
  private store;
  private lastSearchParams: any = {}
  private lastItpSearchParams: any = {}

  constructor() {
    this.store = useDataStore()
  }

  public get isLogin() { return this.store.isLogin }
  public get total() { return this.store.total }
  public get list() { return this.store.list }
  public get loading() { return this.store.loading }
  public get page() { return this.store.page }

  private getLoginUser() {
    return recruitSdk.request({
      url: "https://www.linkedin.com/mysettings-api/settingsApiMiniProfile",
      method: "GET",
      headers: {
        "accept": "application/vnd.linkedin.normalized+json+2.1",
        "csrf-token": "ajax:3777971183678023312",
        "x-li-lang": "zh_CN",
        "x-li-page-instance": "urn:li:page:d_UNKNOWN_ROUTE_member-settings-desktop.index;Ryq9OJ4HQvi2itv0mjGfwg==",
        "x-li-track": '{"clientVersion":"0.2.*","osName":"web","timezoneOffset":8,"deviceFormFactor":"DESKTOP","mpName":"settings-web","displayDensity":2}',
        "x-restli-protocol-version": '2.0.0',
        "authority": "www.linkedin.com",
        "referer": "https://www.linkedin.com/mypreferences/d/"
      },
    })
  }

  private getData(searchParams: any) {
    let queryString = ''

    const queryKeyPair = {
      keywords: encodeSpecialChars(searchParams.keywords),
      flagshipSearchIntent: 'SEARCH_SRP',
      queryParameters: [
        { key: 'network', value: ['S', 'T'] },
        { key: 'geoUrn', value: [searchParams.geoUrn] },
        { key: 'resultType', value: ['PEOPLE'] }
      ],
      includeFiltersInResponse: 'false'
    }

    let linkedInQuery = []
    for (const key in queryKeyPair) {
      if (key === 'keywords' || key === 'flagshipSearchIntent' || key === 'includeFiltersInResponse') {
        let queryParametersString = key + ":" + queryKeyPair[key]
        linkedInQuery.push(queryParametersString)
      }
      if (key === 'queryParameters') {
        let queryParametersString = 'queryParameters:List('
        let queryParameterList = []
        for (const i in queryKeyPair[key]) {
          let paramKeyPair = '('
          paramKeyPair += 'key:' + queryKeyPair[key][i].key + ','
          paramKeyPair += 'value:List(' + queryKeyPair[key][i].value.join(',') + ')'
          paramKeyPair += ')'
          queryParameterList.push(paramKeyPair)
        }
        queryParametersString += queryParameterList?.join(',') + ')'
        linkedInQuery.push(queryParametersString)
      }
    }
    let linkedInFullQueryString = 'query:(' + linkedInQuery?.join(',') + ')'
    queryString = "variables=" +
      "(start:" +
      searchParams.start +
      ",origin:FACETED_SEARCH" +
      "," + linkedInFullQueryString +
      ")"
    
    // Generate dynamic queryId using MD5
    const queryId = MD5('voyagerSearchDashClusters' + Date.now()).toString()
    queryString += "&&queryId=" + "voyagerSearchDashClusters.ed237181fcdbbd288bfcde627a5e2a07"

    return recruitSdk.request({
      url: "https://www.linkedin.com/voyager/api/graphql?includeWebMetadata=true&" + queryString,
      method: "GET",
      headers: {
        "csrf-token": "ajax:3777971183678023312",
        "accept": "application/vnd.linkedin.normalized+json+2.1",
        "x-li-lang": "zh_CN",
        "x-li-page-instance": "urn:li:page:d_flagship3_search_srp_people;esXkkLICRW+XMoSMkVRyaQ==",
        "x-li-track": '{"clientVersion":"1.13.34617","mpVersion":"1.13.34617","osName":"web","timezoneOffset":8,"deviceFormFactor":"DESKTOP","mpName":"settings-web","displayDensity":2,"displayWidth":3024,"displayHeight":1964}',
        "x-restli-protocol-version": '2.0.0',
        "authority": "www.linkedin.com",
        "referer": "https://www.linkedin.com/mypreferences/d/"
      },
    })
  }

  async checkLogin(): Promise<boolean> {
    this.store.setLoading(true)
    const res = await this.getLoginUser()
    this.store.setIsLogin(res.data.data != undefined)
    this.store.setLoading(false)
    return this.isLogin
  }

  async fetch(searchParams: any, itpSerachParams: any, page: number = 0): Promise<any> {
    // 如果没有登录，则直接返回
    if (!this.isLogin) return []
    try {
      this.store.setLoading(true)
      this.store.setPage(page)

      console.log()

      const params = Object.assign({}, searchParams, {start: page * 5})
      this.lastSearchParams = params
      this.lastItpSearchParams = itpSerachParams
      const res = await this.getData(params)

      const rawSearchResult = []
      if (res) {
        const includedList = res.data.included
        for (const i in includedList) {
          if (includedList[i].template === 'UNIVERSAL') {
            rawSearchResult.push(includedList[i])
          }
        }
        const total = res.data.data.data.searchDashClustersByAll.paging.total
        this.store.setTotal(total)
      }

      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = {
        lingDataDto: rawSearchResult,
        queryDto: itpSerachParams
      }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)
      const { aggregate } = aggregateListData.data
      // total从原始搜索结果中得到，list从aggregate中得到
      this.store.setList(aggregate['4'] || [])
      this.store.setLoading(false)
      return rawSearchResult
    } catch (err: any) {
      console.log(err)
      this.store.setLoading(false)
      return []
    }
  }

  nextPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page + 1)
  }

  prevPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page - 1)
  }
}

export default LinkedInService;