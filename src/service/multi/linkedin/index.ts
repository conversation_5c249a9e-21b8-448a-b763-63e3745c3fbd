import { urlEncodeFormData } from "../utils";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'

class Linkedincn {
  private _isLogin: boolean;

  constructor() {
    this._isLogin = false;
  }

  private getData(searchParams: any) {
    let queryString = ''

    const queryKeyPair = {
      keywords: searchParams.keywords,
      flagshipSearchIntent: 'SEARCH_SRP',
      queryParameters: [
        {
          key: 'network',
          value: ['F', 'S', 'O']
        },
        {
          key: 'resultType',
          value: ['PEOPLE']
        }
      ],
      includeFiltersInResponse: 'false'
    }

    let linkedInQuery = []
    for (const key in queryKeyPair) {
      if (key === 'keywords' || key === 'flagshipSearchIntent' || key === 'includeFiltersInResponse') {
        let queryParametersString = key + ":" + queryKeyPair[key]
        linkedInQuery.push(queryParametersString)
      }
      if (key === 'queryParameters') {
        let queryParametersString = 'queryParameters:List('
        let queryParameterList = []
        for (const i in queryKeyPair[key]) {
          let paramKeyPair = '('
          paramKeyPair += 'key:' + queryKeyPair[key][i].key + ','
          paramKeyPair += 'value:List(' + queryKeyPair[key][i].value.join(',') + ')'
          paramKeyPair += ')'
          queryParameterList.push(paramKeyPair)
        }
        queryParametersString += queryParameterList.join(',') + ')'
        linkedInQuery.push(queryParametersString)
      }
    }
    let linkedInFullQueryString = 'query:(' + linkedInQuery.join(',') + ')'
    queryString = "variables=" +
      "(start:" +
      searchParams.start +
      ",origin:GLOBAL_SEARCH_HEADER" +
      "," + linkedInFullQueryString +
      ")"
    queryString += "&&queryId=voyagerSearchDashClusters.f2505dc75f25aae6acd3abdfdc3fee57"

    return window.RecruitSdk.request({
      url: "https://www.linkedin.com/voyager/api/graphql?" + queryString,
      method: "GET",
      headers: {
        "csrf-token": "ajax:3777971183678023312",
        "accept": "application/vnd.linkedin.normalized+json+2.1"
      },
    });
  }

  private getLoginUser() {

    return window.RecruitSdk.request({
      url: "https://www.linkedin.com/mysettings-api/settingsApiMiniProfile",
      method: "GET",
      headers: {
        "accept": "application/vnd.linkedin.normalized+json+2.1",
        "csrf-token": "ajax:3777971183678023312",
        "x-li-lang": "zh_CN",
        "x-li-page-instance": "urn:li:page:d_UNKNOWN_ROUTE_member-settings-desktop.index;Ryq9OJ4HQvi2itv0mjGfwg==",
        "x-li-track": '{"clientVersion":"0.2.*","osName":"web","timezoneOffset":8,"deviceFormFactor":"DESKTOP","mpName":"settings-web","displayDensity":2}',
        "x-restli-protocol-version": '2.0.0',
        "authority": "www.linkedin.com",
        "referer": "https://www.linkedin.com/mypreferences/d/"
      },
    });
  }

  async checkLogin() {
    try {
      console.log('start check linkedin login')
      const res = await this.getLoginUser();

      this._isLogin = res.statusText === "OK" || res.statusText === "";

      if (this._isLogin) {
        console.log("领英职场登录成功");
      } else {
        console.log("请登陆领英职场");
      }
    } catch (err: any) {
      console.error("领英中国登陆失败", err);
    }

    return this._isLogin;
  }

  async fetch(queryParams: any): Promise<SearchResult> {
    const searchResult: SearchResult = {
      total: 0,
      list: [],
      maxRank: 0
    }

    if (!this._isLogin) {
      return searchResult;
    }

    try {
      const params = buildParams(queryParams, 0);

      const res = await this.getData(params);
      if (res) {
        const includedList = res.data.included
        for (const i in includedList) {
          if (includedList[i].template === 'UNIVERSAL') {
            searchResult.list.push(includedList[i])
          }
        }
        searchResult.total = res.data.data.data.searchDashClustersByAll.paging.total
      }
      return searchResult
    } catch (err: any) {
      console.error("err", err);
      return searchResult;
    }
  }

  beforeOpen() {}
}

export default Linkedincn;
