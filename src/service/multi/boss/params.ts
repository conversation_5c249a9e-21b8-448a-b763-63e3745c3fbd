interface DegreeMap {
  [key: string]: string;
}

function getDegree(degree: number) {
  const degreeMap: DegreeMap = {
    "0": "",
    "2": "",
    "3": "206,205",
    "4": "208,205",
    "5": "202,205",
    "6": "203,205",
    "7": "204,205",
    "8": "204,205",
    "9": "205",
  };
  return degreeMap[degree.toString()];
}

interface GenderMap {
  [key: string]: number;
}

function getGender(gender: number) {
  const genderMap: GenderMap = {
    "0": -1,
    "1": 1,
    "2": 0,
  };
  return genderMap[gender.toString()];
}

function getAge(ageMin: number, ageMax: number) {
  let age = [-1, -1];
  if (ageMin) {
    age[0] = ageMin;
  }
  if (ageMax) {
    age[1] = ageMax;
  }
  return age.join(",");
}

function getWorkExperience(yearMin: number, yearMax: number) {
  let experience = [-1, -1];
  if (yearMin) {
    experience[0] = yearMin;
  }
  if (yearMax) {
    experience[1] = yearMax;
  }
  return experience.join(",");
}

interface TSearchParams {
  keywords: string;
  companyNamesForHunter: string;
  degree: string;
  schoolLevel: string;
  experience: string;
  age: string;
  gender: number;
  applyStatus: string;
  firstDegree: number;
  city: string;
  page: number;
  size: number;
}

export default function buildParams(originParams: UnionSearchParams, page:number) {
  const searchParams: TSearchParams = {
    keywords: "",
    companyNamesForHunter: "string",
    degree: "",
    schoolLevel: "",
    experience: "",
    age: "",
    gender: -1,
    applyStatus: "",
    firstDegree: -1,
    city: "",
    page: 1,
    size: 100,
  };
  searchParams.keywords = originParams.keywords.join(" ");
  searchParams.companyNamesForHunter = originParams.companies.join(",");
  searchParams.degree = getDegree(originParams.educationDegree);
  searchParams.gender = getGender(originParams.gender);
  searchParams.age = getAge(originParams.ageFrom, originParams.ageTo);
  searchParams.experience = getWorkExperience(
    originParams.workAgeFrom || 0,
    originParams.workAgeTo || 0
  );
  searchParams.page = page + 1;
  // searchParams.size = originParams.pageSize;

  return searchParams;
}
