import buildParams from "./params";
import { SearchResult } from '@/service/multi'

class Boss {
  private _isLogin: boolean;

  constructor() {
    this._isLogin = false;
  }

  private getData(searchParams: any) {
    return window.RecruitSdk.request({
      url: "https://www.zhipin.com/wapi/zpitem/web/boss/search/geeks.json",
      method: "GET",
      params: searchParams,
    });
  }

  private getLoginUser() {
    return window.RecruitSdk.request({
      url: "https://www.zhipin.com/wapi/zppassport/get/zpToken",
      method: "GET",
    });
  }

  async checkLogin() {
    try {
      const res = await this.getLoginUser();
      this._isLogin = res.data.message === "Success";
      if (this._isLogin) {
        console.log("Boss直聘登录成功");
      } else {
        console.log("请登录Boss直聘");
      }
    } catch (err: any) {
      console.error("Boss 登陆失败", err);
    }

    return this._isLogin;
  }

  async fetch(queryParams: any): Promise<SearchResult> {
    const searchResult: SearchResult = {
      total: 0,
      list: [],
      maxRank: 0
    }

    if (!this._isLogin) {
      return searchResult;
    }

    try {
      const params = buildParams(queryParams, 0);

      const res = await this.getData(params);

      if (res) {
        searchResult.list = res.data.zpData.geeks
      }
      return searchResult
    } catch (err: any) {
      console.error("err", err);
      return searchResult;
    }
  }

  beforeOpen(lid: string, expectId: number, secureId: string) {
    if (lid && expectId && secureId) {
      window.RecruitSdk.setCache({
        currentLid: lid,
        currentExpectId: expectId,
        currentSecurityId: secureId,
      })
    }
  }
}

export default Boss;
