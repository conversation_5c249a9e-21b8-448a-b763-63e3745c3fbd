import { defineStore } from "pinia";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'
import { SiteAdapter } from "../interface/site-adapter";
import { getItpQueryDto } from "../liepin/itp-params";
import { getMultiSearchTalentList } from "@/api/talent/talent";

import recruitSdk from "@/assets/js/RecruitSdk";

const useDataStore = defineStore('bossDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      error: null,
      page: 0
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setError(error: any) { this.error = error },
    setPage(page: number) { this.page = page },
  },
})

class BossService implements SiteAdapter {
  private store;
  private lastSearchParams: any = {}
  private lastItpSearchParams: any = {}

  constructor() {
    this.store = useDataStore()
  }

  public get isLogin() {
    return this.store.isLogin
  }

  public get total() {
    return this.store.total
  }

  public get list() {
    return this.store.list
  }

  public get loading() {
    return this.store.loading
  }

  public get error() {
    return this.store.error
  }

  public get page() {
    return this.store.page
  }

  private getLoginUser() {
    return recruitSdk.request({
      url: "https://www.zhipin.com/wapi/zppassport/get/zpToken",
      method: "GET",
    })
  }

  private getData(searchParams: any) {
    return recruitSdk.request({
      url: "https://www.zhipin.com/wapi/zpitem/web/boss/search/geeks.json",
      method: "GET",
      params: searchParams,
    })
  }

  async checkLogin(): Promise<boolean> {
    this.store.setLoading(true)
    try {
      const res = await this.getLoginUser()
      this.store.setIsLogin(res.data.message === "Success")
      this.store.setError(res.data.message === "Success" ? null : res.data.message)
    } catch (err: any) {
      console.log(err.message)
      this.store.setIsLogin(false)
    }
    this.store.setLoading(false)
    return this.isLogin
  }

  async fetch(searchParams: any, itpSerachParams: any, page: number = 0): Promise<any> {
    // 如果没有登录，则直接返回
    if (!this.isLogin) return []
    this.store.setLoading(true)
    this.store.setPage(page)
    searchParams.page = page + 1
    this.lastSearchParams = searchParams
    this.lastItpSearchParams = itpSerachParams
    const res = await this.getData(searchParams)

    // 如果请求不成功，则设置错误
    if (res.data.message !== 'Success') {
      this.store.setLoading(false)
      this.store.setError(res.data.message === "Success" ? null : res.data.message)
      return []
    }

    const rawSearchResult = res.data.zpData.geeks

    try {
      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = {
        bossDataDto: rawSearchResult,
        queryDto: itpSerachParams,
      }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)
      const { aggregate } = aggregateListData.data
      // total从原始搜索结果中得到，list从aggregate中得到
      this.store.setTotal(res.data.zpData.totalCount)
      this.store.setList(aggregate['5'] || [])
      this.store.setLoading(false)
      return rawSearchResult
    } catch (err: any) {
      this.store.setLoading(false)
      console.log(err)
      return []
    }
  }

  nextPage() {
    this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page + 1)
  }

  prevPage() {
    this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page - 1)
  }

  async beforeOpen(lid: string, expectId: number, secureId: string) {
    if (lid && expectId && secureId) {
      recruitSdk.setCache({
        currentLid: lid,
        currentExpectId: expectId,
        currentSecurityId: secureId,
      })
    }
  }

  translateParams(params: UnionSearchParams) {

    const genderMap = [-1, 0, 1]

    function getExperienceValue(value: number | null) {
      if (!value) return -1
      else if (value > 10) return 11
      else return value
    }

    // return  {
    //   page: 1,
    //   jobId: '',
    //   keywords: params.keywords.join(' '),
    //   tag: '',
    //   // city: *********,
    //   gender: genderMap[params.gender],
    //   experience: `${getExperienceValue(params.workAgeFrom)},${params.workAgeTo || -1}`,
    //   salary: 35,50,
    //   age: 25,30,
    //   schoolLevel: 1103,1104,
    //   applyStatus: -1,
    //   degree: 203,204,
    //   switchFreq: 0,
    //   manageExperience: 0,
    //   geekJobRequirements: 0,
    //   exchangeResume: 0,
    //   viewResume: 0,
    //   firstDegree: 0,
    //   queryAnd: 0,
    //   companyNamesForHunter: ,
    //   companySearchTypeForHunter: 0,
    //   select: true,
    //   defaultCondition: 2,
    //   source: 1,
    //   filterParams: {"manageExperience":0,"region":{"cityCode":"-1","cityName":"全国","areas":[]}},
    //   cityCodeStr: ,
    // }
  }
}

export default BossService;
