import Liepin from "./liepin";
import <PERSON><PERSON><PERSON> from "./maimai";
import Boss from "./boss";
import SmartDeerAppTalent from '@/service/multi/smartdeer'

export interface SearchResult {
  total: number;
  list: any[];
  maxRank: number;
}

class Multi {
  private loginStatus: {
    liepin: boolean;
    maimai: boolean;
    linkedIn: boolean;
    boss: boolean;
  };

  private pagination: {
    all: number;
    rank: number;
    itp: number;
    liepin: number;
    maimai: number;
    linkedIn: number;
    boss: number;
    smartdeer: number;
  };

  private boss: Boss;
  private liepin: Liepin;
  private maimai: Maimai;
  // private linkedincn: Linkedincn;
  private smartdeer: SmartDeerAppTalent;

  constructor() {
    this.loginStatus = {
      liepin: false,
      maimai: false,
      linkedIn: false,
      boss: false,
    };

    this.pagination = {
      all: 0,
      rank: 0,
      itp: 0,
      liepin: 0,
      maimai: 0,
      linkedIn: 0,
      boss: 0,
      smartdeer: 0,
    };

    this.boss = new Boss();
    this.liepin = new Liepin();
    this.maimai = new Maimai();
    // this.linkedincn = new Linkedincn();
    this.smartdeer = new SmartDeerAppTalent();
  }

  async isLogin() {
    const [] = await Promise.all([
      // this.liepin.checkLogin(),
      // this.maimai.checkLogin(),
      // this.linkedincn.checkLogin(),
      // this.boss.checkLogin(),
    ]);

    // this.loginStatus.liepin = liepin;
    // this.loginStatus.maimai = maimai;
    // this.loginStatus.linkedIn = linkedIn;
    // this.loginStatus.boss = boss;

    return this.loginStatus;
  }

  async getLiePin(queryParams: any) {
    // if (!this.loginStatus.liepin) return [];

    return await this.liepin.fetch(queryParams);
  }

  async getMaiMai(queryParams: any) {
    // if (!this.loginStatus.maimai) return [];

    return await this.maimai.fetch(queryParams);
  }

  async getLinkedInCn(queryParams: any) {
    // if (!this.loginStatus.linkedIn) return [];

    // return await this.linkedincn.fetch(queryParams);
  }

  async getBoss(queryParams: any) {
    // if (!this.loginStatus.boss) return [];

    return await this.boss.fetch(queryParams);
  }

  async getSmartDeer(queryParams: any) {
    return await this.smartdeer.fetch(queryParams);
  }

  async search(queryParams: any) {
    const promiseList = [
      // this.liepin.fetch(queryParams),
      this.maimai.fetch(queryParams),
      // this.linkedincn.fetch(queryParams),
      this.boss.fetch(queryParams),
      this.smartdeer.fetch(queryParams),
    ];

    console.time("multi");
    // 这里也可以进行优化？？
    // const [lieDataDto, maiDataDto, lingDataDto, bossDataDto, smartdeerDataDto] =
    //   await Promise.all(promiseList);
    const [maiDataDto, lingDataDto, bossDataDto, smartdeerDataDto] =
      await Promise.all(promiseList);
    console.timeEnd("multi");

    return {
      maiDataDto,
      lingDataDto,
      bossDataDto,
      smartdeerDataDto,
    };
  }

  async searchTwice(queryParams: any) {

    const promiseList = [
      this.search(queryParams),
      this.search({ ...queryParams, page: queryParams.page + 1 }),
    ];

    console.time("multi twice");
    const [one, two]: any = await Promise.all(promiseList);

    const merge: any = {};
    Object.keys(one).forEach((key: string) => {
      merge[key] = [...one[key], ...two[key]];
    });

    console.timeEnd("multi twice");

    return merge;
  }

  async beforeOpenResume(rowData: any) {

  }
}

export default Multi;
