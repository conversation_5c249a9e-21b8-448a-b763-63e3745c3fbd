import buildParams from "./params";
import { urlEncodeFormData } from "../utils";
import { SearchResult } from '@/service/multi'

class SmartDeerAppTalent {

  constructor() {
  }

  private getData(searchParams: any) {
    return window.RecruitSdk.request({
      url: "https://global-recommend.smartdeer.work/talent/talent_search",
      method: "POST",
      data: JSON.stringify(searchParams),
      headers: {
        "Content-Type": "application/json",
        "app_key": "juoRYXf1q65cDgayvGpXP5fIaB8F7XlrvhyDYlz0"
      }
    });
  }

  async fetch (queryParams: any): Promise<SearchResult> {
    const searchResult: SearchResult = {
      total: 0,
      list: [],
      maxRank: 0
    }
    try {
      const params = buildParams(queryParams)
      const res = await this.getData(params)
      if (res) {
        searchResult.total = res.data.total
        searchResult.list = res.data.data
        searchResult.maxRank = res.data.max_talent_rank
      }
      return searchResult
    } catch (err: any) {
      console.error("err", err)
      return searchResult
    }
  }

}

export default SmartDeerAppTalent;
