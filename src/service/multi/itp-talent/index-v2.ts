import { defineStore } from "pinia";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'
import { SiteAdapter } from "../interface/site-adapter";
import { getItpQueryDto } from "../liepin/itp-params";
import { getMultiSearchTalentList, iptTalentUnionSearch } from "@/api/talent/talent";
import { setSearchShowBury } from "@/api/track";

const useDataStore = defineStore('ItpTalentDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 1,
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page }
  },
})

class ItpTelentService implements SiteAdapter {
  private store
  private lastSearchParams: any = {}
  private lastPageRank: number = 0
  private lastListCount: number = 0

  constructor() {
    this.store = useDataStore()
  }

  public get isLogin() {
    return this.store.isLogin
  }

  public get total() {
    return this.store.total
  }

  public get list() {
    return this.store.list
  }

  public get loading() {
    return this.store.loading
  }

  private getData(searchParams: any) {
    return iptTalentUnionSearch(searchParams)
  }

  async checkLogin(): Promise<boolean> {
    this.store.setIsLogin(true)
    return true
  }

  async fetch(searchParams: any, rank: number = 0): Promise<any> {

    try {
      // 如果没有登录，则直接返回
      if (!this.isLogin) return []
      this.store.setLoading(true)

      // 保存这次搜索的参数
      this.lastSearchParams = searchParams
      searchParams.last_rank = rank === 0 ? 0 : this.lastPageRank
      // const queryDto = getItpQueryDto(searchParams, this.lastPageRank)

      // 该请求只用于获取total
      const res = await this.getData(searchParams)
      const result = JSON.parse(res.data)
      this.store.setTotal(result.total)

      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = { queryDto: searchParams }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)

      const { aggregate } = aggregateListData.data
      const searchList = aggregate['1'] || []

      console.log(searchList)

      this.store.setList(searchList)
      this.store.setLoading(false)

      this.lastPageRank = aggregateListData.data.maxItpTalentRank
      this.lastListCount = aggregate['1'].length

      if (searchList && searchList.length > 0) {
        const buryItems = searchList.map((item: any, index: number) => {
          return {
            trackId: item.trackId,
            talentId: item.itpTalentId,
            rank: index,
          }
        })
        setSearchShowBury({ action: 3, site: 1, items: buryItems })
      }
      return aggregate['1']
    } catch (err: any) {
      console.log(err.message)
      this.store.setLoading(false)
      return []
    }
  }

  public nextPage() {
    this.store.setPage(this.store.page + 1)
    return this.fetch(this.lastSearchParams, this.lastPageRank)
  }

  public prevPage() {
    this.store.setPage(this.store.page - 1)
    return this.fetch(this.lastSearchParams, this.lastPageRank - this.lastListCount)
  }
}

export default ItpTelentService