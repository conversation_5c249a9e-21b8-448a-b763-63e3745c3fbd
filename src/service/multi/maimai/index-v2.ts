import { defineStore } from "pinia";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'
import { SiteAdapter } from "../interface/site-adapter";
import { getItpQueryDto } from "../liepin/itp-params";
import { getMultiSearchTalentList } from "@/api/talent/talent";
import { v4 } from 'uuid'
import recruitSdk from "@/assets/js/RecruitSdk";

const useMaimaiDataStore = defineStore('maimaiDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 0,
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page },
  },
})

class MaimaiService implements SiteAdapter {
  private store;
  private lastSearchParams: any = {}
  private lastItpSearchParams: any = {}

  constructor() { this.store = useMaimaiDataStore() }
  public get isLogin() { return this.store.isLogin }
  public get total() { return this.store.total }
  public get list() { return this.store.list }
  public get loading() { return this.store.loading }
  public get page() { return this.store.page }

  private getLoginUser() {
    return recruitSdk.request({
      url: "https://maimai.cn/api/ent/common/v1/const?channel=www&version=1.0.0",
      method: "GET",
    })
  }

  private getData(searchParams: any) {
    return recruitSdk.request({
      url: "https://maimai.cn/api/ent/discover/search?channel=www&version=1.0.0",
      method: "POST",
      data: JSON.stringify(searchParams),
      headers: {
        "Content-Type": "text/plain;charset=UTF-8",
      },
    })
  }

  async checkLogin(): Promise<boolean> {
    console.log('maimai check login!')
    this.store.setLoading(true)
    try {
      const res = await this.getLoginUser()
      this.store.setIsLogin(res.data.result === "ok" || !res.data.result)
    } catch (err:any) {
      console.log(err.message)
      this.store.setIsLogin(false)
    }
    this.store.setLoading(false)
    return this.isLogin
  }

  async fetch(searchParams: any, itpSerachParams: any, page: number = 0): Promise<any> {
    if (!this.isLogin) return []

    try {
      this.store.setLoading(true)
      this.store.setPage(page)

      searchParams.page = page
      searchParams.size = 100
      searchParams.sessionid = v4()
      searchParams.deletesessionid = this.lastItpSearchParams.sessionid || v4()

      this.lastSearchParams = searchParams
      this.lastItpSearchParams = itpSerachParams
      const res = await this.getData({ search: searchParams })
      const rawSearchResult = res.data.data.list

      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = {
        maiDataDto: rawSearchResult,
        queryDto: itpSerachParams,
      }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)
      const { aggregate } = aggregateListData.data
      // total从原始搜索结果中得到，list从aggregate中得到
      this.store.setTotal(res.data.data.total)
      this.store.setList(aggregate['3'] || [])
      this.store.setLoading(false)
      return rawSearchResult
    } catch (err: any) {
      this.store.setLoading(false)
      return []
    }
  }

  nextPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page + 1)
  }

  prevPage() {
    return this.fetch(this.lastSearchParams, this.lastItpSearchParams, this.store.page - 1)
  }
}

export default MaimaiService;
