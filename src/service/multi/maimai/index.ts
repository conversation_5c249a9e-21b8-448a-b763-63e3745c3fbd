import buildParams from "./params";
import { SearchResult } from '@/service/multi'

class Maimai {
  private _isLogin: boolean;

  constructor() {
    this._isLogin = false;
  }

  private getData(searchParams: any) {
    return window.RecruitSdk.request({
      url: "https://maimai.cn/api/ent/discover/search?channel=www&version=1.0.0",
      method: "POST",
      data: JSON.stringify(searchParams),
      headers: {
        "Content-Type": "text/plain;charset=UTF-8",
      },
    });
  }

  private getLoginUser() {
    return window.RecruitSdk.request({
      url: "https://maimai.cn/api/ent/common/v1/const?channel=www&version=1.0.0",
      method: "GET",
    });
  }

  async checkLogin() {
    try {
      const res = await this.getLoginUser();

      this._isLogin = res.data.result === "ok" || !res.data.result;

      if (this._isLogin) {
        console.log("脉脉登录成功");
      } else {
        console.log("请登录脉脉");
      }
    } catch (err: any) {
      console.error("脉脉登陆失败", err);
    }

    return this._isLogin;
  }

  async fetch(queryParams: any): Promise<SearchResult> {
    const searchResult: SearchResult = {
      total: 0,
      list: [],
      maxRank: 0
    }
    if (!this._isLogin) {
      return searchResult
    }

    try {
      const params = buildParams(queryParams, 0);

      const res = await this.getData(params);
      if (res) {
        searchResult.total = res.data.data.total
        searchResult.list = res.data.data.list
      }
      return searchResult
    } catch (err: any) {
      console.error("err", err);
      return searchResult
    }
  }

  beforeOpen() {}
}

export default Maimai;
