/**
 * 学历
 * */
const DICT_DEGREE: any = {
  "0": "0,1,2,3,4",
  "2": "0",
  "3": "0,1,2,3,4",
  "4": "0,1,2,3,4",
  "5": "0,1,2,3,4",
  "6": "1,2,3,4",
  "7": "2,3,4",
  "8": "2,3,4",
  "9": "4",
};

function getProvinceLocation(location: string[]) {
  let locationParam: string[] = [];
  location.forEach((item) => {
    if (item.indexOf("省") !== -1 || item.indexOf("自治区") !== -1) {
      locationParam.push(item);
    }
  });
  return locationParam.join(",");
}

function getCityLocation(location: string[]) {
  let locationParam: string[] = [];
  location.forEach((item) => {
    if (item.indexOf("省") === -1 && item.indexOf("自治区") === -1) {
      locationParam.push(item);
    }
  });
  return locationParam.join(",");
}

/**
 * 行业
 * */
const DICT_INDUSTRY = [
  { key: "金融", value: 2 },
  { key: "IT/互联网", value: 1 },
  { key: "房地产", value: 3 },
  { key: "建筑业", value: 3 },
  { key: "商业服务", value: 4 },
  { key: "公共事业", value: 5 },
  { key: "文化娱乐", value: 6 },
  { key: "旅游", value: 6 },
  { key: "本地生活", value: 7 },
  { key: "营销", value: 8 },
  { key: "生产制造", value: 9 },
  { key: "电信/通信技术", value: 11 },
  { key: "商贸", value: 12 },
  { key: "医疗健康", value: 13 },
  { key: "教育", value: 14 },
  { key: "能源矿产", value: 15 },
  { key: "交通运输", value: 16 },
  { key: "航空/航天", value: 16 },
  { key: "农业", value: 17 },
  { key: "政府", value: 18 },
  { key: "不限", value: 19 },
];

function getIndustry(industry: string[]) {
  let industryParam: string[] = [];
  industry.forEach((item) => {
    for (let i = 0; i < DICT_INDUSTRY.length; i++) {
      if (DICT_INDUSTRY[i].key === item) {
        industryParam.push(item);
      }
    }
  });
  return industryParam.join(",");
}

interface TSearchParams {
  query: string;
  positions: string;
  worktimes: string;
  cities: string;
  provinces: string;
  degrees: string;
  professions: string;
  // allcompanies: string;
  is_211: number;
  is_985: number;
  schools: string;
  is_exclude_low_user: string;
  page: number;
  size: number;
}

export default function buildParams(originParams: UnionSearchParams, page:number) {
  const searchParams: TSearchParams = {
    query: "",
    positions: "",
    worktimes: "",
    cities: "",
    provinces: "",
    degrees: "",
    professions: "",
    // allcompanies: "",
    is_211: 0,
    is_985: 0,
    schools: "",
    is_exclude_low_user: "1",
    page: page,
    size: 100,
  };

  const keywords = []
  if (originParams.keywords.length) keywords.push(...originParams.keywords)
  if (originParams.name) keywords.push(originParams.name)
  if (originParams.companies.length) keywords.push(...originParams.companies)

  searchParams.query = keywords.join(" ");
  searchParams.positions = originParams.positions.join(',');
  // searchParams.cities = getCityLocation(originParams.cities);
  // searchParams.provinces = getProvinceLocation(originParams.current_cities);
  // searchParams.professions = getIndustry(originParams.industries);
  // searchParams.allcompanies = originParams.companies.join(",");
  searchParams.is_211 = originParams.is211 ? 1 : 0;
  searchParams.is_985 = originParams.is985 ? 1 : 0;
  searchParams.schools = originParams.schools.join(",");
  searchParams.degrees = DICT_DEGREE[originParams.educationDegree];
  searchParams.page = page;
  return {
    search: searchParams,
  };
}
