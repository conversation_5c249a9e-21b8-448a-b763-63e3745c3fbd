
interface TSearchParams {
  search_id: string;
  last_rank: number;
  search_count: number;
  position_keywords: string[];
  work_age_from: number;
  work_age_to: number;
  cities: string[];
  companies: string[];
  education_degree: number;
  is_211: boolean;
  is_985: boolean;
  age_from: number;
  age_to: number;
  gender: number;
  regist_types: number[];
  name: string,
}

export default function buildParams(originParams: any, last_rank:number) {
  const searchParams: TSearchParams = {
    search_id: originParams.search_id,
    last_rank: last_rank,
    search_count: originParams.search_count,
    position_keywords: getKeyword(originParams.keyword),
    work_age_from: originParams.work_age_from,
    work_age_to: originParams.work_age_to,
    cities: originParams.cities,
    companies: originParams.companies,
    education_degree: getEducationId(originParams.education_degree),
    is_211: originParams.is_211,
    is_985: originParams.is_985,
    age_from: originParams.age_min,
    age_to: originParams.age_max,
    gender: getGender(originParams.gender),
    regist_types: [1, 2],
    name: originParams.name,
  }

  return searchParams
}

function getKeyword (keyword: any) {
  const keywords: string[] = []
  for (const key in keyword) {
    keywords.push(keyword[key])
  }
  return keywords
}

function getEducationId (educationId: number) {
  return 0
}

function getGender (gender: string) {
  return 0
}