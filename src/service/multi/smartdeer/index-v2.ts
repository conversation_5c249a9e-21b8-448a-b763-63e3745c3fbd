import { defineStore } from "pinia";
import buildParams from "./params";
import { SearchResult } from '@/service/multi'
import { SiteAdapter } from "../interface/site-adapter";
import { getItpQueryDto } from "../liepin/itp-params";
import { getMultiSearchTalentList } from "@/api/talent/talent";
import recruitSdk from "@/assets/js/RecruitSdk";
import { setSearchShowBury } from "@/api/track";

const useDataStore = defineStore('SmartDeerDataStore', {
  state: () => {
    return {
      total: 0,
      list: [] as any[],
      isLogin: false,
      loading: false,
      page: 0,
    }
  },
  actions: {
    setTotal(total: number) { this.total = total },
    setList(list: any[]) { this.list = list },
    setIsLogin(isLogin: boolean) { this.isLogin = isLogin },
    setLoading(loading: boolean) { this.loading = loading },
    setPage(page: number) { this.page = page }
  },
})

class SmartDeerService implements SiteAdapter {
  private store
  private lastSearchParams: any = {}
  private lastPageRank: number = 0
  private lastListCount: number = 0

  constructor() {
    this.store = useDataStore()
  }

  public get isLogin() { return this.store.isLogin }
  public get total() { return this.store.total }
  public get list() { return this.store.list }
  public get loading() { return this.store.loading }
  public get page() { return this.store.page }

  private getData(searchParams: any) {
    return recruitSdk.request({
      url: "https://global-recommend.smartdeer.work/talent/talent_search",
      method: "POST",
      data: JSON.stringify(searchParams),
      headers: {
        "Content-Type": "application/json",
        "app_key": "juoRYXf1q65cDgayvGpXP5fIaB8F7XlrvhyDYlz0"
      }
    })
  }

  async checkLogin(): Promise<boolean> {
    this.store.setIsLogin(true)
    return true
  }

  async fetch(searchParams: any, rank: number = 0): Promise<any> {
    try {
      // 如果没有登录，则直接返回
      if (!this.isLogin) return []
      this.store.setLoading(true)

      this.lastSearchParams = searchParams
      searchParams.last_rank = rank === 0 ? 0 : this.lastPageRank
      const res = await this.getData(searchParams)
      const rawSearchResult = res.data.data
      console.log(searchParams)
      // 注意这里的这个接口会同时返回ITP的人才搜索结果。需要从aggregate中取到猎聘自己的数据
      const aggregateListQueryDto = {
        smartdeerDataDto: rawSearchResult,
        queryDto: searchParams,
      }
      const aggregateListData = await getMultiSearchTalentList(aggregateListQueryDto)
      const { aggregate } = aggregateListData.data
      console.log(aggregate)
      const searchList = aggregate['6'] || []

      // total从原始搜索结果中得到，list从aggregate中得到
      this.store.setTotal(res.data.total)
      this.store.setList(searchList)
      this.store.setLoading(false)

      this.lastPageRank = aggregateListData.data.maxItpTalentRank
      this.lastListCount = aggregate['6']?.length || 0

      if (searchList && searchList.length > 0) {
        const buryItems = searchList.map((item: any, index: number) => {
          return {
            trackId: item.trackId,
            talentId: item.itpTalentId,
            rank: index,
          }
        })
        setSearchShowBury({ action: 3, site: 6, items: buryItems })
      }
      return rawSearchResult
    } catch (err: any) {
      console.log(err)
      this.store.setLoading(false)
      return []
    }
  }

  public nextPage() {
    this.store.setPage(this.store.page + 1)
    return this.fetch(this.lastSearchParams, this.lastPageRank)
  }

  public prevPage() {
    this.store.setPage(this.store.page - 1)
    return this.fetch(this.lastSearchParams, this.lastPageRank - this.lastListCount)
  }
}

export default SmartDeerService;