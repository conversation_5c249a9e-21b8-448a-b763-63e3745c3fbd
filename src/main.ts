import { createApp } from 'vue'
import { createPinia } from 'pinia'
import antDesignVue from 'ant-design-vue'
import App from '@/App.vue'
import router from '@/router'
import Permission from '@/plugins/permission'
import tracker from '@/utils/tracker'

import department from '@/components/itp-department/itp-department.vue'

// global css
import '@/global.less'

// 创建app实例
const app = createApp(App)

// 注册自定义的组件，用于递归组件调用
app.component('itp-department', department)

// 初始化pinia，官方推荐的全局状态管理，替代vuex
app.use(createPinia())

// 初始化ant design
app.use(antDesignVue)

// 初始化路由
app.use(router)

// 初始化权限工具
// app.use(Permission)

// mount到#app
app.mount('#app')

app.config.errorHandler = (err:any, isntance:any, info:string) => {
  tracker.exception('vue-error', {
    message: err.message,
    info: info
  })
}