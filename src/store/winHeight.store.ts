/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2023-02-17 11:11:01
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-02-17 11:25:52
 * @FilePath: /itp-operation-web/src/store/winHeight.store.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia'

export const useWinHeightStore = defineStore('winHeight.store', {
  state: () => ({
    value:  window.innerHeight
  }),

  actions: {
    dispatch(value: number) {
      this.value = value
    },
  }
})