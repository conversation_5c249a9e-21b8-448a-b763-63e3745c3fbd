import { defineStore } from "pinia";
import { getUserPermission, login, LoginParams } from '@/api/user'
import storage from "store";
import Cookies from "js-cookie";

const ACCESS_TOKEN = "Authorization";
const COMPANY_USER = "CompanyUserDto";
const COMPANY_INFO = "CompanyInfo";
const USER_PERMISSION = "UserPermission";
const USER = "UserDto";

const token = storage.get(ACCESS_TOKEN);
const companyUser = storage.get(COMPANY_USER) || {};
const companyInfo = storage.get(COMPANY_INFO) || {};
const userPermissions = storage.get(USER_PERMISSION) || [];
const userDto = storage.get(USER) || {};

export const useUserStore = defineStore("user.store", {
  state: () => ({
    token: token as string,
    name: companyUser.realName,
    avatar: companyUser.formalPhoto,
    permissions: userPermissions,
    companyId: companyInfo.id,
    companyName: companyInfo.companyName,
    id: companyUser.id,
    isAdmin: companyUser.isAdministrator === 1,
    userType: userDto.userType
  }),

  actions: {
    // 用户登录
    async login(params: LoginParams) {
      const loginResponse = await login(params);
      this.id = loginResponse.data.companyUser.id;
      this.token = loginResponse.data.token;
      this.name = loginResponse.data.companyUser.realName;
      this.avatar = loginResponse.data.companyUser.formalPhoto;
      this.companyId = loginResponse.data.company.id;
      this.companyName = loginResponse.data.company.companyName;
      this.isAdmin = loginResponse.data.companyUser.isAdministrator === 1;
      this.userType = loginResponse.data.user.userType;

      Cookies.set(ACCESS_TOKEN, loginResponse.data.token, {
        domain: "itp.smartdeer.work",
        expires: 30,
      });
      storage.set(ACCESS_TOKEN, loginResponse.data.token);
      storage.set(COMPANY_USER, loginResponse.data.companyUser);
      storage.set(COMPANY_INFO, loginResponse.data.company);
      storage.set(USER, loginResponse.data.user);

      const userPermissions = await getUserPermission(this.companyId);
      this.permissions = userPermissions.data;
      storage.set(USER_PERMISSION, userPermissions.data);

      return loginResponse;
    },

    // 用户登出
    async logout() {
      // 数据清理
      storage.remove(ACCESS_TOKEN);
      storage.remove(COMPANY_USER);
      storage.remove(COMPANY_INFO);
      storage.remove(USER_PERMISSION);
      storage.remove(USER);

      this.token = "";
      this.name = "";
      this.avatar = "";
      this.companyId = 0;
      this.permissions = [];

      location.href = "/account/login";
    },

    async getPermission() {
      const userPermissions = await getUserPermission(this.companyId);
      this.permissions = userPermissions.data;
      storage.set(USER_PERMISSION, userPermissions.data);
    }
  }
});
