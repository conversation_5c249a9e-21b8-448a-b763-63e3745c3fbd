/**
 * 内存缓存，用于在系统运行时提供数据暂存的功能
 */

import { defineStore } from 'pinia'

const store = new Map<string, any>()

export const useMemoStore = defineStore('memo.store', {
  actions: {
    set(key: string, value: any) {
      store.set(key, value)
    },

    get(key: string) {
      const value = store.get(key)
      return value
    },

    getOnce(key: string) {
      const value = store.get(key)
      store.delete(key)
      return value
    },

    clear(key: string) {
      store.delete(key)
    }
  },
})