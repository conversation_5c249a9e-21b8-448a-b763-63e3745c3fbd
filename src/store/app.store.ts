import { defineStore } from 'pinia'
import storage from 'store'

const APP_COLLASPED = 'appCollasped'

const appCollasped = storage.get(APP_COLLASPED) || false

export const useAppStore = defineStore('app.store', {
  state: () => ({
    collapsed: appCollasped,
  }),

  actions: {
    toggleMenuCollapse () {
      this.collapsed = !this.collapsed
      storage.set(APP_COLLASPED, this.collapsed)
    },
  }
})