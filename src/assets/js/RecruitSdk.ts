class RecruitSdk {

  /**
   * 
   */
  private _counter = new Date().getTime()

  /**
   * 请求的唯一标识，通过上面的计数器来自增生成。
   * @returns 
   */
  private unique() {
    this._counter++
    return 'unique_' + this._counter
  }

  /**
   * 
   * @param callName 
   * @param data 
   * @param timeout 
   * @returns 
   */
  private callToPlugin(callName: string, data: any, timeout = 1000 * 30) {
    let _timer: NodeJS.Timeout
    const nextCallName = this.unique() + ':' + callName

    return new Promise((resolve, reject) => {
      const callFunc = (event: any) => {
        const { callName, data } = event.detail

        if (callName === nextCallName) {
          clearTimeout(_timer)
          window.document.removeEventListener('browse_plugin_recruit_consume', callFunc)
          if (data.status === 'success') {
            resolve(data.result)
          } else {
            reject(data.result)
          }
        }
      }

      window.document.addEventListener('browse_plugin_recruit_consume', callFunc)

      const event = new CustomEvent('browse_plugin_recruit', {
        detail: { callName: nextCallName, data }
      })
      window.document.dispatchEvent(event)

      _timer = setTimeout(() => {
        window.document.removeEventListener('browse_plugin_recruit_consume', callFunc)
        reject('timeout')
      }, timeout)
    })
  }

  /**
   * 
   * @returns 
   */
  public checkInstallPlugin() {
    return this.callToPlugin('checkPluginActive', null, 1000)
  }

  /**
   * 
   * @param options 
   * @returns 
   */
  public request(options: any): Promise<any> {
    return this.callToPlugin('request', options)
  }

  /**
   * 
   * @param object 
   * @returns 
   */
  public setCache(object: any) {
    return this.callToPlugin('setLocalCache', object)
  }

  /**
   * 
   * @param object 
   * @returns 
   */
  public clearCache() {
    return this.callToPlugin('clearLocalCache', null)
  }

  /**
   * 
   * @param object 
   * @returns 
   */
  public setOpenWebQueue(object: any) {
    return this.callToPlugin('setOpenWebQueue', object)
  }
}

// 这里导出一个全局唯一的实例
const recruitSdk = new RecruitSdk()
export default recruitSdk