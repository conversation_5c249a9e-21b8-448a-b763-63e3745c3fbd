<template lang="pug">
.file-message
  .file-wrapper(@click="handleFileClick")
    .file-icon
      FileTextOutlined
    .file-name 
      span {{ message.fileInfo?.fileName }}
      .file-action
        span 点击下载
  
</template>

<script lang="ts" setup>
import { ref, toRef } from 'vue'
import { Image } from 'ant-design-vue'
import { FileTextOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  message: {}
}>()

const message:any = toRef(props, 'message')

function handleFileClick() {
  window.open(message.value.fileInfo.fileUrlDesc)
}
</script>

<style lang="sass" scoped>
.file-wrapper
  // background-color: #fff
  border-radius: 12px
  display: flex
  align-items: center
  cursor: pointer
  border: 1px solid RGBA(0,0,0, .2)
  padding: 8px 16px

  &:hover
    background-color: RGBA(0,0,0, .1)

  .file-icon
    flex: 0 0 32px
    font-size: 24px
    width: 32px
    height: 32px
    color: RGBA(0,0,0,.7)
    display: flex
    align-items: center
    justify-content: flex-start

  .file-name
    padding: 0 4px
    flex: 1 1 auto
    text-align: left
</style>