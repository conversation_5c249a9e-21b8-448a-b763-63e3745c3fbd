<template lang="pug">
.image-message
  Image(
    v-if="message.MsgContent.ImageInfoArray"
    :src="message.MsgContent.ImageInfoArray[0].URL"
    :preview="{src: message.MsgContent.ImageInfoArray[1].URL}"
  )
</template>

<script lang="ts" setup>
import { toRef } from 'vue'
import { Image } from 'ant-design-vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
      ImageInfoArray: []
    }
  }
}>()

const message = toRef(props, 'message')

</script>

<style lang="sass" scoped>
.image-message
  img
    width: 100%
</style>