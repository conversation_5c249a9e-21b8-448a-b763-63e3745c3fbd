<template lang="pug">
.file-message
  .file-wrapper(@click="handleFileClick")
    .file-icon
      FileTextOutlined
    .file-name 
      span {{ message.MsgContent?.FileName }}
      .file-action
        span 点击下载
  
</template>

<script lang="ts" setup>
import { toRef } from 'vue'
import { Image } from 'ant-design-vue'
import { FileTextOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
      FileName:string,
      Url:string
    }
  }
}>()

const message = toRef(props, 'message')
function handleFileClick() {
  window.open(message.value.MsgContent.Url)
}
</script>

<style lang="sass" scoped>
.file-wrapper
  // background-color: #fff
  border-radius: 12px
  display: flex
  align-items: center
  cursor: pointer
  border: 1px solid RGBA(0,0,0, .2)
  padding: 8px 16px

  &:hover
    background-color: RGBA(0,0,0, .1)

  .file-icon
    font-size: 24px
    width: 32px
    height: 32px
    color: RGBA(0,0,0,.7)
    display: flex
    align-items: center
    justify-content: flex-start

  .file-name
    text-align: left
</style>