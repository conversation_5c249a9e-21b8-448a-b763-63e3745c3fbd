<template lang="pug">
.custom-message-position-card
  .position-content(@click="handleShowPosition")
    .position-head
      .title {{ position.positionTitle }}
      .salary {{ formatSalary(position) }}
    .position-requirement
      .requirement-item {{ position.workTimeStr }}
      .requirement-item(v-if="position.isRemote") 可远程
      .requirement-item(v-if="position.locationStr") {{ position.locationStr }}
      .requirement-item {{ position.requiredEducationStr }}
      .requirement-item {{ position.requiredWorkYearStr }}

  .company-content
    .compnay-logo
      img(:src="position.organizationEntity.organizationLogoUrl")
  Drawer(v-model:open="status.showPositionDetail")
    //- PositionDetail(:positionId="position.id")
    .position-detail
      p 这里将会展示社招职位详情，后续实现。
      p 未来会增加从社招职位创建项目的能力。
  
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef } from 'vue'
import { Drawer } from 'ant-design-vue'

const status = reactive({
  showPositionDetail: false,
})

const props = defineProps<{position: any}>()
const position = toRef(props, 'position')

function formatSalary(params: any) {
  if (params.salaryFrom && params.salaryTo)
  return `${params.salaryFrom?.toLocaleString()} ~ ${params.salaryTo.toLocaleString()} ${params.salaryUnitStr} / ${params.salaryTimeUnitStr}`
}

function handleShowPosition() {
  status.showPositionDetail = true
}

onMounted(()=>{

})

</script>

<style lang="sass" scoped>
.custom-message-position-card
  padding: 12px
  border-radius: 3px
  text-align: left
  justify-content: space-between
  width: 100%
  min-width: 360px
  box-sizing: border-box
  position: relative

  &:hover
    background-color: RGBA(0,0,0,.05)
    cursor: pointer

  .position-content
    flex: 1 1 auto
    
    .position-head
      padding-right: 52px
      margin-bottom: 8px
      .title
        font-weight: bold
        font-size: 18px
      .salary
        color: #ff9111

    .position-requirement
      display: flex
      margin: -4px
      flex-wrap: wrap
      .requirement-item
        margin: 4px
        border-radius: 6px
        background-color: RGBA(0,0,0,.05)
        font-size: 12px
        padding: 4px 12px

  .company-content
    position: absolute
    top: 12px
    right: 12px
    .compnay-logo
      img
        width: 40px
        border-radius: 6px

.position-detail
  padding: 24px

</style>