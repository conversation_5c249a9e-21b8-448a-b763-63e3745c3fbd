<template lang="pug">
.video-message
  //- span {{ message }}
  video(
    controls
    :src="message.MsgContent.VideoUrl"
  )
</template>

<script lang="ts" setup>
import { toRef } from 'vue'
import { Image } from 'ant-design-vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
    }
  }
}>()

const message = toRef(props, 'message')

</script>

<style lang="sass" scoped>
.video-message
  video
    width: 100%
</style>