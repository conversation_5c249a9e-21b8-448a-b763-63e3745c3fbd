<template lang="pug">
.text-message
  pre(v-html="html.join('')")
</template>

<script lang="ts" setup>
import { toRef } from 'vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
      Text: string
    }
  }
}>()

const message = toRef(props, 'message')
const delimiter = /((?:https?:\/\/)?(?:(?:[a-z0-9]?(?:[a-z0-9\-]{1,61}[a-z0-9])?\.[^\.|\s])+[a-z\.]*[a-z]+|(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3})(?::\d{1,5})*[a-z0-9.,_\/~#&=;%+?\-\\(\\)]*)/gi

const html = message.value.MsgContent.Text.split(delimiter).map(word => {
  let match = word.match(delimiter)
  if (match) {
    let url = match[0]
    return `<a href=${url.startsWith("http") ? url : `https://${url}`} key=${url} target="_blank">${url}</a>`
  }
  return word
})
</script>

<style lang="sass" scoped>
.text-message
  padding: 12px
  pre
    margin: 0
    text-align: left
</style>