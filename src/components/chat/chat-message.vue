<template lang="pug">
.chat-message
  template(v-for="(message, index) in message.MsgBody")

    template(v-if="message.MsgType === 'TIMTextElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        TextMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMImageElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        ImageMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMFileElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        FileMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMVideoFileElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        VideoMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMSoundElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        AudioMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMVideoFileElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        VideoMessage(:message="message")

    template(v-else-if="message.MsgType === 'TIMCustomElem'")
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        CustomMessage(:message="message")

    template(v-else)
      MessageBubble(:type="type" :profile="profile" :time="messageTime")
        span 暂未实现{{message.MsgType}}消息类型，请联系开发者实现

</template>

<script lang="ts" setup>
import { computed, onMounted, toRef } from "vue"
import TextMessage from "@/components/chat/message-text.vue"
import ImageMessage from "@/components/chat/message-image.vue"
import VideoMessage from "@/components/chat/message-video.vue"
import AudioMessage from "@/components/chat/message-audio.vue"
import FileMessage from "@/components/chat/message-file.vue"
import MessageBubble from "@/components/chat/message-bubble.vue"
import CustomMessage from "@/components/chat/message-custom.vue"
import dayjs from "dayjs"

const props = defineProps<{
  type: "from" | "to";
  message: any;
  profile: any;
}>();

const profile = toRef(props, "profile");
const type = toRef(props, "type");
const message = toRef(props, "message");
const messageTime = dayjs(message.value.MsgTime * 1000).format('YYYY-MM-DD HH:mm')

onMounted(()=>{
})

</script>

<style lang="sass" scoped>
.message-container
  position: relative

  .message-avatar
    position: absolute
    img
      width: 32px
      height: 32px
      border-radius: 50%

  .message-body
    .message-info-summary
      display: flex

  &.from
    padding-right: 42px

    .message-avatar
      right: 0

    .message-body
      .message-info-summary
        justify-content: flex-start
        flex-direction: row-reverse

  &.to
    padding-left: 42px

    .message-avatar
      left: 0
</style>
