<template lang="pug">
mixin talent-summary
  .talent-summary

mixin message-list
  .message-list
    MessageList(:from="from" :to="to" @newMessage="handleNewMessage")

mixin chat-operation
  .chat-operation
    .chat-input
      Textarea(:rows="3" placeholder="请输入消息内容" v-model:value="form.message"  @keyup.ctrl.enter="hendleSendTextMessage")
    .chat-actions
      .chat-action-extra
        Space()
          Button(@click="()=>{status.showPositionSelect = true}")
            ContactsOutlined
            span 职位
      .chat-action-button
        But<PERSON>(type="primary" @click="hendleSendTextMessage") 发送 Ctrl+Enter

.talent-chat-component(ref="chatContainer")
  +talent-summary
  +message-list
  +chat-operation

  //- div(v-if="status.showPositionSelect")
  //-   JobSelect(
  //-     @close="() => status.showPositionSelect = false"
  //-     @success="handlePositionSelectConfirm"
  //-   )

  //- TODO 这里需要使用ITP的职位选择器。
  JobSelector(v-model:visible="status.showPositionSelect" :selected="[]" @select="handelPositionSelect" :multi="false")
  
</template>

<script lang="ts" setup>
import { Space, Button, Textarea } from 'ant-design-vue'
import MessageList from '@/components/chat/message-list.vue'
import { ContactsOutlined } from '@ant-design/icons-vue'
import JobSelector from '@/components/app/job-selector.vue'
import { nextTick, onMounted, reactive, ref, toRef } from 'vue'
import API from '@/api/chat'
import { getPositionCode, getPositionDetail } from '@/api/position'
import {salaryTimeUnit,currencyUnit } from '@/views/h5/dict'

const chatContainer = ref()

interface ChatUserProfile {
  avatar: string,
  nick: string,
  imUserId: string,
}

interface TalentChatProps {
  from: ChatUserProfile,
  to: ChatUserProfile,
}

const status = reactive({
  showPositionSelect: false,
})

const props = defineProps<TalentChatProps>()
const from = toRef(props, 'from')
const to = toRef(props, 'to')

const form = reactive({
  message: ''
})

async function sendTextMessage(message: string) {
  form.message = ''
  const res = await API.sendMessage({ 
    sendType: 1, 
    text: message, 
    toImUserId: to.value.imUserId, 
    fromImUser: from.value.imUserId 
  })
}

async function handelPositionSelect(positions: any[]) {
  status.showPositionSelect = false
  const position = positions[0]
  sendPosition(position)
}

const h5PageBaseUrl = import.meta.env.VITE_VUE_APP_BUILD_ENV == 'production' ? 'https://web.itp.smartdeer.work' : 'https://test.itp.smartdeer.work'

async function sendPosition(job: any) {

  const positionId = job.positionId
  const positionRes = await getPositionDetail(positionId)
  const position = positionRes.data

  const codeRes = await getPositionCode(position.id)
  const positionCode = codeRes.data
  const itpPositionLink = `${h5PageBaseUrl}/h5/position/${positionCode}`

  const positionMessageTemplate = `${position.positionTitle}
${salaryRange(position)} ${salaryUnit(position)}
${position.areaStr}${position.requireDegreeStr == '未知' ? '' : ` ${position.requireDegreeStr}`} ${position.requireWorkYearsStr == '未知' ? '' : position.requireWorkYearsStr}
${itpPositionLink}`
  await API.sendMessage({ sendType: 1, text: positionMessageTemplate, toImUserId: to.value.imUserId, fromImUser: from.value.imUserId})
}

function salaryRange(position:any) {
  if (position.salaryFrom <= 0) {
    return '薪资面议'
  } else {
    return position.salaryFrom?.toLocaleString() + '-' + position.salaryTo?.toLocaleString()
  }
}

function salaryUnit(position:any) {
  if (position.salaryFrom <= 0) {
    return ''
  } else {
    return `${currencyUnit.get(position.salaryUnit)}/${salaryTimeUnit.get(position.salaryTimeUnit)}`
  }
}

async function handleNewMessage() {
  nextTick(() => {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  })
}

async function setConversationRead() {
  API.clearUnReadCount({
    from: from.value.imUserId,
    to: to.value.imUserId
  })
}

function hendleSendTextMessage() {
  if (!form.message) return
  sendTextMessage(form.message)
  setConversationRead()
}

onMounted(() => {
})

</script>

<style lang="sass" scoped>
.talent-chat-component
  height: 100%
  position: relative
  overflow-y: scroll
  &::-webkit-scrollbar
    display: none

  .talent-summary
    position: sticky
    top: 0
    z-index: 1
    padding: 0 12px

  .message-list
    padding: 0 12px
    min-height: calc(100% - 144px)

  .chat-operation
    background: #fff
    position: sticky
    bottom: 0
    padding: 12px 12px

    .chat-input
      margin-bottom: 12px

    .chat-actions
      display: flex
      align-items: center
      justify-content: space-between
</style>