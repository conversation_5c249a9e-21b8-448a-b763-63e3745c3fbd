<template lang="pug">
.talent-chat-message-list
  //- .message-list-head
  //-   .talent-info
  //-     .talent-avatar
  //-       a-avatar(:src="`${talent.profileBasic?.formalPhotoUrl}?imageMogr2/crop/96x96/gravity/center`" :size="48")
  //-     .talent-info-container
  //-       .talent-name 
  //-         .name {{ talent.profileBasic.realName }}
  //-         a-tag {{ talent.profileBasic.gender == 1 ? '男' : '女' }}
  //-       .talent-info {{ getTalentBasicInfo(talent) }}
  //-       //- .talent-id
  //-       //-   span ID： {{ talent.imAccount.accountId }}

  //-   .talent-action
  //-     But<PERSON>(@click="handleShowTalentDetailClick") 人才详情
  .message-list-history

    Button(type="text" block @click="getMore" v-if="hasMore") 查看更多
    .messge(v-for="(item, index) in messageList")
      ChatMessage(
        :message="item" 
        :type="from.imUserId === item.From_Account ? 'from' : 'to'"
        :profile="imUserProfile.get(item.From_Account)"
      )

  //- Drawer(
  //-   v-model:open="status.showTalentDetail",
  //-   title="人才详情",
  //-   :destroyOnClose="true",
  //-   :width="520"
  //- )
  //-   TalentDetail(:imUserId="selectedAccountImUserId" ref="talentDetail")

</template>

<script lang="ts" setup>
import { computed, nextTick, onDeactivated, onMounted, onUnmounted, reactive, ref, toRef, watch } from 'vue'
import { Button, Drawer } from 'ant-design-vue'
import ChatMessage from "@/components/chat/chat-message.vue"
// import TalentDetail from '@/components/talent-detail.vue'
import dayjs from 'dayjs'
import API from '@/api/chat'

const emit = defineEmits(['newMessage'])

const props = defineProps<{ 
  to: any
  from: any
}>()
const to = toRef(props, 'to')
const from = toRef(props, 'from')

const messageList = ref<any[]>([])
const imUserProfile = new Map()

let getMessageTimer = null as null | NodeJS.Timeout

const pagination = reactive<any>({
  current: 1,
  pageSize: 20,
  total: 0,
})

const status = reactive({
  showTalentDetail: false,
})

const selectedAccountImUserId = ref('')

function handleShowTalentDetailClick() {
  selectedAccountImUserId.value = to.value.imUserId || ''
  status.showTalentDetail = true
}

async function init() {
  messageSet.clear()
  messageList.value = []
  pagination.current = 1
  setUserProfile()

  const toImId = to.value.imUserId || ''
  await getHistoryMessages(from.value.imUserId, to.value.imUserId, pagination)
  clearInterval(getMessageTimer!)
  getMessageTimer = setInterval(() => {
    getNewMessages(from.value.imUserId, toImId)
  }, 1500)

  nextTick(() => {
    emit('newMessage')
  })
}

const messageSet = new Set()

async function getHistoryMessages(fromImId: string, toImId: string, pagination: any) {
  try {
    const res = await API.getChatHistoryList(fromImId, toImId, pagination.current, pagination.pageSize)
    pagination.total = res.data.total
    const historyMessage = res.data.dataInfo.filter((item: any) => {
      // 第=一步先过滤掉不用显示的消息,和重复的消息
      // 可能会出现时序问题，因此过滤掉不是当前用户的消息
      if(item.uniqueKey !== `${to.value.imUserId}|${from.value.imUserId}`) return false
      if (item.MsgKey && !messageSet.has(item.MsgKey)) {
        messageSet.add(item.MsgKey)
        return true
      } else return false
    }).reverse()

    messageList.value.unshift(...historyMessage)
  } catch (err: any) {

  }
}

async function getNewMessages(fromImId: string, toImId: string) {
  try {
    const res = await API.getChatHistoryList(fromImId, toImId, 1, 10)
    const newMessage = res.data.dataInfo.filter((item: any) => {
      // 第一步先过滤掉不用显示的消息,和重复的消息
      // 可能会出现时序问题，因此过滤掉不是当前用户的消息
      if(item.uniqueKey !== `${to.value.imUserId}|${from.value.imUserId}`) return false
      if (item.MsgKey && !messageSet.has(item.MsgKey)) {
        messageSet.add(item.MsgKey)
        return true
      } else return false
    }).reverse()
    messageList.value.push(...newMessage)

    if (newMessage.length > 0) {
      nextTick(() => {
        emit('newMessage', newMessage)
      })
    }
  } catch (err: any) {
  }
}

async function getMore() {
  pagination.current = pagination.current + 1
  const toImId = to.value.imUserId || ''
  await getHistoryMessages(from.value.imUserId, to.value.imUserId, pagination)
}

const hasMore = computed(() => {
  return pagination.total! > pagination.pageSize * pagination.current
})

async function setUserProfile() {
  imUserProfile.set(from.value.imUserId, from.value)
  imUserProfile.set(to.value.imUserId, to.value)
}

watch(() => from.value, (value) => {
  if (value) init()
})

watch(() => to.value, (value) => {
  if (value) init()
})

onMounted(() => {
  if (from.value && to.value) init()
})

onUnmounted(() => {
  clearInterval(getMessageTimer!)
})

onDeactivated(() => {
  console.log('message deactive')
})

</script>

<style lang="sass" scoped>
.talent-info
  display: flex
  align-items: center

  .talent-info-container
    white-space: nowrap
    .talent-name
      display: flex
      margin-bottom: 4px
      .name
        font-weight: bold
        margin-right: 8px

  .talent-avatar
    flex: 0 0 auto
    margin-right: 8px
    width: 48px
    height: 48px
    border-radius: 50%
    overflow: hidden

.message-list-head
  padding: 12px
  display: flex
  justify-content: space-between
  align-items: center
  position: sticky
  background-color: #F0F2F5
  top: 0
  z-index: 2

.message-list-history
  padding: 12px
  height: 100%
</style>  