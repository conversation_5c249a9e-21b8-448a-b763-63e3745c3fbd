<template lang="pug">
.custom-formated-card
  template(v-for="(item, index) in textList")
    div.text-item() {{ item.text }}

  
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef } from 'vue'
import { Drawer } from 'ant-design-vue'

const status = reactive({
  showPositionDetail: false,
})

const props = defineProps<{message: any}>()
const message = toRef(props, 'message')

const textList = ref<any[]>([])

function findText(message:any) {
  if (message.class === 'Text' && message.content) {
    textList.value.push({text: message.content, style: message.style})
  }

  if(Array.isArray(message.children)) {
    message.children.forEach((item: any) => {
      findText(item)
    })
  }

  if (message.child) {
    findText(message.child)
  }
}

onMounted(()=>{
  findText(message.value.customContent)
})

</script>

<style lang="sass" scoped>

.custom-formated-card
  text-align: left
  padding: 20px

.text-item
  margin: 4px 0
</style>