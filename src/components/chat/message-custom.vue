<template lang="pug">
.custom-message
  template(v-if="message.MsgType === 'TIMCustomElem'")
    template(v-if="customMessage.msgCardType === 1")
      span 暂未实现的消息类型[{{ customMessage.msgCardType }}]
      
    template(v-else-if="customMessage.msgCardType === 3")
      CustomPositionMessage(:position="customMessage")

    template(v-else-if="customMessage.msgCardType === 6")
      MessageResume(:message="customMessage")

    template(v-else-if="customMessage.msgCardType === 5")
      MessageResume(:message="customMessage")
  
    template(v-else-if="customMessage.msgCardType === 7")
      span {{ customMessage.text }}

    template(v-else-if="customMessage.msgCardType === 10")
      FormatedMessage(:message="customMessage")

</template>

<script lang="ts" setup>
import { onMounted, ref, toRef } from 'vue'
import { Image } from 'ant-design-vue'
import CustomPositionMessage from '@/components/chat/custom-position-card.vue'
import MessageResume from '@/components/chat/custom-resume.vue'
import FormatedMessage from '@/components/chat/custom-formated-card.vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
      Data: string
      Desc: string
    }
  }
}>()

const message = toRef(props, 'message')
const customMessage = ref(JSON.parse(message.value.MsgContent.Data))
</script>

<style lang="sass" scoped>
.custom-message
  text-align: center
</style>