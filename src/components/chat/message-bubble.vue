<template lang="pug">
.message-container(:class="{from: type === 'from', to: type === 'to'}")
  .message-avatar
    Avatar(:src="profile?.avatar" :size="36") {{ profile?.nick }}
  .message-body
    .message-info-summary
      span {{ profile?.nick }}
      span &nbsp;
      span {{ time }}
    .message-bubble
      slot
</template>

<script lang="ts" setup>
import { computed, toRef } from "vue"
import dayjs from "dayjs"
import { Avatar } from "ant-design-vue"

interface BubbleProfile {
  nick: string,
  avatar: string,
}

const props = defineProps<{
  type: "from" | "to"
  profile: BubbleProfile
  time: string
}>();

const profile = toRef(props, "profile");
const type = toRef(props, "type");
const time = toRef(props, "time");
</script>

<style lang="sass" scoped>
.message-container
  position: relative
  margin: 16px 0

  .message-avatar
    position: absolute
    img
      width: 36px
      height: 36px
      border-radius: 50%

  .message-body
    .message-info-summary
      display: flex
      color: #bbb

    .message-bubble
      display: inline-block
      border-radius: 10px
      font-weight: 400
      font-size: 14px
      color: #000000
      letter-spacing: 0
      word-wrap: break-word
      word-break: break-all

  &.from
    padding-right: 44px

    .message-avatar
      right: 0

    .message-body
      text-align: right
      .message-info-summary
        justify-content: flex-start
        flex-direction: row-reverse

      .message-bubble
        background-color: #dceafd

  &.to
    padding-left: 44px

    .message-avatar
      left: 0
    .message-body
      .message-bubble
        background-color: #fbfbfb

</style>