<template lang="pug">
.audio-message
  //- span {{ message }}
  template(v-if="message.MsgContent.Url")
    audio(controls)
      source(:src="message.MsgContent.Url" )
</template>

<script lang="ts" setup>
import { toRef } from 'vue'
import { Image } from 'ant-design-vue'

const props = defineProps<{
  message: {
    MsgType: string
    MsgContent: {
    }
  }
}>()

const message = toRef(props, 'message')

</script>

<style lang="sass" scoped>
.audio-message
  audio
    width: 320px
    display: block
</style>