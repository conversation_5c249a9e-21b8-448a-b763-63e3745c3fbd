<template lang="pug">
.lp-info-section(:class="{'editable': editable, 'x40': title, 'x24': !title}")
  h3(v-if="title") {{title}}
  slot

  .lp-info-action
    a-space(:size="16")
      a-button(type="primary" shape="circle" ghost @click="$emit('edit')")
        EditOutlined
  .lp-info-spliter
</template>

<script lang="ts" setup>

import { toRefs } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'

const props = defineProps({ title: String, editable: Boolean })
const emit = defineEmits(['edit'])

const { title, editable } = toRefs(props)

</script>

<style lang="scss" scoped>
.lp-info-section {
  border: 1px solid transparent;
  position: relative;

  &.x40 {
    padding: 18px 24px;
    padding-bottom: 0;

    .lp-info-spliter {
      border-color: #f0f0f0;
    }
  }

  &.x24 {
    padding: 18px 24px;
    padding-bottom: 0;

    .lp-info-spliter {
      border-color: transparent;
    }
  }

  h3 {
    font-size: 18px;
    position: relative;
    margin-block-start: 0;
    margin-block-end: 0;
    line-height: 24px;
    margin-bottom: 24px;
    padding-left: 12px;

    &::before {
      content: "";
      display: block;
      width: 4px;
      height: 18px;
      left: 0;
      top: 3px;
      position: absolute;
      border-radius: 2px;
      background-color: #FF9111;
    }
  }

  .lp-info-action {
    display: none;
    position: absolute;
    right: 24px;
    top: 20px;
  }

  &.editable {
    transition: all .2s;

    .lp-info-action {
     display: block;
    }
  }

  .lp-info-spliter {
    margin-top: 18px;
    border-top-width: 1px;
    border-top-style: solid;
    transition: all .2s;

    position: relative;
    top: 1px;
    z-index: 0;
  }
}
</style>