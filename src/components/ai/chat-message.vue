<template lang="pug">
.message-item
  MessageBubble(
    v-if="message.type === 'text'",
    :content="message.content",
    :conf="conf",
    typing="{step: 2, interval: 50}"
  )
  MessageThoughtChain(
    v-if="message.type === 'thoughtChain'",
    :thoughtChainItems="message.thoughtChainItems"
  )
</template>

<script lang="ts" setup>
import MessageBubble from './message-bubble.vue'
import MessageThoughtChain from './message-thought-chain.vue'

import { ref, defineProps, reactive } from 'vue'

const props = defineProps<{ message: any }>()
const message = ref(props.message)
const conf = reactive({
  placement: message.value.placement,
  avatarStyle: message.value.avatarStyle
})

</script>

<style lang="scss" scoped>
.message-item {
  margin-bottom: 10px;
}
</style>