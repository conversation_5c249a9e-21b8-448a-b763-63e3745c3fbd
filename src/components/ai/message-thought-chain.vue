<template lang="pug">
ThoughtChain(
  :items="thoughtChainItems"
)
</template>

<script lang="ts" setup>
import { ref, defineProps, onMounted } from 'vue'
import {
  ThoughtChain,
  type ThoughtChainItem
} from 'ant-design-x-vue'

const props = defineProps({ thoughtChainItems: Array })
const thoughtChainItems = ref(props.thoughtChainItems)

onMounted(async () => {

})

</script>

<style lang="scss" scoped>
</style>