<template lang="pug">
Sender(
  :loading="loading"
  :value="inputValue"
  @submit="sendMessage"
  @change="(msg) => {inputValue.value = msg}"
)
</template>

<script lang="ts" setup>
import {
  Sender
} from "ant-design-x-vue";
import { ref } from 'vue';

const emit = defineEmits(['onSend'])

const inputValue = ref('')
const loading = ref<boolean>(false)

const sendMessage = (message: string) => {
  inputValue.value = ''
  loading.value = true
  emit('onSend', message)
  console.log(message)
}
</script>

<style lang="scss" scoped>
</style>