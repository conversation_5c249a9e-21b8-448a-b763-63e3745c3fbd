<template lang="pug">
Bubble(
  :content="content",
  :placement="conf.placement"
  avatar="#avatar"
)
  template(#avatar)
    UserOutlined(:style="conf.avatarStyle" class="message-avatar")
</template>

<script lang="ts" setup>
import { ref, defineProps, onMounted } from 'vue'
import {
  Bubble
} from 'ant-design-x-vue'
import { UserOutlined } from '@ant-design/icons-vue'

const props = defineProps<{ content: string, conf: any }>()
const content = ref(props.content)
const conf = ref(props.conf)
console.log(conf.value)

onMounted(() => {

})

</script>

<style lang="scss" scoped>
.message-avatar {
  width: 30px;
  height: 30px;
  font-size: 30px;
}
</style>