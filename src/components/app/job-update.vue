<template lang=pug>
.job-update-component
  .job-update-form
    a-spin(:spinning="status.loading")
      a-form(labelAlign="left" ref="formInstance" :model="form" layout="vertical")
        a-form-item(
          name="status"
          label="项目状态"
        )
          a-select(
            placeholder="项目状态",
            v-model:value="form.status",
            :options="dict.jobStatus",
          )

        a-form-item(
          name="priority"
          label="优先级"
        )
          a-select(v-model:value="form.priority")
            a-select-option(:value="10") 最高优先级(P0)
            a-select-option(:value="5") 常规(P1)
            a-select-option(:value="1") 低优先级(P2)

        a-form-item(
          label="BD"
          name="bd"
          :rules="[{ type: 'array', required: true, message: '请至少指定一名BD', trigger: ['change'] }]"
        )
          a-select(
            placeholder="请指定BD",
            v-model:value="form.bd",
            mode="multiple",
            :options="dict.BDUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="PM"
          name="pm"
          :rules="[{ type: 'array', required: true, message: '请至少指定一名PM', trigger: ['change'] }]" 
        )
          a-select(
            placeholder="请指定PM",
            v-model:value="form.pm",
            mode="multiple",
            :options="dict.PMUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="CA"
          name="ca"
          :rules="[{type: 'array', required: true, message: '请至少指定一名CA', trigger: ['change'] }]"
        )
          a-select(
            placeholder="请指定CA",
            v-model:value="form.ca",
            mode="multiple",
            :options="dict.CAUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="佣金支付类型"
          name="commissionType"
          :rules="[{ required: true, message: '请选择佣金支付类型' }]"
        )
          a-select(
            placeholder="请选择佣金支付类型",
            v-model:value="form.commissionType"
          )
            a-select-option(:value="1") 一次性支付
            a-select-option(:value="2") 按月支付

        a-form-item(
          label="预估佣金金额"
          name="commissionEstimatedAmount"
          :rules="[{ required: true, message: '请输入预估佣金金额' }]"
        )
          a-input-number(
            v-model:value="form.commissionEstimatedAmount"
            placeholder="请输入预估佣金金额"
            :min="0"
            :precision="2"
            style="width: 100%"
            :controls="false"
          )

        a-form-item(
          label="币种"
          name="commissionEstimatedCurrency"
          :rules="[{ required: true, message: '请选择币种' }]"
        )
          a-select(
            placeholder="请选择币种",
            v-model:value="form.commissionEstimatedCurrency"
          )
            a-select-option(v-for="item in dict.currencyList" :value="item.value") {{ item.label }}

        a-form-item(
          label="是否可发布到三方平台"
          name="canPublishToPlatform"
          :rules="[{ required: true, type:'boolean', message: '请选择是否可发布到三方平台' }]"
        )
          a-radio-group(
            v-model:value="form.canPublishToPlatform",
            :options="[{label: '是', value: true},{label: '否', value: false}]",
          )
          
        a-form-item(
          label="第三方平台职位名称"
          v-if="form.canPublishToPlatform"
        )
          .platform-position-names-tip 请将该职位在第三方平台发布的职位名称填入，以便当有简历投递时自动匹配到当前职位。
          a-row(:gutter="16")
            a-col(:span="12")
              a-form-item(
                label="猎聘职位名称"
                name="liepin"
                :wrapperCol="{ span: 24 }"
              )
                a-input(
                  placeholder="猎聘职位名称"
                  v-model:value="form.platformPositionNames.liepin"
                )
            a-col(:span="12")
              a-form-item(
                label="脉脉职位名称"
                name="maimai"
                :wrapperCol="{ span: 24 }"
              )
                a-input(
                  placeholder="脉脉职位名称"
                  v-model:value="form.platformPositionNames.maimai"
                )
          a-row(:gutter="16")
            a-col(:span="12")
              a-form-item(
                label="Boss直聘职位名称"
                name="boss"
                :wrapperCol="{ span: 24 }"
              )
                a-input(
                  placeholder="Boss直聘职位名称"
                  v-model:value="form.platformPositionNames.boss"
                )
            a-col(:span="12")
              a-form-item(
                label="LinkedIn职位名称"
                name="linkedin"
                :wrapperCol="{ span: 24 }"
              )
                a-input(
                  placeholder="LinkedIn职位名称"
                  v-model:value="form.platformPositionNames.linkedin"
                )

  .job-update-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="updateJob" type="primary") 保存
</template>


<script lang=ts setup>
import { getJobStatus } from '@/api/dictionary'
import { getJobRequirementDetail } from '@/api/position'
import { message } from 'ant-design-vue'
import { onMounted, reactive, toRef, ref } from 'vue'
import { getCompanyOnboardUsers } from '@/api/system/roles'
import { updateJobRequirement } from '@/api/job'
import { getCurrencyList } from '@/api/dictionary'

interface JobDetail {
  bd: number[],
  pm: number[],
  ca: number[],
}

const dict = reactive({
  BDUsers: [] as any[],
  PMUsers: [] as any[],
  CAUsers: [] as any[],
  jobStatus: [],
  currencyList: [] as any[]
})

const status = reactive({
  initDict: false,
  loading: false,
})

const props = defineProps({ jobRequirementId: Number })
const jobRequirementId = toRef(props, 'jobRequirementId')
const emit = defineEmits(['close', 'update'])

const form = reactive({
  status: 0,
  priority: 5,
  bd: [] as number[],
  pm: [] as number[],
  ca: [] as number[],
  commissionType: 1,
  commissionEstimatedAmount: 0 as number | string,
  commissionEstimatedCurrency: '',
  canPublishToPlatform: true,
  platformPositionNames: {
    liepin: '',
    maimai: '',
    boss: '',
    linkedin: ''
  }
})

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

async function initDict() {
  status.initDict = true
  try {
    const dictPromises = [
      getJobStatus(),
      getCompanyOnboardUsers('ca'),
      getCompanyOnboardUsers('bd'),
      getCompanyOnboardUsers('pm'),
      getCurrencyList()
    ]
    const [
      dictJobStatus,
      dictCAUsers,
      dictBDUsers,
      dictPMUsers,
      dictCurrencyList
    ] = await Promise.all(dictPromises)

    dict.jobStatus = dictJobStatus.data
    dict.BDUsers = dictBDUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.CAUsers = dictCAUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.PMUsers = dictPMUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.currencyList = dictCurrencyList.data.map((item: any) => { return { value: item.id, label: item.name } })
  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

async function fetchJobDetail(jobRequirementId: number | undefined) {
  if (!jobRequirementId) return
  status.loading = true
  try {
    const res = await getJobRequirementDetail(jobRequirementId)
    form.status = res.data.status
    form.priority = res.data.priority || 5
    form.canPublishToPlatform = res.data.canPublishToPlatform == 0 ? false : true
    form.commissionType = res.data.commissionType || 1
    form.commissionEstimatedAmount = res.data.commissionEstimatedAmount || 0
    form.commissionEstimatedCurrency = res.data.commissionEstimatedCurrency === 0 ? 0 : (res.data.commissionEstimatedCurrency || '')
    
    console.log('jobRequirementDetail', res.data)
    
    // 初始化第三方平台职位名称
    if (res.data.platformPositionNames) {
      try {
        const platformPositions = typeof res.data.platformPositionNames === 'string' 
          ? JSON.parse(res.data.platformPositionNames) 
          : res.data.platformPositionNames;
          
        if (platformPositions) {
          // 设置对应平台的职位名称
          form.platformPositionNames.liepin = platformPositions['2'] || ''
          form.platformPositionNames.maimai = platformPositions['3'] || ''
          form.platformPositionNames.linkedin = platformPositions['4'] || ''
          form.platformPositionNames.boss = platformPositions['5'] || ''
        }
      } catch (e) {
        console.error('Parse platformPositionNames error:', e)
      }
    }
    
    res.data.properties.forEach((item: any) => {
      if (['bd', 'pm', 'ca'].includes(item.key)) {
        if (item.key === 'bd') {
          if (!dict.BDUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.BDUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.bd.push(Number(item.value))
        }
        if (item.key === 'pm') {
          if (!dict.PMUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.PMUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.pm.push(Number(item.value))
        }
        if (item.key === 'ca') {
          if (!dict.CAUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.CAUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.ca.push(Number(item.value))
        }
      }
    })
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function updateJob() {
  status.loading = true
  try {
    const invertedIndex = [] as any[]

    Object.entries({
      bd: form.bd,
      pm: form.pm,
      ca: form.ca,
    }).forEach((item, index) => {
      const [key, value] = item
      if (Array.isArray(value)) {
        value.forEach(itemValue => {
          invertedIndex.push({ key, value: itemValue })
        })
      } else invertedIndex.push({ key, value })
    })

    // 构建平台职位名称对象
    const platformPositionNames = {} as any
    if (form.platformPositionNames.liepin) platformPositionNames['2'] = form.platformPositionNames.liepin
    if (form.platformPositionNames.maimai) platformPositionNames['3'] = form.platformPositionNames.maimai
    if (form.platformPositionNames.linkedin) platformPositionNames['4'] = form.platformPositionNames.linkedin
    if (form.platformPositionNames.boss) platformPositionNames['5'] = form.platformPositionNames.boss

    // 确保佣金金额是数字格式
    let commissionAmount: number
    if (typeof form.commissionEstimatedAmount === 'string') {
      // 移除逗号并转换为数字
      commissionAmount = parseFloat(form.commissionEstimatedAmount.replace(/,/g, '')) || 0
    } else {
      commissionAmount = form.commissionEstimatedAmount || 0
    }

    const res = await updateJobRequirement(props.jobRequirementId!, {
      status: form.status,
      priority: form.priority,
      canPublishToPlatform: form.canPublishToPlatform ? 1 : 0,
      commissionType: form.commissionType,
      commissionEstimatedAmount: commissionAmount,
      commissionEstimatedCurrency: form.commissionEstimatedCurrency,
      invertedIndex,
      platformPositionNames: Object.keys(platformPositionNames).length > 0 ? platformPositionNames : undefined
    })

    emit('update')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

onMounted(async () => {
  await initDict()
  fetchJobDetail(props.jobRequirementId)
})

</script>

<style lang="scss" scoped>
.job-update-component {
  padding-bottom: 56px;

  .job-update-form {
    padding: 12px 24px;
  }

  .job-update-action {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px 24px;
    text-align: right;
    box-sizing: border-box;
  }
  
  .platform-position-names-tip {
    font-size: 12px;
    color: #999;
    margin-bottom: 12px;
  }
}
</style>