<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useRoute } from 'vue-router'
import FinanceContractForm from '@/components/app/finance-contract-form.vue'
import { getCustomerContractList } from '@/api/finance'
import { message } from 'ant-design-vue'
import { toRef } from 'vue';
import { onMounted } from 'vue';
import { watch } from 'vue';

const props = defineProps<{ customerId: number }>()
const customerId = toRef(props, 'customerId')

const status = reactive({
  loading: false,
  visible: false
})

const contractId = ref<number>()
const columnsList = [
  {
    title: "合同状态",
    dataIndex: "contractStatusStr",
    key: "contractStatusStr",
  },
  {
    title: "合同类型",
    dataIndex: "contractTypeStr",
    key: "contractTypeStr",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action ",
    width: "180px"
  },
]
const dataList = ref([] as any[])
async function fetchContractsByCustomer(customerId: number) {
  status.loading = true
  try {
    const res = await getCustomerContractList(customerId)

    dataList.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const handleShowVisible = (id: number) => {
  contractId.value = id
  status.visible = true
}

const handleClickDownload = (fileUrl: string) => {
  window.open(fileUrl)
}

const handleClickAddContractSuccess = () => {
  status.visible = false
  fetchContractsByCustomer(customerId.value)
}

onMounted(() => {
  fetchContractsByCustomer(customerId.value)
})

watch(customerId, (val) => {
  fetchContractsByCustomer(val)
})

</script>

<template lang="pug">
.contract-list 
  a-button(
    class="add-button"
    type="primary"
    @click="() => handleShowVisible()"
  ) 新增合同

  .section-body
    a-spin(:spinning="status.loading")
      a-table(
        :columns="columnsList" 
        :data-source="dataList" 
        :pagination="false"
        rowClassName="clickable"
      )
        template(#bodyCell="{ text, record, index, column }")
          template(v-if="column.dataIndex === 'action'")
            a-button(type="link" @click="() => handleClickDownload(record.fileUrl)") 下载合同
            a-button(type="link" @click="() => handleShowVisible(record.id)") 编辑

  FinanceContractForm(
    v-if="status.visible"
    :customerId="customerId"
    :contractId="contractId"
    @close="() => status.visible = false"
    @success="handleClickAddContractSuccess"
  )
</template>
<style lang="scss" scoped>
.contract-list {
  position: relative;
}

.add-button {
  position: absolute;
  right: 0;
  top: -52px;
}
</style>
