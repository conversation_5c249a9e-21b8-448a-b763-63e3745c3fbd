<template lang="pug">
.talent-search-item(v-if="talent")
  .talent-head
    a-avatar(:size="64", shape="square", :src="talent.talentBase.photoUrl") {{ talent.talentBase.realName }}
    .gender
    //- .site {{ getSite(item.site) }}
  .talent-info 
    .talent-basic-name 
      span.name {{ talent.talentBase.realName }}
      template(v-if="talentStatus")
        a-divider(type="vertical")
        span.status {{ talent.talentBase.employeeStatusStr }}
    .talent-basic-other
      span(v-if="talent.talentBase.gender") {{ talent.talentBase.gender === 1 ? '男' : '女' }}
      span(v-else) 性别未知
      a-divider(type="vertical")
      span {{ getFromTimeBirthday(talent.talentBase.birthday) }}
      a-divider(type="vertical")
      span {{ getFromTimeBeginWorkDate(talent.talentBase.beginWorkDate) }}

    .talent-mobile
      span(v-if="talent.talentBase.mobileNumber")
        MobileOutlined(style="margin-right: 4px; color: #ccc;")
      span {{ talent.talentBase.mobileNumber }}
    .talent-email
      span(v-if="talent.talentBase.email")
        MailOutlined(style="margin-right: 6px; color: #ccc;")
      span {{ talent.talentBase.email }}

</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { computed, onMounted } from 'vue';
import { MailOutlined, MobileOutlined } from '@ant-design/icons-vue'
import { toRef } from 'vue'

const props = defineProps<{talent: any}>()
const talent = toRef(props, 'talent')
const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(val).format('YYYY.MM')
}

function getFromTimeBeginWorkDate(strDate: string) {
  if (!strDate || strDate === "0") {
    return "未知工作年限";
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}年工作年限`
  }
}

function getFromTimeBirthday(strDate: string) {
  if (!strDate || strDate === "0") {
    return "年龄未知"
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}岁`
  }
}

const talentStatus = computed(()=>{
  if (talent.value.talentBase.updateTimeEmployeeStatus && talent.value.talentBase.employeeStatus !== 0) {
    const now = dayjs()
    const diff = now.diff(talent.value.talentBase.updateTimeEmployeeStatus, 'day')
    if (diff < 90) return talent.value.talentBase.employeeStatus
    else return ''
  } else {
    return ''
  }
})

onMounted(()=>{

})

</script>

<style lang="sass" scoped>
.talent-search-item
  width: 100%
  color: #444
  cursor: pointer
  position: relative
  padding-left: 78px
  .talent-head
    position: absolute
    left: 0

  .talent-info
    line-height: 24px
    font-size: 13px
    .talent-basic-name

      .name
        font-weight: 700
        font-size: 16px
      .status
        font-weight: 400
        font-size: 13px
        color: #999

    .talent-mobile, .talent-email
      width: 100%
      overflow: hidden
      text-overflow: ellipsis
      white-space: nowrap

    .talent-intro
      color: #999
      width: 100%
      display: -webkit-box
      -webkit-line-clamp: 3
      -webkit-box-orient: vertical
      text-overflow: ellipsis
      overflow: hidden
</style>