<script lang="ts" setup>
import { defineComponent, reactive, toRaw, ref } from 'vue'
import type { UnwrapRef } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import dayjs, { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'

import { getCurrencyList, getPayMethodList } from '@/api/dictionary'
import { getEntityList, setReceivePayment } from '@/api/finance'
import { getProcessInstances, getJobRequireSearch } from '@/api/job'

const emit = defineEmits(['close', 'finish'])

function handleCancel() {
  emit('close')
}

const formInstance = ref()
const form = reactive({

})

function handleSave() {
  
}

</script>

<template lang="pug">
.create-payment-form
  .form-body
    a-form(:model="form" ref="formInstance" layout="vertical")

      a-form-item(label="交易流水号")
        a-input()


      a-form-item(label="付款主体")
        a-input()
          template(#addonAfter)
            a-select(style="width: 100px")
              a-select-option(value="公司") 公司
              a-select-option(value="政府机构") 政府机构
              a-select-option(value="个人") 个人



      a-form-item(label="收款账户")
        a-select(
          show-search
        )

      //- a-form-item(label="收款账户")
      //-   a-input()

      a-form-item(label="收款金额")
        a-input-number(
          style="width: 100%"
        )
          template(#addonAfter)
            a-select(
              style="width: 100px"
            )
              a-select-option(value="1") 人民币
              a-select-option(value="2") 美元
              a-select-option(value="3") USDC

      a-form-item(label="收款日期")
        a-date-picker(
          style="width: 100%"
        )

      a-form-item(label="付款方式")
        a-select()
          a-select-option(:value="1") 银行转账
          a-select-option(:value="2") 微信支付
          a-select-option(:value="3") 支付宝支付
          a-select-option(:value="4") 现金支付
          a-select-option(:value="5") 支票支付
          a-select-option(:value="6") Web3钱包支付

  .form-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="handleSave" type="primary") 保存

</template>

<style lang="sass" scoped>
.create-payment-form
  padding-bottom: 56px
  height: 100%
  position: relative
  box-sizing: border-box

  .form-body
   padding: 20px

  .form-action 
    position: absolute
    bottom: 0
    background-color: #fff
    border-top: 1px solid #f0f0f0
    width: 100%
    padding: 12px 24px
    text-align: right
    box-sizing: border-box
</style>