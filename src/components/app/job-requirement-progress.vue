<template lang="pug">
mixin resume-action-btns
  .resume-item__btns
    //- a-space(:size="8")
    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_get'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_get', record.task.id, record.talent.id)}") 推荐给客户

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_get_to_customer'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_get_to_customer', record.task.id, record.talent.id)}") 约面试

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_interview'")
      a-button(ghost  type="primary" @click="() => {showInterviewList(record.task.id, record.talent.id)}") 面试结果反馈
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_interview', record.task.id, record.talent.id)}" v-if="isFinalInterview(record)") 谈薪

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_to_salary'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_to_salary', record.task.id, record.talent.id)}") 已谈薪

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_to_offer'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_to_offer', record.task.id, record.talent.id)}") 确认OFFER

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_to_inspect'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_to_inspect', record.task.id, record.talent.id)}") 已背调

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_to_hired'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_to_hired', record.task.id, record.talent.id)}") 已入职

    template(v-if="record.task?.taskDefinitionKey === 'position_pm_talent_in_keep'")
      a-button(type="primary"  @click="() => {tackAction('position_pm_talent_in_keep', record.task.id, record.talent.id)}") 已过保

    template(v-if="record.task?.id === undefined && record.process?.variables.obsoleteReason !== undefined")
      a-button(type="primary"  @click="() => {rePushTalentToJobRequirement(jobRequirementId, record.talent.id)}") 重新加入项目

    template(v-if="record.task?.id !== undefined")
      a-button(v-if="record.task?.taskDefinitionKey != 'position_pm_talent_to_obsolete'" ghost  type="primary"  @click="() => {tackAction('weed_out', record.task.id, record.talent.id)}") 淘汰


mixin resume-item
  a-row.resume-item(v-for="(record, index) in taskList")
    a-col(:span="8")

      JobTalentInfo(:taskDetail="record" @click="() => { showTalentDetail(record.talent) }")
    a-col(:span="12")
      JobTalentExp(:talent="record.talent")

    a-col(:span="4" align="right")
      +resume-action-btns

mixin bot-button
  .bot-container(@click="toggleChat")
    img(src="@/assets/smartdeer_logo.png") 

.job-progress-component
  .job-progress
    .job-progress-item(
      v-for="(task, index) in jobRequirementTaskDefinition",
      :key="index",
      @click="() => { handleTaskSelect(task) }"
      :class="{ active: selectedTask.taskDefinitionKey === task.taskDefinitionKey }"
    )
      .job-progress-item__left
        .job-progress-item__index {{ index + 1 }}
      .job-progress-item__right
        .job-progress-item__title {{ task.title }}
        .job-progress-item__count 
          a-popover(placement="top" trigger="hover")
            template(#content)
              div(v-if="(userStore.companyId != jobCompanyId) && (task.taskDefinitionKey !== 'position_pm_finished')") {{ `${userStore.companyName} ${task.companyTotal || 0} 人` }}
              div 全部 {{ task.total }} 人
            span(v-if="(userStore.companyId != jobCompanyId) && (task.taskDefinitionKey !== 'position_pm_finished')")  {{ task.companyTotal || 0 }} /
            span {{ ` ${task.total}` }}

  a-spin(:spinning="status.jobRequirementTaskListLoading")
    .job-progress__list(v-if="taskList.length > 0")
      +resume-item
      .job-progress__list-pagination
        a-pagination(
          v-model:current="pagination.current" 
          v-model:page-size="pagination.size" 
          :total="pagination.total"
          @change="hanldeChangePage"
        )
    .job-progress__empty(v-else)
      a-empty

  JobProgressActions(ref="jobActionsInstance", :jobRequirementId="jobRequirementId" @update="handleUpdate")

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" @close="status.showTalentDetail = false" :jobId="jobRequirementId")
  +bot-button

</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getJobRequirementHeader } from '@/api/position'
import { finishJobTask, createSubProcessByTask, getJobRequirementTaskList, addInterviewTask, getInterviewTaskList, updateInterviewTaskVariables, getJobRequirementFinishedTaskList } from '@/api/job'
import { getAgeFromBirthday, getShortDate } from '@/utils/string'
import { useUserStore } from '@/store/user.store'
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import dayjs from 'dayjs'
import JobProgressActions from '@/components/app/job-progress-actions.vue'
import { addTalentToJobPool } from '@/api/job'
import JobTalentInfo from '@/components/app/job-talent-info.vue'
import JobTalentExp from '@/components/app/job-talent-exp.vue'
import { v4 } from 'uuid'

const userStore = useUserStore()
const jobActionsInstance = ref()
const props = defineProps<{jobRequirementId: number, jobCompanyId:number}>()
const jobRequirementId = toRef(props, 'jobRequirementId')
const jobCompanyId = toRef(props, 'jobCompanyId')

const status = reactive({
  jobRequirementHeaderLoading: false,
  jobRequirementTaskListLoading: false,
  showModal: false,
  taskAction: false,
  showTalentDetail: false,
  showInterviewList: false,
  interviewListLoading: false,
  deleteInterview: false,
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0,
})

const cozeWebSDK = ref(null)

// 获取职位进展的节点和数值状态
const jobRequirementTaskDefinition = ref<any[]>([])
let finishedNum = 0
async function fetchJobRequirementTaskDefinition(jobId: number | undefined) {
  if (!jobId) return
  status.jobRequirementHeaderLoading = true
  try {
    const res = await getJobRequirementHeader(jobId)
    jobRequirementTaskDefinition.value = []
    for (let key in res.data) {
      jobRequirementTaskDefinition.value.push(
        Object.assign({}, res.data[key], { title: key })
      )
    }
    jobRequirementTaskDefinition.value.push({
      processDefinitionKey: "linglupin_Position_PM_Process",
      sort: 8,
      taskDefinitionKey: "position_pm_finished",
      title: "已完成",
      total: finishedNum
    })
    jobRequirementTaskDefinition.value.sort((a, b) => a.sort - b.sort)
  } catch (err: any) {
    message.error(err.message)
  }
  status.jobRequirementHeaderLoading = false
}

function getExperiences(talentItem: any): any[] {
  const result: any[] = []
  if (!talentItem) return []

  if (talentItem.experiences && talentItem.experiences.length > 0) {
    // const expirence = talentItem.experiences[0]
    talentItem.experiences.forEach((expirence: any) => {
      if (result.length < 3) {
        const textStr = `${expirence.companyName} - ${expirence.position} ${getShortDate(expirence.fromDate)} - ${getShortDate(expirence.toDate)}`
        result.push({
          exp: `${expirence.companyName} - ${expirence.position}`,
          timeRange: `${getShortDate(expirence.fromDate)} - ${getShortDate(expirence.toDate)}`,
          textStr,
          type: 'work'
        })
      }
    })
  }

  return result
}

function getEducations(talentItem: any): any[] {
  const result: any[] = []
  if (!talentItem) return []
  if (talentItem.educations && talentItem.educations.length > 0) {
    // const education = talentItem.educations[0]
    talentItem.educations.forEach((education: any) => {
      if (result.length < 2) {
        const textStr = `${education.schoolName} - ${education.degreeStr} - ${education.major} ${getShortDate(education.fromDate)} - ${getShortDate(education.toDate)}`
        result.push({
          exp: `${education.schoolName} - ${education.degreeStr} - ${education.major}`,
          timeRange: `${getShortDate(education.fromDate)} - ${getShortDate(education.toDate)}`,
          textStr,
          type: 'education'
        })
      }
    })
  }

  return result
}

function isFinalInterview(record:any) {
  return record.task.localVariables?.isFinalInterview == '1'
}

const selectedTask = ref<any>({})
const taskList = ref<any[]>([])
async function fetchTaskList(jobId: number, headerItem: any) {

  // 只有推荐给PM这个列表的 assignee是0, 其他的都是自己。
  let assignee = null
  if (headerItem.taskDefinitionKey === 'position_pm_talent_get') assignee = 0
  else assignee = userStore.id

  status.jobRequirementTaskListLoading = true
  try {
    const taskListRes = await getJobRequirementTaskList({
      // assignee: assignee,
      jobRequirementId: jobId,
      taskDefinitionKey: headerItem.taskDefinitionKey,
      current: pagination.current,
      size: pagination.size
    })

    taskList.value = taskListRes.data.tasks
    await fetchJobRequirementTaskDefinition(jobRequirementId.value)
    pagination.total = taskListRes.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.jobRequirementTaskListLoading = false
}

const hanldeChangePage = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.size = pageSize

  if (selectedTask.value.taskDefinitionKey === 'position_pm_finished') {
    fetchFinishedTaskList()
  } else {
    fetchTaskList(jobRequirementId.value!, selectedTask.value)
  }
}

async function fetchFinishedTaskListLength() {
  if (jobRequirementId.value === 0) return
  
  const taskListRes = await getJobRequirementFinishedTaskList({
    jobRequirementId: jobRequirementId.value,
    processDefinitionKey: 'linglupin_Position_PM_Process',
    current: pagination.current,
    size: pagination.size
  })

  const total = taskListRes.data.total

  finishedNum = total

  jobRequirementTaskDefinition.value[jobRequirementTaskDefinition.value.length - 1].total = total
}

async function fetchFinishedTaskList() {
  try {
    const taskListRes = await getJobRequirementFinishedTaskList({
      jobRequirementId: jobRequirementId.value,
      processDefinitionKey: 'linglupin_Position_PM_Process',
      current: pagination.current,
      size: pagination.size
    })

    taskList.value = taskListRes.data.processes.map((item: any, index: number) => {
      return item
      // const talent = item.talent
      // const suggestUser = item.suggestUser
      // const talentCreateUser = item.talentCreateUser
      // const processInstance = item.process
      // // const task = item.task
      // // const isFinalInterview = item.task.localVariables?.isFinalInterview === 1
      // const expectCities = !talent.demands[0] ? '未知' : talent.demands[0].areaDemandStrs.length ? '未知' : talent.demands[0].areaDemandStrs.join(',')

      // return {
      //   id: talent.id,
      //   birthday: talent.birthday,
      //   name: talent.realName,
      //   photoName:talent.realName.substring(0,1),
      //   photo: talent.photo,
      //   age: getAgeFromBirthday(talent.birthday),
      //   workYears: talent.value?.workYears,
      //   degree: talent.latestDegreeStr,
      //   status: talent.employeeStatusStr,
      //   talentId: talent.id,
      //   experiences: getExperiences(talent),
      //   educations: getEducations(talent),
      //   expectPosition: talent.demands[0] ? talent.demands[0].positionDemand : '',
      //   poolId: talent.poolId,
      //   expectCities,
      //   suggestName: suggestUser.realName,
      //   talentCreateName: talentCreateUser === undefined ? "系统": talentCreateUser.realName,
      //   suggestTime: item.process.startTime,
      //   processVariables: processInstance.variables
      //   // taskId: task.id,
      //   // taskDefinitionKey: task.taskDefinitionKey,
      //   // isFinalInterview
      // }
    })

    const total = taskListRes.data.total

    pagination.total = total

    await fetchJobRequirementTaskDefinition(jobRequirementId.value)

    finishedNum = total

    jobRequirementTaskDefinition.value[jobRequirementTaskDefinition.value.length - 1].total = total
  } catch (err: any) {
    message.error(err.message)
  }
}

async function handleTaskSelect(taskItem: any) {
  if (!taskItem) return
  selectedTask.value = taskItem
  pagination.current = 1

  if (taskItem.taskDefinitionKey === 'position_pm_finished') {
    fetchFinishedTaskList()
  } else {
    fetchTaskList(jobRequirementId.value!, selectedTask.value)
  }
}

let curActionKey = ''
async function tackAction(actionKey:string, taskId:string, talentId:number) {
  curActionKey = actionKey
  jobActionsInstance.value.trigger(actionKey, taskId, talentId)
}

async function showInterviewList(taskId:string, talentId:number) {
  jobActionsInstance.value.showInterviewList(taskId, talentId)
}

async function rePushTalentToJobRequirement(jobRequirementId: number, talentId: number) {
  Modal.confirm({
    title: "确认将候选人重新加入项目？",
    content: "重新加入项目后会加入新的统计",
    onOk() {
      pushTalentToJobRequirement(jobRequirementId, talentId)
    },
    onCancel() {

    }
  })
}

async function pushTalentToJobRequirement(jobRequirementId: number, talentId: number) {
  try {
    const res = await addTalentToJobPool(jobRequirementId, talentId)
    message.success(`人才加入职位成功！`)
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleUpdate() {
  fetchTaskList(jobRequirementId.value!, selectedTask.value)
  
  if (curActionKey === 'position_pm_talent_in_keep') {
    fetchFinishedTaskListLength()
  }
}

const talentId = ref<number>()
function showTalentDetail(talent: any) {
  talentId.value = talent.id
  status.showTalentDetail = true
}

function formatDate(dateString: string) {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

function toggleChat() {
  console.log(cozeWebSDK.value)
  if (cozeWebSDK.value) {
    cozeWebSDK.value.showChatBot()
  }
}

onMounted(async () => {
  await fetchJobRequirementTaskDefinition(jobRequirementId.value)
  handleTaskSelect(jobRequirementTaskDefinition.value[0])
  fetchFinishedTaskListLength()
  const uuid = v4()

  cozeWebSDK.value = new CozeWebSDK.WebChatClient({
    config: {
      botId: '7509366156747833394',
      isIframe: false,
      botInfo: {
        parameters: {
          "job_title": "事业部总经理",
          "customer_name": "四川长虹佳华信息产品有限责任公司",
        }
      }
    },
    userInfo: {
      nickname: 'User',
      id: uuid
    },
    ui: {
      base: {
        icon: 'https://static.smartdeer.com/logo.png',
        layout: 'pc',
        zIndex: 1000
      },
      chatBot: {
        title: '小 R',
        uploadable: false,
      },
      asstBtn: {
        isNeed: false
      },
      footer: {
        isShow: true,
        expressionText: 'Powered by SmartDeer.'
      }
    }
  })
})

watch(jobRequirementId, (value) => {
  fetchJobRequirementTaskDefinition(value)
  handleTaskSelect(selectedTask.value)

  console.log(jobCompanyId.value, 'jobcompanyId')
  console.log(userStore.companyId, 'compnayId')
})

defineExpose({
  refresh: () => {
    fetchTaskList(jobRequirementId.value!, selectedTask.value)
  }
})

</script>

<style lang="scss" scoped>
.job-progress {
  margin-top: 12px;
  padding: 0 24px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;

  &__list {
    &-pagination {
      padding: 16px 16px;
      text-align: right;
    }
  }

  &__empty {
    padding: 20px 0;
  }
}

.job-progress-item {
  display: flex;
  flex: 1 1 auto;
  cursor: pointer;
  transition: all .2s;

  &:hover {
    opacity: .6;
    transition: all .2s;
  }

  &.active {
    color: #FF9111;

    .job-progress-item__index {
      background-color: #FF9111;
    }
  }

  &__index {
    border-radius: 50%;
    height: 16px;
    width: 16px;
    line-height: 16px;
    text-align: center;
    background-color: #8C8C8C;
    color: #fff;
    font-size: 12px;
    margin-right: 8px;
  }

  &__title {
    line-height: 16px;
    font-size: 14px;
  }
}

.resume-item {
  // height: 170px;
  padding: 24px;
  border-bottom: 1px solid #F7F8F8;
  position: relative;
  width: 100%;

  &__row {
    cursor: pointer;
    position: relative;
    padding-left: 70px;
    width: 100%;
  }

  .ant-avatar {
    position: absolute;
    left: 10px;
  }

  &__logo {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    background: #ccc;
  }

  &__title {
    margin-bottom: 10px;
    strong {
      font-size: 16px;
    }
    div {
      font-size: 14px;
      line-height: 20px;
    }
  }

  &__summary {
    display: flex;
  }

  &__content {
    flex-shrink: 0;
    width: 250px;

    div {
      // height: 20px;
      line-height: 20px;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__other {
    // display: flex;
    // margin-top: 24px;
    font-size: 14px;
    // display: flex;
    margin-left: 100px;
    flex: 1;
    padding-right: 130px;
    width: 0;

    .expirence-item {
      padding-left: 24px;
      padding-right: 24px;
      position: relative;
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: 0;
      }
      // display: flex;
      // overflow: hidden;

      &__icon {
        position: absolute;
        left: 0;
        top: 2px;

        img {
          width: 16px;
          display: block;
        }
      }

      &__exp {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &__time-range {
        width: 150px;
        padding-left: 8px;
      }
    }
  }

  &__btns {
    :deep(.ant-btn) {
      min-width: 128px;
      display: block;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}


.interview-list-container {
  padding: 16px;
  .drawer-action{
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    padding: 16px;
    text-align: right;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}

.bot-container {
  z-index: 1000;
  cursor: pointer;
  display: flex;
  position: fixed;
  bottom: 80px;
  right: 50px;
  transition: transform 0.3s ease;
  &:hover {
    transform: scale(1.16);
  }
  img {
    width: 60px;
    height: 60px;
    border-radius: 30px;

  }
}
</style>