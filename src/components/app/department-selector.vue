<template lang="pug">
mixin department-selector
  .department-selector
    .department-selector-left
      //- .department-selector-search
      //-   a-input(@change="")
      //-     template(#prefix)
      //-       SearchOutlined
      .department-list
        .department-list-container(v-if="departmentTreeData.length > 0")
          .root-department()
            .root-department__title(:class="{ active: selectedDepartmentIds.includes(0)  }")
              HomeOutlined.root-department__icon
              span.root-department__title-text {{ userStore.companyName }}
          .children-department
            itp-department(:data="departmentTreeData" :selected="selectedDepartmentIds" @check="check" mode="check")

    .department-selector-right
      .department-select-selected
        .department-item(v-for="(dept, index) in selectedDepartmentList", :key="index")
          //- a-avatar(:src="staff.formalPhoto") {{ staff.realName }}
          ApartmentOutlined.department-item__icon
          .department-item__name {{ dept.deptName }}
          a.department-item__action(@click="() => { removeDept(dept) }") 移除

a-modal(
  v-model:open="showModal",
  title="选择部门",
  :width="640",
  :body-style="{ padding: '12px' }",
  :ok-button-props="{ disabled: selectedDepartmentIds.length === 0 }"
  :destroy-on-close="true",
  @ok="departmentListConfirm"
)
  a-spin(:spinning="status.loading")
    +department-selector
</template>

<script lang="ts" setup>

import { reactive, ref, toRefs, watch, onMounted } from 'vue'
import { HomeOutlined, SearchOutlined, ApartmentOutlined } from '@ant-design/icons-vue'
import { getCompanyUserList } from '@/api/system/users'
import { message } from 'ant-design-vue'
import { getCompanyDepartment } from '@/api/system/department'
import { useUserStore } from '@/store/user.store'

interface Staff {
  id: number,
  name: string,
  department: string[],
  gender: string,
}

interface Department {
  id: number,
  companyId: number,
  parentId: number,
  deptName: string,
  userId: number,
  children?: Department[],
  title?: string,
  key: number,
}

// export default defineComponent({
//   components: { SearchOutlined },
//   props: { visible: Boolean, multi: Boolean },
//   emits: ['update:visible', 'select'],

const userStore = useUserStore()

const props = defineProps({
  visible: Boolean, 
  multi: Boolean,
  list: {
    type: Array,
    default: []
  }
})

const emit = defineEmits(['update:visible', 'select'],)

// setup(props: any, { emit }: any) {

const { visible } = toRefs(props)
const showModal = ref(false)
showModal.value = visible.value

const columnConfig = [
  { title: '员工', key: 'name', dataIndex: 'name' },
  { title: '部门', key: 'name', dataIndex: 'department' },
]

const staffList = ref([] as any[])
const status = reactive({
  loading: false
})

watch(visible, (value, oldValue) => {
  if (value !== showModal.value) {
    showModal.value = value
    selectedDepartmentIds.value = props.list
  }
})

// 向外抛出当前modal的状态
watch(showModal, (value, oldValue) => {
  emit('update:visible', value)
})

const departmentTreeData = ref([] as any[])

async function getDepartment() {
  try {
    const res = await getCompanyDepartment()
    departmentTreeData.value = _processDepartmentData(res.data)
  } catch (err: any) {
    message.error(err.message)
  }
}

const deptMap = new Map<number, Department>()

function _processDepartmentData(departments: Department[]) {
  // const deptMap = new Map<number, Department>()
  const deptTree = new Array<Department>()

  // 初始化数据结构，并更具id创建索引
  departments.forEach((dept) => {
    dept.children = []
    dept.title = dept.deptName
    dept.key = dept.id
    deptMap.set(dept.id, dept)

    // 如果父级部门ID是0， 则表示上级无企业
    if (dept.parentId === 0) deptTree.push(dept)
  })

  // 组装父子关系
  deptMap.forEach((dept, key) => {
    if (dept.parentId) {
      const parent = deptMap.get(dept.parentId)
      parent?.children?.push(dept)
    }
  })

  return deptTree
}

onMounted(() => {
  getDepartment()
})

const selectedDepartmentIds = ref<any[]>([])
const selectedDepartmentList = ref<any[]>([])
function check(departmentIds: number[]) {
  selectedDepartmentList.value = departmentIds.map((item: any, index: number) => {
    return deptMap.get(item)
  })
  selectedDepartmentIds.value = departmentIds
}

function removeDept(department: any) {
  const idx = selectedDepartmentList.value.indexOf(department)
  selectedDepartmentIds.value = selectedDepartmentIds.value.filter((item:any, index:number) => index !== idx )
  selectedDepartmentList.value.splice(idx, 1)
}

async function initUserList() {
  try {
    const res = await getCompanyUserList()
    staffList.value = res.data.map((staff: any) => {
      return Object.assign({}, staff, { key: staff })
    })
  } catch (err) {
    message.error('获取员工列表失败，请重试。')
  }
}

function departmentListConfirm() {
  emit('select', selectedDepartmentIds.value)
}

onMounted(() => {
  initUserList()
})



</script>

<style lang="scss" scoped>
.department-selector {
  border-radius: 6px;
  display: flex;
  border: 1px solid #e8e8e8;
  min-height: 540px;

  &-left {
    flex: 1 1 auto;
    width: 50%;
  }

  &-right {
    flex: 1 1 auto;
    width: 50%;
    border-left: 1px solid #e8e8e8;
  }

  &-search {
    padding: 12px;
  }
}

.department-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;

  &__name {
    padding: 0 12px;
    display: inline;
    flex: 1 1 auto;
  }

  &__action {
    width: 40px;
    flex: 0 0 auto;
  }
}


.department-list {
  width: 100%;

  .department-list-container {
    border-radius: 8px;
    margin-bottom: 12px;

    .root-department {
      &__title {
        padding: 8px 12px;
        border-radius: 4px;

        &:hover {
          background-color: #f9f9f9;
          cursor: pointer;
        }
      }

      &__icon {
        margin-right: 8px;
      }
    }

    .active {
      background-color: RGBA(255, 145, 17, .1);
      color: RGBA(255, 145, 17, 1);
      transition: all .2s;

      &:hover {
        background-color: RGBA(255, 145, 17, .2);
        transition: all .2s;
      }
    }
  }
}


.children-department {
  padding-left: 24px;
}
</style> 
