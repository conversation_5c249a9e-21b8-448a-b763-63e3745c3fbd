<template lang="pug">

mixin email-editor
  a-form.email-editor(layout="vertical" :form="emailForm")
    a-form-item(label="收件人")
      a-input(v-model:value="emailForm.to" disabled)

    a-row(:gutter="[16, 16]")
      a-col(:span="12")
        a-form-item(label="推荐职位")
          a-button(block @click="status.showPositionSelect=true") 选择职位

    a-form-item(label="邮件标题")
      a-input(v-model:value="emailForm.subject")

      //- a-col(:span="12")
      //-   a-form-item(label="模板")
      //-     a-select(v-model:value="emailForm.subject")

    a-form-item()
      quill-editor(v-model:value="emailForm.content")

mixin send-email-action
  .email-send-action
    .right
      a-button(@click="handleCancel")  取消
    .left
      a-button(@click="handleSendEmail" type="primary") 发送

.sent-email-comp
  a-spin(:spinning="status.loading")
    +email-editor
    +send-email-action

  JobSelector(v-model:visible="status.showPositionSelect" :selected="[]" @select="handelPositionSelect" :multi="false")
  
</template>

<script lang="ts" setup>
import { getCompanyUserDetail } from '@/api/system/users'
import { getTalentDetail, sendEmail } from '@/api/talent/talent'
import quillEditor from '@/components/quill-editor/quill-editor.vue'
import { useUserStore } from '@/store/user.store'
import { ConsoleSqlOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref, toRef } from 'vue'
import Handlebars from 'handlebars'
import template from '@/email-template/talent-touch'
import { resetScroll } from 'vant/lib/utils'
import { getPositionDetail } from '@/api/position'
import JobSelector from './job-selector.vue'

const props = defineProps<{ to: string, talentId: number, positionId: number }>()
const emit = defineEmits(['close'])

const status = reactive({
  loading: false,
  showPositionSelect: false
})

const to = toRef(props, 'to')
const talentId = toRef(props, 'talentId')
const positionId = toRef(props, 'positionId')

const emailForm = reactive({
  to: to.value,
  subject: '',
  content: ''
})

const talentDetail = ref({})
const positionDetail = ref<any>({})
const staffDetail = ref({})

async function sendTalentEmail(talentId: number, to: string, subject: string, content: string) {
  status.loading = true
  try {
    const res = await sendEmail(talentId, to, subject, content )
    message.success('邮件发送成功！')
    emit('close')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleSendEmail() {
  const content = emailForm.content.replaceAll('\n', `<br/>`)
  sendTalentEmail(talentId.value, emailForm.to, emailForm.subject, content)
}

async function fetchTalentDetail(talentId: number) {
  try {
    const res = await getTalentDetail(talentId)
    return res.data.talent
  } catch (err: any) {
    message.error(err.message)
  }
}

async function fetchPositionDetail(positionId: number) {
  if (!positionId) return
  try {
    const res = await getPositionDetail(positionId)
    return res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const email = Handlebars.compile(template.template)
async function renderTemplate() {
  const content = email({
    talent: talentDetail.value,
    staff: staffDetail.value,
    position: positionDetail.value || {},
    landing: {
      link: `https://web.itp.smartdeer.work/h5/talent/${talentId.value}/position/${positionDetail.value.id}/touch-me`,
      text: '我感兴趣，请联系我'
    }
  })
  emailForm.content = content
}

async function fetchStaffDetail() {
  try {
    const userStore = useUserStore()
    const res = await getCompanyUserDetail(userStore.id)
    return res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function handelPositionSelect(jobs:any[]) {
  status.loading = true
  const job = jobs[0]
  positionDetail.value = await fetchPositionDetail(job.positionId)
  emailForm.subject = `有新的${positionDetail.value.positionTitle}职位来袭`
  renderTemplate()
  status.showPositionSelect = false
  status.loading = false
}

async function handleCancel() {
  emit('close')
}

const staff = ref()

onMounted(async () => {
  staffDetail.value = await fetchStaffDetail()
  talentDetail.value = await fetchTalentDetail(talentId.value)
  // renderTemplate()
})

</script>

<style lang="sass" scoped>
.sent-email-comp
  padding: 20px
  padding-bottom: 56px
  position: relative
  height: 100%
  box-sizing: border-box

.email-send-action 
  position: sticky
  display: flex
  justify-content: space-between
  bottom: 0
  background-color: #fff
  width: 100%
  padding: 12px 0
  text-align: right
  box-sizing: border-box
  
</style>