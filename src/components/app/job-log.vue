<template lang="pug">
a-spin(:spinning="status.loading")
  .job-logs(v-if="jobLogs.length")
    a-collapse(v-model:activeKey="jobLogsKey")
      a-collapse-panel(v-for="(item, index) in jobLogs" :key="item.task.id" :header="item.task.name")
        template(#extra)
          span.extra {{ item.companyUser?.realName }} {{ getFormatedDate(item.task.endTime, 'YYYY-MM-DD HH:mm') }}

        template(v-if="item.task.taskDefinitionKey === 'position_pm_talent_get'")
          .description-item
            .label 推荐人:
            .value {{ item.suggestUser.realName }}

        template(v-if="item.task.taskDefinitionKey === 'position_pm_talent_to_hired'")
          .edit-btn(v-if="showAction")
            a-button(type="primary" @click="() => {tackAction('position_pm_talent_to_hired', item.task.id, true)}") 编辑
          .description-item
            .label 入职时间:
            .value {{ item.task.localVariables.onboardingDate }}

        template(v-if="item.task.taskDefinitionKey === 'position_pm_talent_to_inspect'")
          .edit-btn(v-if="showAction")
            a-button(type="primary" @click="() => {tackAction('position_pm_talent_to_inspect', item.task.id, true)}") 编辑
          .description-item
            .label 背调信息:
            .value {{ item.task.localVariables.inspect }}

        template(v-if="item.task.taskDefinitionKey === 'position_pm_talent_to_offer'")
          .edit-btn(v-if="showAction")

            a-button(type="primary" @click="() => {tackAction('position_pm_talent_to_offer', item.task.id, true)}") 编辑
          template(v-if="item.task.localVariables.salaryUnit === 'monthly'")
            .description-item
              .label 薪资类型:
              .value 月薪
            .description-item
              .label 每月薪资:
              .value {{ item.task.localVariables.salary }} {{ item.task.localVariables.currencyType }} / 每月
            .description-item
              .label 发放月数:
              .value {{ item.task.localVariables.payMonth }}

          template(v-if="item.task.localVariables.salaryUnit === 'yearly'")
            .description-item
              .label 薪资类型:
              .value 年薪
            .description-item
              .label 薪资:
              .value {{ item.task.localVariables.salary }} {{ item.task.localVariables.currencyType }} / 每年

          .description-item
            .label 预计入职时间:
            .value {{ item.task.localVariables.expectOnboardingDate }}
          .description-item
            .label 预计过保时间:
            .value {{ item.task.localVariables.expectOverinsureDate }}
          .description-item
            .label 预计佣金:
            .value {{ item.task.localVariables.expectCommission }} {{ item.task.localVariables.commissionCurrencyType }}

        template(v-if="item.task.taskDefinitionKey === 'position_pm_talent_to_obsolete'")
          .description-item
            .label 淘汰原因:
            .value {{ item.task.variables.obsoleteReason }}
        .description-item
          .label 任务创建时间:
          .value {{  getFormatedDate(item.task.createTime, 'YYYY-MM-DD HH:mm') }}
        .description-item
          .label 任务完成时间:
          .value {{  getFormatedDate(item.task.endTime, 'YYYY-MM-DD HH:mm') }}
        .description-item
          .label 操作人:
          .value {{ item.companyUser?.realName }}

  JobProgressActions(ref="jobActionsInstance", :jobRequirementId="jobRequirementId" @update="handleUpdate")

</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import JobProgressActions from '@/components/app/job-progress-actions.vue'
import { getProcessTaskList } from '@/api/position'
import { onMounted, reactive, ref, toRef, watch } from 'vue'
import { message } from 'ant-design-vue';

const emit = defineEmits(['update'])
const props = defineProps<{ talentId?: number, processInstanceId: string, jobRequirementId: number, showAction?: boolean }>()
const talentId = toRef(props, 'talentId')
const processInstanceId = toRef(props, 'processInstanceId')
const jobRequirementId = toRef(props, 'jobRequirementId')
const showAction = toRef(props, 'showAction')

const jobActionsInstance = ref()
const jobLogs = ref<any[]>([])
const jobLogsKey = ref([] as any[])

const status = reactive({
  loading: false,
})

function getFormatedDate(date: string, formate: string) {
  return dayjs(date).format(formate)
}

async function fetchJobLogs(processInstanceId: string) {
  status.loading = true
  try {
    const res = await getProcessTaskList(processInstanceId)
    jobLogs.value = res.data.tasks

    if (Array.isArray(res.data.tasks) && res.data.tasks.length) {
      jobLogsKey.value = [res.data.tasks[0].task.id]
    } else {
      jobLogsKey.value = []
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function tackAction(actionKey: string, taskId: string | undefined, isUpdate: boolean) {
  jobActionsInstance.value.trigger(actionKey, taskId, talentId.value, isUpdate)
}

async function handleUpdate() {
  fetchJobLogs(processInstanceId.value)
  emit('update')
}

onMounted(() => {
  fetchJobLogs(processInstanceId.value)
})

watch(processInstanceId, (val) => {
  fetchJobLogs(val)
})
</script>

<style lang="scss" scoped>
.job-logs {
  margin-top: 16px;
  .extra {
    color: RGBA(0, 0, 0, 0.4);
  }
  .description-item {
    display: flex;

    .label {
      width: 120px;
      flex: 0 0 auto;
      color: RGBA(0, 0, 0, 0.4);
    }

    .value {
      flex: 1 1 auto;
    }
  }

  .edit-btn {
    line-height: 20px;
    text-align: right;
  }
}
</style>