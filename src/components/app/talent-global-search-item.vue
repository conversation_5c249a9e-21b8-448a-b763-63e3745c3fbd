<script lang="ts" setup>
import { onMounted, ref, toRef } from 'vue'
import dayjs from "dayjs"
import { MailOutlined, MobileOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { getFromYearMonthBirthday } from "@/utils/string"
import { watch } from 'vue';

const props = defineProps<{ item: any, degree: {} }>()
const emit = defineEmits(['open', 'chat'])
const item = toRef(props, 'item')

// TODO: Degree 从字典中获取
const degree = toRef(props, 'degree')

const getScore = (score: any) => {
  if (typeof score === 'string') {
    try {
      return JSON.parse(score)
    } catch (e) {
      return { final: 0 }
    }
  }
  return score
}

const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(Number(val) * 1000).format('YYYY.MM')
}

const talentExp = ref<any[]>([])
const talentEdu = ref<any[]>([])

function sortTalentExp(webSearchTalent: any) {
  if (!webSearchTalent || !webSearchTalent.experiences) {
    talentExp.value = []
  } else {
    talentExp.value = webSearchTalent.experiences.sort((a: any, b: any) => {
      const aTime = dayjs(a.startDate).valueOf()
      const bTime = dayjs(b.startDate).valueOf()
      return bTime - aTime
    })
  }

  if (!webSearchTalent || !webSearchTalent.educations) {
    talentEdu.value = []
  } else {
    talentEdu.value = webSearchTalent.educations.sort((a: any, b: any) => {
      const aTime = dayjs(a.startDate).valueOf()
      const bTime = dayjs(b.startDate).valueOf()
      return bTime - aTime
    })
  }
}

onMounted(() => {
  sortTalentExp(props.item)
})

watch(() => props.item, (val) => {
  sortTalentExp(val)
})

const getSite = (val: string) => {
  switch (Number(val)) {
    case 1:
      return 'ITP'
    case 2:
      return '猎聘'
    case 3:
      return '脉脉'
    case 4:
      return '领英'
    case 5:
      return 'Boss直聘'
    case 6:
      return 'SmartDeer'
    default:
      break;
  }
}

const getBtnText = (val: string) => {
  if (!isNaN(Number(val))) {
    return '打开ITP简历'
  }

  if (val.indexOf('liepin') !== -1) {
    return '打开猎聘'
  }

  if (val.indexOf('maimai') !== -1) {
    return '打开脉脉'
  }

  if (val.indexOf('linkedin') !== -1) {
    return '打开领英'
  }

  if (val.indexOf('zhipin') !== -1) {
    return '打开Boss直聘'
  }
}

</script>

<template lang="pug">
.talent-search-item
  .talent-head-img
    a-avatar(:size="80", shape="square", :src="item.avatar") {{ item.name }}
    .site {{ getSite(item.site) }}
  .talent-basic 
    .talent-basic-name {{ item.name }}
    .talent-basic-other
      a-space(:size="12")
        span ID: {{ item.id }}
        span(v-if="item.sex > 0") {{ item.sex === 1 ? '男' : '女' }}
        span(v-else) 性别未知
        span {{ getFromYearMonthBirthday(item.birthYearMonth) }}
        span(v-if="item.workingExperience >= 0" ) {{ item.workingExperience }} 年工作经验
        span(v-else) 工作经验未知
        span {{ degree[item.highestDegree + ''] }}
        span(v-if="item.nowLocation") {{ item.nowLocation }}
        div(v-if="item.mobile")
          MobileOutlined(style="margin-right: 4px; color: #ccc;")
          span {{ item.mobile }}
        div(v-if="item.email")
          MailOutlined(style="margin-right: 6px; color: #ccc;")
          span {{ item.email }}

    .talent-basic-status {{ item.online === 'true' ? '在线' : item.activeStatus }}
  .talent-score(v-if="item.score" ) AI 人才评分 &nbsp;
    strong {{ getScore(item.score).final }} &nbsp;
    a-popover(trigger="hover")
      template(#content)
        .score-detail
          .score-header
            .score-label 评分项
            .score-value 分数
          template(v-for="(value, key) in getScore(item.score)")
            template(v-if="key !== 'final'")
              .score-item
                .score-label {{ key }}: 
                .score-value {{ Array.isArray(value) ? value.join('、') : value }}
      InfoCircleOutlined(style="margin-right: 8px; color: #ccc;")
  .talent-tags
    span(v-if="Array.isArray(item.fields)" )
      a-space(:size="[0, 8]" wrap)
        a-tag(color="orange" v-for="tag in item.fields" :key="tag") {{ tag }}

  .talent-info
    .exps    
      .exps-item(v-for="(exp, i) in talentExp" :key="i")
        .exps-time {{ exp.startDate }} - {{ exp.endDate }}
        div {{ exp.company }} 
        div(v-if="exp.title") · {{ exp.title }}

    .educations
      .educations-item(v-for="(education, i) in talentEdu" :key="i")
        .educations-time {{ education.startDate }} - {{ education.endDate }}
        div {{ education.school }} 
        div(v-if="education.speciality") · {{ education.speciality }}
</template>

<style lang="scss" scoped>
.talent-search-item {
  padding: 24px 0px 24px 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #f0f0f0;
  min-height: 128px;

  &:hover {
    background-color: #fafafa;
    transition: all 0.2s;
  }

  .site {
    background: RGBA(0, 0, 0, .2);
    backdrop-filter: blur(10px);
    color: #fff;
    height: 23px;
    line-height: 23px;
    text-align: center;
  }

  .talent-head-img {
    position: absolute;
    left: 0px;
    top: 24px;

    .site {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .talent-basic {
    display: flex;
    align-items: center;
    position: relative;

    &-name {
      font-size: 16px;
      font-weight: bold;
    }

    &-other {
      margin-left: 24px;
      color: #888;
      font-size: 14px;
    }

    &-status {
      position: absolute;
      right: 0;
      color: #7F7F7F;
    }

    .btns {
      position: absolute;
      right: 0px;
      top: -10px;
    }
  }

  .talent-tags {
    margin-top: 6px;
  }

  .talent-info {
    display: flex;
    margin-top: 10px;
  }
}

.exps {
  width: 50%;
  padding-right: 20px;
  border-right: 1px solid #d7d7d7;
  font-size: 13px;

  &-time {
    width: 140px;
    min-width: 140px;
  }

  &-item {
    display: flex;
    margin-top: 6px;

    &:first-child {
      font-weight: 700;
      font-size: 13px;
      margin-top: 0px;
    }
  }
}

.educations {
  width: 50%;
  padding-left: 20px;

  &-time {
    width: 140px;
    min-width: 140px;
  }

  &-item {
    display: flex;
    margin-top: 6px;

    &:first-child {
      font-weight: 700;
      font-size: 13px;
      margin-top: 0px;
    }
  }
}

.red {
  color: #ff4d4f;
}
.talent-score {
  margin-top: 5px;
  font-size: 16px;
  font-family: 'Bebas';
  cursor: pointer;
}

.score-detail {
  padding: 8px;
  min-width: 300px;
  
  .score-header {
    display: flex;
    margin-bottom: 8px;
    font-weight: bold;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 4px;
  }

  .score-item {
    display: flex;
    margin-bottom: 4px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .score-label {
    width: 120px;
    flex-shrink: 0;
  }

  .score-value {
    flex: 1;
    word-break: break-all;
  }
}
</style>
