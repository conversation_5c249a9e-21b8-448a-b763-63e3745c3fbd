<template lang="pug">

mixin talent-follow-up
  .follow-up
    .follow-up__head
      a-tabs.follow-up-tab(v-model:activeKey="followUpTab" @tabClick="handleFollowUpTabChange")
        a-tab-pane(:key="1" tab="全部跟进记录")
        a-tab-pane(:key="2" tab="我的沟通记录")

    a-spin(:spinning="status.followUpLoading")
      .follow-up__textarea
        a-textarea.follow-up__textarea-input(v-model:value="followForm.comment" placeholder="添加跟进记录" :auto-size="{ minRows: 4, maxRows: 8 }" )
        a-button(@click="handleNewFollowUp") 提交

      .follow-up__comments
        .comments-item(v-for="(item, index) in followups" v-if="followups.length")
          .comments-item__comment {{item.comment}}
          a-row
            a-col(:span="8") 
              .comments-item__name {{item.assigneeName}}
            a-col(:span="16", align="right")
              .comments-item__date {{formatDate(item.createTime)}}
        .comment-empty(v-else)
          a-empty(description="暂无跟进数据")
        .comment-pagination(v-if="(followUpPagination.total / followUpPagination.pageSize) > 1")
          a-pagination(v-model:current="followUpPagination.current" :total="followUpPagination.total" :pageSize="followUpPagination.pageSize" show-less-items @change="handleFollowUpPageChange")

.talent-detail-modal
  a-spin(:spinning="status.detailLoading")
    a-row(:gutter="16")
      a-col(:span="16")
        .talent-detail-container
          TalentDetail(:talent="talent" :attachments="attachments" @update="handleTalentUpdateType")
          .talent-action
            a-space(:size="16" v-if="talentBlackList === false" )
              a-button(v-if="talentImUserId.imUserId" @click="()=>{handleChat(talent)}") 开聊
              a-button(v-if="talent.talent.email" @click="()=>{handleSendEmail(talent)}") 发送邮件
              a-button(@click="()=>{status.showSmartDeerWorkModal = true}") 小鹿推
              a-button(@click="handleJoinJob" type="primary") 加入职位
              a-button(v-if="isSuperAdmin === 2" @click="handleAddBlackList(talent.talent.id)") 加入黑名单
            a-space(:size="16" v-if="talentBlackList === true")
              a-tag(color="red") 该人才在黑名单中，不允许推荐。了解详情请联系管理员。
              a-button(v-if="isSuperAdmin === 2" @click="handleRemoveBlackList(talent.talent.id)") 移除黑名单
      a-col(:span="8")
        div(v-if="talentId")
          TalentJobProgress(:talentId="talentId" ref="talentJobProgressInstance" :jobId="props.jobId")
        +talent-follow-up

  JobSelector(v-model:visible="status.showJobSelecor" :jobId="props.jobId" @select="handleSelectJob" :loading="status.joinJob" :multi="false")

  SmartDeerWork(
    :visible="status.showSmartDeerWorkModal",
    :talent="talent.talent",
    @cancel="()=>{status.showSmartDeerWorkModal = false}",
    @success="()=>{status.showSmartDeerWorkModal = false}"
  )

  a-drawer(v-model:open="drawer.show" :title="drawer.title" :destroyOnClose="true" :width="drawer.width" :bodyStyle="{padding: 0}")
    component(:is="drawer.component" v-bind="drawer.params" @close="drawer.show = false" @update="handleTalentUpdated")

  a-drawer(v-model:open="status.showChatBox" :destroyOnClose="true" :width="480" title="聊天记录" :bodyStyle="{padding: 0}")
    ChatBox(:from="chatUsers.from" :to="chatUsers.to")
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, shallowRef, watch, onMounted, computed, toRef } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { getTalentDetail, getTalentTaskPosition, getCompanyTalentFollowUp, createCompanyTalentFollowUp, deleteTalent, getTalentBlackList, updateTalentBlackListStatus } from '@/api/talent/talent'
import { Modal, message } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'
import { getPositionDetail } from '@/api/position'
import { getCustomerDetail } from '@/api/customer'
import { useUserStore } from '@/store/user.store'
import { addTalentToJobPool } from '@/api/job'
import JobSelector from '@/components/app/job-selector.vue'
import SmartDeerWork from '@/components/app/smart-deer-work.vue'
import { setSearchBury } from "@/api/track"
import ChatBox from '@/components/chat/chat-box.vue'
import SendEmail from '@/components/app/send-email.vue'
import tracker from '@/utils/tracker'

// 人才详情所需要的组件
import TalentDetail from '@/components/app/talent-detail.vue'
import TalentBasicInfoEdit from '@/components/app/talent-update.vue'
import TalentJobProgress from '@/components/app/talent-job-progress.vue'

import { getTalentResumeAttachments } from '@/api/resume'

const talentJobProgressInstance = ref()
const props = defineProps<{talentId:number, trackId?: string, jobId?: number}>()
const jobId = toRef(props, 'jobId')
const emit = defineEmits(['close'])
const router = useRouter()
const { talentId, trackId } = toRefs(props)
const userStore = useUserStore()
const route = useRoute()

const status = reactive({
  detailLoading: false,
  attachmentsLoading: false,
  positionLoading: false,
  showEditBasicModal: false,
  showEditDemandModal: false,
  initDict: false,
  updateTalentInfo: false,
  followUpLoading: false,
  showJobSelecor: false,
  joinJob: false,
  showSmartDeerWorkModal: false,
  showChatBox: false
})

const drawer = reactive({
  show: false,
  title: '',
  component: null as any,
  params: {} as any,
  width: 480
})

const talent = ref<any>({
  talent: {} as any,
  talentDemand: {} as any,
  talentEducations: [] as any[],
  talentExperiences: [] as any[],
  talentProjects: [] as any[],
  talentSkills: [] as any[],
  expirencesStr: [] as Expirence[]
})

const isSuperAdmin = ref(userStore.userType)

const talentBlackList = ref<any>(false)

const talentImUserId = ref<any>({
  imUserId: '' as string
})

const attachments = ref<any[]>([])

async function fetchTalentDetail(talentId: number) {
  status.detailLoading = true
  try {
    const detailRes = await getTalentDetail(talentId)
    const blackListRes = await getTalentBlackList(talentId)
    talentBlackList.value = blackListRes.data
    talent.value = detailRes.data

    talent.value.talentExperiences = talent.value.talentExperiences.sort((a:any,b:any) => {
      const aTime = dayjs(a.fromTime).valueOf()
      const bTime = dayjs(b.fromTime).valueOf()
      return bTime - aTime
    })

    talent.value.talentEducations = talent.value.talentEducations.sort((a:any,b:any) => {
      const aTime = dayjs(a.fromTime).valueOf()
      const bTime = dayjs(b.fromTime).valueOf()
      return bTime - aTime
    })

    if (detailRes.data.talent.sourceExtra !== undefined && detailRes.data.talent.sourceExtra) {
      talentImUserId.value = JSON.parse(detailRes.data.talent.sourceExtra)
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.detailLoading = false
}

async function fetchTalentAttachments(talentId: number) {
  status.attachmentsLoading = true
  try {
    const attachmentsRes = await getTalentResumeAttachments(talentId)
    attachments.value = attachmentsRes.data.sort((a:any, b:any) => {
      return dayjs(b.fileUploadTime).valueOf() - dayjs(a.fileUploadTime).valueOf()
    })
  } catch (err: any) {
    message.error(err.message)
  }
  status.attachmentsLoading = false
}

const pagination = reactive({
  current: 1,
  size: 4,
  total: 0,
})

const chatUsers = reactive({
  from: {},
  to: {}
})

const FROM_USER = ['production', 'test'].includes(import.meta.env.VITE_VUE_APP_BUILD_ENV) ? 'smart:0:smartdeer' : 'test:0:smartdeer'

async function handleAddBlackList(talentId:any) {
  status.detailLoading = true
  Modal.confirm({
    title: "确定将该人才加入黑名单？",
    content: "加入黑名单后，人才将不能被推荐到任何项目",
    onOk: async() => {
      await updateTalentBlackListStatus(talentId, 1)
      talentBlackList.value = true
    }
  })
  status.detailLoading = false
}

async function handleRemoveBlackList(talentId:any) {
  status.detailLoading = true
  try {
    const updateBlackList = await updateTalentBlackListStatus(talentId, 0)
    talentBlackList.value = false
  } catch (err: any) {
    message.error(err.message)
  }
  status.detailLoading = false
}

function handleSendEmail(talent:any) {
  drawer.show = true
  drawer.component = shallowRef(SendEmail)
  drawer.title = '发送邮件'
  drawer.params = { to: talent.talent.email, talentId: talent.talent.id }
  drawer.width = 640
  tracker.click('talent-detail-send-email-click', { talentId: talent.talent.id, page: route.name })
}

function handleChat(talent: any) {
  chatUsers.from = {
    imUserId: FROM_USER,
    nick: 'Jobs（乔布斯）',
    avatar: 'https://global-image.smartdeer.work/p/images/0x47b67a41f6324d2ea85c03270fe0064d.jpeg_median'
  }

  chatUsers.to = {
    imUserId: talentImUserId.value.imUserId,
    nick: talent.talent.realName,
    avatar: talent.talent.photoUrl
  }

  status.showChatBox = true
  tracker.click('talent-detail-chat-click', { talentId: talent.talent.id, page: route.name })
}

function getUsers(position: any, userCategory: 'pm' | 'ca') {
  const users: any[] = []
  position.jobRequirement.properties?.forEach((item: any, index: number) => {
    if (item.key.toLowerCase() === userCategory) {
      users.push(item.valueName)
    }
  })
  return users.join(',')
}

async function handleJoinJob() {
  status.showJobSelecor = true
}

async function handleSelectJob(jobs: any[]) {
  const job = jobs[0]
  status.showJobSelecor = false
  status.joinJob = true
  // emit('close')
  // router.push(`/talent/${talentId.value}/job/${job.id}/report/create?trackId=${trackId?.value}`)
  try {
    if (props.trackId) {
      setSearchBury({
        action: 2,
        track_id: props.trackId
      })
    }

    const talentId = talent.value.talent.id
    const talentName = talent.value.talent.realName
    const jobName = job.processName
    const taskRes = await addTalentToJobPool(job.id, talentId)
    talentJobProgressInstance.value.refresh()
    message.success(`人才[ ${talentName} ]成功加入项目[ ${jobName} ]!`)

    // console.log(taskRes)

    // 这里创建task并没有返回processInstanceId,所以暂时不跳转到推荐报告页面
    // TODO: 后续时间
    // Modal.confirm({
    //   title:'加入职位成功',
    //   content: '已成功加入职位，是否填写推荐报告？',
    //   okText: '编辑推荐报告',
    //   cancelText: '暂不编辑',
    //   onOk: ()=>{
    //     router.push({
    //       path: `/talent/${talentId}/report`,
    //       query: { processInstanceId: taskRes.data?.processInstanceId }
    //     })
    //   },
    // })
    
  } catch (err: any) {
    message.error(err.message)
  }
  status.joinJob = false
}

interface Expirence {
  exp: string
  timeRange: string
  type: 'work' | 'education'
}

const followUpTab = ref(1)
const followForm = reactive({ comment: '' })
const followups = ref<any[]>([])
const followUpPagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0
})
const followUpUser = ref<number | null>(0)

async function fetchTalentFollowUp(talentId: number, pagination: any, userId: number | null) {
  status.followUpLoading = true
  try {
    const params = userId ? { current: pagination.current, size: pagination.pageSize, assignee: userId, jobRequirementId: 0 } : { current: pagination.current, size: pagination.pageSize, assignee: userId, jobRequirementId: 0 }
    const res = await getCompanyTalentFollowUp(talentId, params)
    followups.value = res.data.followups
    followUpPagination.total = res.data.total
    followForm.comment = ''
  } catch (err: any) {
    message.error(err.message)
  }
  status.followUpLoading = false
}

async function handleFollowUpTabChange(tabIndex: number) {
  // 如果点击相同，则不进行搜索
  if (tabIndex === followUpTab.value) return
  if (tabIndex === 2) followUpUser.value = userStore.id
  else followUpUser.value = null
  followUpTab.value = tabIndex
  fetchTalentFollowUp(talentId?.value, followUpPagination, followUpUser.value)
}

async function handleFollowUpPageChange(page: number) {
  followUpPagination.current = page
  fetchTalentFollowUp(talentId?.value, followUpPagination, followUpUser.value)
}

async function handleNewFollowUp() {
  status.followUpLoading = true
  try {
    if (!followForm.comment) throw new Error('跟进记录不能为空')
    const res = await createCompanyTalentFollowUp(talentId?.value, followForm)
    fetchTalentFollowUp(talentId?.value, followUpPagination, followUpUser.value)
    message.success('添加跟进记录成功！')
  } catch (err: any) {
    message.error(err.message)
  }
  status.followUpLoading = false
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

function handleTalentUpdated() {
  drawer.show = false
  fetchTalentDetail(talentId?.value)
}

function handleTalentUpdateType(type: string) {
  drawer.show = true
  drawer.width = 480
  drawer.component = shallowRef(TalentBasicInfoEdit)
  drawer.title = '编辑人才基本信息'
  drawer.params = { talentId: talent.value.talent.id, section: [type] }
}

const hasImUserId = computed(() => {
  try {
    const jsonStr = talent.value?.talent.sourceExtra
    const imUserId = JSON.parse(jsonStr)?.imUserId
    return imUserId !== undefined
  } catch (err: any) {
    return false
  }
})

watch(() => props.talentId, (value: any) => {
  talentId.value = value
  fetchTalentDetail(talentId!.value)
  fetchTalentAttachments(talentId?.value)
  fetchTalentFollowUp(talentId!.value, followUpPagination, null)
  tracker.event('talent-detail-modal-show', { talentId: value, page: route.name })
})

onMounted(async () => {
  fetchTalentDetail(talentId?.value)
  fetchTalentAttachments(talentId?.value)
  fetchTalentFollowUp(talentId?.value, followUpPagination, null)
  tracker.event('talent-detail-modal-show', { talentId: talentId?.value, page: route.name })
})

onBeforeRouteLeave(()=>{
  emit('close')
})

</script>

<style lang="scss" scoped>
.talent-detail-page {
  .talent-detail-container {
    background-color: #FFF;
    border-radius: 8px;
    overflow: hidden;
  }
}

.talent-action {
  margin-top: 40px;
  position: sticky;
  bottom: 0;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__head {
    padding: 24px 24px 0 32px;

    h4 {
      font-size: 20px;
      margin-bottom: 24px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }

  &__empty {
    padding-bottom: 24px;
  }
}

.follow-up {
  background-color: #fff;
  border-radius: 4px;

  .follow-up-tab {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }

    :deep(.ant-tabs-tab) {
      font-size: 16px;
    }
  }

  &__head {
    padding: 0 24px;
    position: relative;

    h4 {
      font-size: 20px;
      position: relative;
      line-height: 24px;
      color: #ddd;
      margin: 0;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }

  &__textarea {
    padding: 16px 24px 24px;
    text-align: right;
  }

  &__textarea-input {
    background-color: #f9f9f9;
    border: none;
    border-radius: 4px;
    outline: none;
    margin-bottom: 16px;
  }

  &__comments {
    padding: 0 24px;

    .comments-item {
      padding: 16px 0;
      border-top: 1px solid #f0f0f0;

      &__comment {
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      &__name {
        color: #999;
      }

      &__date {
        color: #999;
      }
    }
  }

  .comment-empty {
    padding-bottom: 24px;
  }

  .comment-pagination {
    padding: 16px 0;
    text-align: center;
  }
}
</style>