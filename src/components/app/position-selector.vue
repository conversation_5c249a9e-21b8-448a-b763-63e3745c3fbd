<template lang="pug">
mixin position-selector
  .position-selector
    .position-selector-left
      .position-selector-search
        a-input(@change="debouncedFetchPosition" v-model:value="searchKeyword")
          template(#prefix)
            SearchOutlined
      .position-selector-table
        a-table(
          :loading="status.loading"
          :row-selection="{ selectedRowKeys:selectedPositionKeys, onSelect:onSelectChange, hideSelectAll:!multi }"
          :columns="columnConfig",
          :data-source="positionList",
          :pagination="pagination",
          @change="handlePageChange",
          size="middle"
        )
          template(#bodyCell="{ column, record }")
            //- 头像部分
            template(v-if="column.dataIndex === 'position'")
              a-popover(@visibleChange="() => {getPositionDetail(record)}" :mouseEnterDelay=".5" title="职位详情" placement="right")
                .position-item__title {{ record.positionTitle }}
                .position-item__customer 公司: {{ record.customer?.customerFullName }}
                .position-item__info
                  a-space(:size="12")
                    span {{getPositionType(record)}}
                    span 工作地: {{record.areaStr ? record.areaStr : '未知'}}
                    span 薪资: {{formatSalary(record)}}
                template(#content)
                  .position-detail__summary
                    .position-item__title {{ record.positionTitle }}
                    a-space(:size="12")
                      span 工作地: {{record.areaStr ? record.areaStr : '未知'}}
                      span 薪资: {{formatSalary(record)}}
                  .position-detail__tags
                    a-space(:size="12")
                      a-tag(v-for="(item, index) in record.tags") {{item.name}}
                  .position-detail__company(v-if="record.customer")
                    a-avatar.position-detail__company-logo(:size="48")
                    .position-detail__company-name  {{ record.customer.customerFullName }}
                    .position-detail__company-desc
                      a-space
                        span 规模: {{record.customer.companyScaleStr}}
                        span {{record.customer.companyTypeStr}}
                        span 行业: {{record.customer.industryStr}}
                  .position-detail__info
                    a-row(:gutter="[16,16]")
                      a-col(:span="12") HC数量: {{record.jobRequirementCount}}
                      a-col(:span="12") 工作地点: {{record.areaStr}}
                      a-col(:span="12") 职能: {{record.functionStr}}
                      a-col(:span="24") 对标公司: {{record.targetFirms}}

    .position-selector-right
      .position-select-selected
        .position-item(v-for="(position, index) in selectedPositions", :key="index")
          .position-detail__summary
            .position-item__title {{ position.positionTitle }}
            .position-item__customer {{position.customer.customerFullName}}
            a-space(:size="12")
              span 工作地: {{position.areaStr ? position.areaStr : '未知'}}
              span 薪资: {{formatSalary(position)}}
          .position-item__action
            a-button(type="link" @click="() => { removePosition(position) }") 移除

a-modal(
  v-model:open="showModal",
  title="选择职位",
  :width="950",
  :body-style="{ padding: '12px' }",
  :ok-button-props="{ disabled: selectedPositions.length === 0 }"
  :destroyOnClose="true",
  @ok="handlePositionListConfirm"
)
  +position-selector

</template>
  
<script lang="ts" setup>
// 需要做一个单选还是多远的参数

import { reactive, ref, toRef, toRefs, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getPositionList } from '@/api/position'
import { debounce } from '@/utils/util'
import { getCustomerDetail } from '@/api/customer'
import { formatSalary } from '@/utils/salary-helper'

interface Staff {
  id: number,
  name: string,
  department: string[],
  gender: string,
}

const props = defineProps<{ visible: boolean, multi: boolean, selected: any[]}>()
const emit = defineEmits(['update:visible', 'select'])

const visible = toRef(props, 'visible')
const multi = toRef(props, 'multi')
const showModal = ref(false)
const searchKeyword = ref('')
showModal.value = visible.value

const columnConfig = [
  { title: '职位', key: 'name', dataIndex: 'position' },
]

const pagination = reactive({
  pageSize: 10,
  total: 0,
  current: 1,
  showSizeChanger: false
})

const selectedPositionKeys = ref<any[]>([])
const selectedPositions = ref<any[]>([])
const status = reactive({
  loading: false
})

watch(visible, (value, oldValue) => {
  if (value !== oldValue) showModal.value = value
  if (value === false) reset()
})

// 向外抛出当前modal的状态
watch(showModal, (value, oldValue) => {
  if (value === false) reset()
  emit('update:visible', value)
})

const positionMap = new Map()

function reset() {
  selectedPositionKeys.value = []
  selectedPositions.value = []
  positionList.value = []
  pagination.current = 1
  pagination.total = 0
}

function getPositionType(position: any) {
  switch (position.type) {
    case 0: return '实习';
    case 1: return '校招';
    case 3: return '社招';
  }
}

function onSelectChange(record:any, selected:any) {
  if(multi.value) {
    if (selectedPositionKeys.value.includes(record.id)) {
      const index = selectedPositionKeys.value.indexOf(record.id)
      selectedPositionKeys.value.splice(index, 1)
      selectedPositions.value.splice(index, 1)
    } else {
      selectedPositionKeys.value.push(record.id)
      selectedPositions.value.push(record)
    }
  } else { 
    if (selectedPositionKeys.value.includes(record.id)) {
      selectedPositionKeys.value = []
      selectedPositions.value = []
    } else {
      selectedPositionKeys.value = [record.id]
      selectedPositions.value = [record]
    }
  }
}

function removePosition(positionKey: any) {
  const index = selectedPositionKeys.value.indexOf(positionKey)
  selectedPositionKeys.value.splice(index, 1)
  selectedPositions.value.splice(index, 1)
}

async function fetchCustomerDetail(position: any) {
  try {
    const res = await getCustomerDetail(position.customerId)
    position.customer = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function getPositionDetail(position: any) {
  if (!position.customer) {
    fetchCustomerDetail(position)
  }
}

const positionList = ref<any[]>()

interface fetchPositionListParams {
  keyword: string
  current: number
  size: number
}

async function fetchPositionList() {
  const keyword = searchKeyword.value
  status.loading = true
  try {
    positionMap.clear()
    const res = await getPositionList({ positionTitle: keyword, current: pagination.current, size: pagination.pageSize, type: null })
    positionList.value = res.data.positions.map((item: any) => {
      const postionTemp = Object.assign({}, item, { key: item.id })
      positionMap.set(item.id, postionTemp)
      return postionTemp
    })
    pagination.total = res.data.total
  } catch (err: any) {

  }
  status.loading = false
}

const debouncedFetchPosition = debounce(fetchPositionList)

function handlePageChange(page: any) {
  pagination.current = page.current
  fetchPositionList()
}

function handlePositionListConfirm() {
  emit('select', selectedPositions.value)
}

function getAnnualSalary(position: any) {
  const start = (position.salaryFrom * position.salaryCalMonth * 1000) / 10000
  const to = (position.salaryTo * position.salaryCalMonth * 1000) / 10000
  return `${start}-${to}万`
}

</script>
  
<style lang="scss" scoped>
.position-selector {
  border-radius: 6px;
  display: flex;
  border: 1px solid #e8e8e8;
  height: 80vh;

  &-left {
    flex: 1 1 auto;
    width: 50%;
    overflow-y: scroll;
  }

  &-right {
    flex: 1 1 auto;
    width: 50%;
    border-left: 1px solid #e8e8e8;
  }

  &-search {
    padding: 12px;
  }
}

.position-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;

  &__title {
    font-weight: bold;
    word-break: break-all;
  }

  &__customer {
    // color: #999;
  }

  &__info {
    color: #999;
  }

  &__action {
    width: 40px;
    flex: 0 0 auto;
  }
}

.position-detail {
  &__company {
    padding: 12px 0;
    padding-left: 60px;
    position: relative;
    padding-bottom: 12px;
    border: 1px solid #e8e8e8;
    border-left: none;
    border-right: none;
    margin-top: 12px;
  }

  &__company-logo {
    position: absolute;
    left: 0px;
  }

  &__company-name {
    // font-size: 16px;
    font-weight: bold;
  }

  &__detail {
    padding-top: 12px;
  }

  &__summary {
    flex: 1 1 auto;
  }

  &__tags {
    padding-top: 12px;
  }

  &__info {
    padding-top: 12px;
  }
}
</style> 
  