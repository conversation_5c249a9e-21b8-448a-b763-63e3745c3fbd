<template lang="pug">

mixin talent-detail-main
  info-section(title="个人评价")
    section.detail-section
      .section-body
        p.pre-present {{talent.talent.selfEvaluation}}

  info-section(title="需求信息" editable @edit="emit('update', 'demand')")
    section.detail-section.talent-demand
      .section-body
        a-row(:gutter="[16, 16]")
          a-col(:span="3") 意向城市
          a-col(:span="9") {{ talent.talentDemand.areaDemandStr?.map(item => item.areaName).join(' , ') || '未知' }}
          a-col(:span="3") 求职状态
          a-col(:span="9") {{getEmployeeStatus(talent.talent.employeeStatus)}}
        a-row(:gutter="[16, 16]")
          a-col(:span="3") 意向行业
          a-col(:span="9") {{ talent.talentDemand.currentIndustryTypesStr?.map(item=> item.industryName).join(' , ') || '未知' }}
          a-col(:span="3") 期望薪资
          a-col(:span="9") {{talent.talentDemand.targetTotalCash || '未知'}}
      .talent-demand__action
        span(@click="()=>{ status.showEditDemandModal = true }") 编辑

  info-section(title="教育经历" editable @edit="emit('update', 'education')")
    section.detail-section
      .section-body
        a-row.education-item(v-for="(item ,index) in talent.talentEducations")
          a-col.edutaion-item(:span="6") {{item.fromDate}} - {{item.toDate}}
          a-col.education-item__name(:span="18")
            span {{ item.schoolName }} {{item.major ? ` · ${item.major}` : ''}} {{item.degreeStr ? ` · ${item.degreeStr}` : ''}}

  info-section(title="工作经历" editable @edit="emit('update', 'experience')")
    section.detail-section
      .section-body
        .experience-item(v-for="(item, index) in talent.talentExperiences")
          .experience-item__company
            strong {{item.companyName}}
            em {{item.position}}
            span {{item.fromDate}} - {{item.toDate}}
          .experience-item__desc {{item.duties}}
          .experience-item__desc {{item.jobDesc}}

  info-section(title="项目经历" @edit="emit('update', 'project')")
    section.detail-section
      .section-body
        .project-item(v-for="(item, index) in talent.talentProjects")
          .project-item__basic
            strong {{ item.projectName }}
            span {{item.fromDate}}-{{item.toDate}}
          .project-item__desc {{item.projectDesc}}

  info-section(title="技能信息" @edit="emit('update', 'skill')")
    section.detail-section(v-if="talent.talentSkills.length > 0")
      .section-body
        .skill-item {{ talent.talentSkills.map((item:any) => ` ${item.skillType} `).join(',') }}

mixin talent-detail-summary
  info-section(editable @edit="() => emit('update', 'basic')")
    section.detail-section.talent-base-info
      .talent-base-info__avatar
        a-avatar(:size="96" shape="square" :src="talent.talent.photo") {{talent.talent.realName}}
      .talent-base-info__content
        .talent-base-info__basic
          strong {{talent.talent.realName}}
          span {{ talentGender }} / {{getAgeFromBirthday(talent.talent.birthday)}} 
          span ID: {{ talent.talent.id }}
        .talent-base-info__contact
          a-space(:size="24")
            div(v-if="talent.talent.mobileNumber") 
              MobileOutlined(style="margin-right: 4px; color: #999;")
              span {{talent.talent.mobileNumber}}
            div(v-if="talent.talent.email")
              MailOutlined(style="margin-right: 4px; color: #999;")
              span {{talent.talent.email}}
        .talent-base-info__expirence
          a-space(:size="24")
            span 创建: {{ talent.talent.createdBy !== 0 ? talent.talent.companyUser?.realName || talent.talent.createBy : '系统创建' }} [{{ formatDate(talent.talent.createTime) }}]
            div(v-if="lastUpdateUserAndTime") 
              span 最后修改: {{ lastUpdateUserAndTime.companyUser?.realName  }}
              span(v-if="lastUpdateUserAndTime.updateTime") [{{ formatDate(lastUpdateUserAndTime.updateTime) }}]

.talent-detail-component

  +talent-detail-summary

  a-tabs(v-model:activeKey="activeTab")
    a-tab-pane(key="detail" tab="人才详情")
      +talent-detail-main
    a-tab-pane(key="resume" tab="附件简历")
      .file-previewer-container
        FileReviewer(:url="resumeFile" :mime="resumeFileMime")

    template(#renderTabBar="")
      .talent-detail-tab
        h4(@click="()=>{activeTab = 'detail'}" :class="{active: activeTab === 'detail'}") 人才详情
        a-dropdown(v-if="attachments.length")
          h4(:class="{active: activeTab === 'resume'}") 
            span 附件简历 
            DownOutlined
          template(#overlay)
            a-menu
              a-menu-item(v-for="(item, index) in attachments" @click="()=>handleSelectResume(item)" :key="index")
                .resume-file-item(:class="{active: resumeFile == item.fileAbsolutePath}")
                  .resume-file-name 简历{{index + 1}}
                  .resume-file-time {{formatDate(item.fileUploadTime)}}
        span(v-else) 暂无附件简历
</template>

<script lang="ts" setup>
import { DownOutlined } from '@ant-design/icons-vue'
import { getAgeFromBirthday, getShortDate } from '@/utils/string'
import { computed, onMounted, reactive, ref, watch, shallowRef, toRef } from 'vue'
import { MobileOutlined, MailOutlined } from '@ant-design/icons-vue'
import { dictionary } from '@/api/dictionary'
import InfoSection from '@/components/info-section/info-section.vue'
import FileReviewer from '@/components/file-previewer/file-previewer.vue'
import dayjs from 'dayjs'

const props = defineProps(['talent', 'attachments'])
const emit = defineEmits(['update'])

const talent = toRef(props, 'talent')
const attachments = toRef(props, 'attachments')
const activeTab = ref('detail')
const resumeFile = ref<string>('')
const resumeFileMime = ref<string>('')

interface Expirence {
  exp: string
  timeRange: string
  type: 'work' | 'education'
}

const status = reactive({
  detailLoading: false,
  positionLoading: false,
  showEditBasicModal: false,
  showEditDemandModal: false,
  initDict: false,
  updateTalentInfo: false,
})

function getEmployeeStatus(status: number) {
  const statusItem = dict.employeeStatusMap.get(status)
  if (statusItem) return statusItem.type
  else return '未知'
}

const dict = reactive({
  employeeStatusMap: new Map(),
  employeeStatus: [] as any[],
})

async function handleSelectResume(resume: any) {
  activeTab.value = 'resume'
  resumeFile.value = resume.fileAbsolutePath
  resumeFileMime.value = resume.fileMimeType
}

async function initDict() {
  const employeeStatusDictRes = await dictionary.getEmployeeStatus()
  dict.employeeStatus = employeeStatusDictRes.data.map((item: any, index: number) => {
    dict.employeeStatusMap.set(item.id, item)
    return { label: item.type, value: item.id }
  })
}

const talentGender = computed(() => {
  switch (talent.value.talent.gender) {
    case 1:
      return '男'
    case 2:
      return '女'
    case 3:
      return '未知'
  }
})

function formatDate(time: number) {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const lastUpdateUserAndTime = computed(()=>{
  const userLatestTime:any[] = []
  if (!talent.value.talentOwners) return {}
  talent.value.talentOwners.forEach((owner: any) => {
    const max = Math.max(owner.contactUpdateTime || 0, owner.experienceUpdateTime || 0, owner.demandUpdateTime || 0)
    userLatestTime.push({ companyUser: owner.companyUser, updateTime: max })
  })
  return userLatestTime.sort((a, b) => b.updateTime - a.updateTime)[0]
})

watch(attachments, (value) => {
  if (activeTab.value === 'resume' && value.length) {
    const resume = value[0]
    resumeFile.value = resume.fileAbsolutePath
    resumeFileMime.value = resume.fileMimeType
  }
})

onMounted(() => {
  initDict()
})

</script>

<style lang="scss" scoped>
.talent-detail-component {
  background-color: #fff;
  border-radius: 8px;

  .talent-demand {
    border: 1px solid #fff;
    position: relative;
    transition: all .2s;

    &__action {
      color: #FF9111;
      display: none;
      position: absolute;
      right: 24px;
      top: 24px;

      span {
        cursor: pointer;
      }
    }
  }

  .file-previewer-container {
    padding: 24px;
  }

  .talent-base-info {
    display: flex;

    // &:hover {
    //   border: 1px solid #FF9111;
    //   border-top-left-radius: 8px;
    //   border-top-right-radius: 8px;
    //   transition: all .2s;

    //   .talent-base-info__action {
    //     display: block;
    //     position: absolute;
    //     right: 24px;
    //     top: 24px;
    //   }
    // }

    &__action {
      color: #FF9111;
      display: none;

      span {
        cursor: pointer;
      }
    }

    &__avatar {
      flex: 0 0 auto;
    }

    &__content {
      flex: 1 1 auto;
      padding-left: 24px;
      line-height: 32px;
    }

    &__basic {
      strong {
        font-size: 18px;
      }

      span {
        margin-left: 16px;
      }
    }

    &__contact {
      .spliter {
        color: #f0f0f0;
        display: inline;
        margin: 0 16px;
      }
    }
  }

}

.section-spliter {
  display: block;
  border-bottom: 1px solid #f0f0f0;
}

section.detail-section {

  .pre-present {
    display: block;
    unicode-bidi: embed;
    white-space: pre-wrap;
  }

  p {
    margin-bottom: 0;
  }

  &:last-child {
    border-bottom: none;
  }

  .section-head {
    h4 {
      font-size: 20px;
      margin-bottom: 24px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  .section-body {
    line-height: 24px;
  }
}

.talent-detail-info {
  background-color: #fff;
  border-radius: 8px;

  .talent-demand {
    border: 1px solid #fff;
    position: relative;
    transition: all .2s;

    &:hover {
      border: 1px solid #FF9111;
      transition: all .2s;

      .talent-demand__action {
        display: block;
      }
    }

    &__action {
      color: #FF9111;
      display: none;
      position: absolute;
      right: 24px;
      top: 24px;

      span {
        cursor: pointer;
      }
    }
  }

  .talent-base-info {
    padding: 24px;
    display: flex;
    border: 1px solid #fff;
    transition: all .2s;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    position: relative;

    &:hover {
      border: 1px solid #FF9111;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      transition: all .2s;

      .talent-base-info__action {
        display: block;
        position: absolute;
        right: 24px;
        top: 24px;
      }
    }

    &__action {
      color: #FF9111;
      display: none;

      span {
        cursor: pointer;
      }
    }

    &__avatar {
      flex: 0 0 auto;
    }

    &__content {
      flex: 1 1 auto;
      padding-left: 24px;
      line-height: 32px;
    }

    &__basic {
      strong {
        font-size: 18px;
        margin-right: 16px;
      }
    }

    &__contact {
      .spliter {
        color: #f0f0f0;
        display: inline;
        margin: 0 16px;
      }
    }
  }

}

.education-item {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.experience-item {
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }

  &__company {
    display: flex;
    justify-items: center;
    margin-bottom: 8px;

    strong {
      font-size: 16px;
      margin-right: 24px;
    }

    em {
      margin-right: 24px;
      font-weight: bold;
      color: #333;
    }

    span {
      color: #999;
    }
  }

  &__desc {
    line-height: 24px;
    display: block;
    unicode-bidi: embed;
    white-space: pre-wrap;
  }
}

.project-item {
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 0;
  }

  &__basic {
    display: flex;
    justify-items: center;
    margin-bottom: 12px;

    strong {
      font-size: 16px;
      margin-right: 24px;
    }

    span {
      color: #999;
    }
  }

  &__desc {
    line-height: 24px;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__head {
    padding: 24px 24px 0 32px;

    h4 {
      font-size: 20px;
      margin-bottom: 24px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }

  &__empty {
    padding-bottom: 24px;
  }
}

.expirence-item {
  padding-left: 24px;
  padding-right: 24px;
  position: relative;
  display: flex;

  &__icon {
    position: absolute;
    left: 0;
    top: 8px;

    img {
      width: 16px;
      display: block;
    }
  }

  &__exp {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__time-range {
    width: 150px;
    padding-left: 8px;
  }
}


.follow-up {
  background-color: #fff;
  border-radius: 4px;
  margin-top: 16px;

  &__head {
    padding: 24px 24px 0 32px;
    position: relative;

    &:before {
      content: '';
      display: block;
      width: 4px;
      background-color: #FF9111;
      height: 30px;
      position: absolute;
      left: 16px;
      top: 21px;
    }

    h4 {
      font-size: 20px;
      position: relative;
      line-height: 24px;
      color: #ddd;
      margin: 0;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }

  &__textarea {
    padding: 16px 24px 24px;
    text-align: right;
  }

  &__textarea-input {
    background-color: #f9f9f9;
    border: none;
    border-radius: 4px;
    outline: none;
    margin-bottom: 16px;
  }

  &__comments {
    .comments-item {
      padding: 24px;
      border-top: 1px solid #f0f0f0;

      &__comment {
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      &__name {
        color: #999;
      }

      &__date {
        color: #999;
      }
    }
  }

  .comment-empty {
    padding-bottom: 24px;
  }

  .comment-pagination {
    padding: 16px 0;
    text-align: center;
  }
}

.talent-detail-tab {
  padding: 16px 24px;

  h4 {
    font-size: 18px;
    display: inline;
    margin-right: 24px;
    color: inherit;
    cursor: pointer;
    position: relative;

    &.active {
      color: #FF9111;

      &::after {
        content: '';
        position: absolute;
        height: 2px;
        border-radius: 1px;
        width: 100%;
        background-color: #FF9111;
        left: 0;
        bottom: -21px;
      }
    }
  }

  border-bottom: 1px solid #f0f0f0;
}


.resume-file-item {
  .resume-file-name {
    font-weight: bold;
  }

  .resume-file-time {
    color: #999;
  }

  &.active {
    .resume-file-name {
      color: #FF9111;
    }
  }
}
</style>