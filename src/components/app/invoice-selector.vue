<template lang="pug">
a-modal(
  :open="open" 
  @cancel="()=> emit('update:open', false)"
  @ok="handleSelect"
  style="width: 90%; overflow: auto;"
  title="选择票据"
)
  .filters-section
    a-row(:gutter="[12, 12]")
      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            v-model:value="searchParams.invoiceStatus" 
            :options="options.invoiceStatus" 
            placeholder="审核状态"
            allow-clear
            @change="() => fetchInvoiceList()"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-range-picker(
            style="width: 100%"
            v-model:value="dataRange.applayDateRange"
            :presets="options.ranges"
            :placeholder="['申请时间','申请时间']"
            @change="() => handleDateRangeChange('applayDateRange')"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-range-picker(
            style="width: 100%"
            v-model:value="dataRange.verifyDateRange"
            :placeholder="['审核时间','审核时间']"
            :presets="options.ranges"
            @change="() => handleDateRangeChange('verifyDateRange')"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            placeholder="票据类型"
            v-model:value="searchParams.invoiceType" 
            :options="options.invoiceType"
            allow-clear
            @change="(value:any) => { handleFilterChange('invoiceType', value)}"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="候选人姓名"
            v-model:value="searchParams.talentRealName"
            @search="(value:any) => { handleFilterChange('talentName', value) }"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="项目名称"
            v-model:value="searchParams.positionTitle"
            @search="(value:any) => { handleFilterChange('projectName', value) }"
          )

  .invoice-list-section

    a-row
      a-col(:span="24")
        a-table(
          :columns="invoiceListColumn"
          :data-source="invoiceList"
          :pagination="pagination"
          :loading="status.tableLoading"
          :rowSelection="rowSelection"
          :scroll="{ x: 1000 }"
          @change="handlePageChange"
          size="small"
        )
          template(#bodyCell="{ text, record, index, column }")
            template(v-if="column.key === 'offer'")
              template(v-for="(offer, index) in record.financeInvoiceDetails" :key="offer.id")
                a-space(:size="12")
                  .offer-info
                    label 职位: 
                    span {{ offer.positionTitle }}
                  .offer-info
                    label 姓名: 
                    span {{ offer.talentRealName }}
                  .offer-info
                    label 金额: 
                    span {{ (offer.grandTotal / 100).toFixed(2) }} {{ record.currencyTypeKey }}
                  .offer-info
                    label 待收: 
                    span {{ (offer.remainingAmount / 100).toFixed(2) }} {{ record.currencyTypeKey }}

            template(v-if="column.key === 'invoiceStatusStr'")
              a-tag(v-if="record.invoiceStatus === 0" color="blue") 待审核
              a-tag(v-if="record.invoiceStatus === 1" color="green") 通过
              a-tag(v-if="record.invoiceStatus === 2" color="red") 拒绝

            template(v-if="column.key === 'grandTotal'")
              span {{ (record.grandTotal / 100).toFixed(2) }} {{ record.currencyTypeKey }}
  
</template>

<script lang="ts" setup>
import { getInvoiceList } from '@/api/finance'
import { ref, toRef, watch, onMounted, reactive } from 'vue'
import { } from 'ant-design-vue'
import FilterItem from '@/components/ui/filter-item.vue'
import { getDateRanges } from '@/utils/util';
import { getCustomerList } from '@/api/customer'
import { useUserStore } from '@/store/user.store';
import dayjs from 'dayjs';

const userStore = useUserStore()

const options = reactive({
  ranges: getDateRanges(),
  customer: [] as any[],
  paymentMethod: [] as any[],
  entityList: [] as any[],
  invoiceStatus: [
    { label: '未审核', value: 0 },
    { label: '通过', value: 1 },
    { label: '拒绝', value: 2 },
  ],
  invoiceType: [
    { label: 'INVOICE', value: 1 },
    { label: '普通发票', value: 2 },
    { label: '增值税专用发票', value: 3 },
  ]
})

const searchParams = reactive({
  size: 0,
  current: 0,
  companyUserIds: [] as number[],
  customerIds: [] as number[],
  invoiceStatus: null,
  invoiceType: null,
  positionTitle: '',
  endDate: '',
  startDate: '',
  talentRealName: '',
  verifyEndDate: '',
  verifyStartDate: '',
  finaceAccountId: null
})

const status = reactive({
  loading: false,
  tableLoading: false,
})

const filter = reactive({

})

function handleFilterChange(key: string, value: any) {
  if (key === 'invoiceStatus') {
    searchParams.invoiceStatus = value
  } else if (key === 'customerId') {
    if (value) searchParams.customerIds = [value]
    else searchParams.customerIds = []
  } else if (key === 'invoiceType') {
    searchParams.invoiceType = value
  } else if (key === 'talentName') {
    searchParams.talentRealName = value
  } else if (key === 'projectName') {
    searchParams.positionTitle = value
  } else if (key === 'financeAccountId') {
    searchParams.finaceAccountId = value
  }
  pagination.current = 1
  fetchInvoiceList()
}

function handleDateRangeChange(key: string) {
  if (key === 'applayDateRange') {
    if (!dataRange.applayDateRange) {
      searchParams.startDate = ''
      searchParams.endDate = ''
    } else {
      searchParams.startDate = dayjs(dataRange.applayDateRange[0]).format('YYYY-MM-DD')
      searchParams.endDate = dayjs(dataRange.applayDateRange[1]).format('YYYY-MM-DD')
    }
  } else if (key === 'verifyDateRange') {
    if (!dataRange.verifyDateRange) {
      searchParams.verifyStartDate = ''
      searchParams.verifyEndDate = ''
    } else {
      searchParams.verifyStartDate = dayjs(dataRange.verifyDateRange[0]).format('YYYY-MM-DD')
      searchParams.verifyEndDate = dayjs(dataRange.verifyDateRange[1]).format('YYYY-MM-DD')
    }
  }
  searchParams.current = 1
  fetchInvoiceList()
}


const selectedKeys = ref<string[]>([])

type InvoiceSelectorParams = {
  companyUserIds: number[],
  finaceAccount: number,
}
const props = defineProps<{ open: boolean, params: InvoiceSelectorParams}>()
const open = toRef(props, 'open')
const selectorParams = toRef(props, 'params')
const emit = defineEmits(['update:open', 'select'])

const invoiceList = ref<any[]>([])

const invoiceListColumn = [
  { title: "审核状态", dataIndex: "invoiceStatusStr", key: "invoiceStatusStr", fixed: true, width: 100 },
  { title: '客户', dataIndex: ['customer', 'customerFullName'], key: 'customer', width: 200 },
  { title: "发票抬头", dataIndex: "customerName", key: "customerName", width: 200 },
  { title: "开票主体", dataIndex: "vendorName", key: "vendorName", width: 200 },
  { title: "开票金额", dataIndex: "grandTotal", key: "grandTotal", width: 140 },
  { title: "发起人", dataIndex: "applyUserRealName", key: "applyUserRealName", width: 100 },
  { title: "OFFER 信息", key: "offer", width: 720 },
]

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const selectedInvoice = reactive({
  keys: [] as string[],
  list: [] as any[]
})

const fetchInvoiceList = async () => {
  status.tableLoading = true
  try {

    // 这里用户没有填写的参数，需要不传给后端。所以这里需要重新组装参数
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      companyUserIds: selectorParams.value.companyUserIds,
      customerIds: searchParams.customerIds.length === 0 ? undefined : searchParams.customerIds,
      invoiceStatus: searchParams.invoiceStatus === null ? undefined : searchParams.invoiceStatus,
      invoiceType: searchParams.invoiceType === null ? undefined : searchParams.invoiceType,
      positionTitle: searchParams.positionTitle === '' ? undefined : searchParams.positionTitle,
      endDate: searchParams.endDate === '' ? undefined : searchParams.endDate,
      startDate: searchParams.startDate === '' ? undefined : searchParams.startDate,
      talentRealName: searchParams.talentRealName === '' ? undefined : searchParams.talentRealName,
      verifyEndDate: searchParams.verifyEndDate === '' ? undefined : searchParams.verifyEndDate,
      verifyStartDate: searchParams.verifyStartDate === '' ? undefined : searchParams.verifyStartDate,
    }
    const res = await getInvoiceList(params)
    const { financeInvoices, total } = res.data
    const list = financeInvoices.map((item: any) => {
      return {
        ...item,
        key: item.id
      }
    })
    // 清空原来选择的行
    selectedInvoice.keys = []
    selectedInvoice.list = []

    invoiceList.value = list
    pagination.total = total
    status.tableLoading = false
  } catch (err) {
    status.tableLoading = false
  }
}

const dataRange = reactive({
  applayDateRange: [],
  verifyDateRange: [],
})

const fetchCustomerList = async (name: string = '') => {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    isSearchProject: true,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })

  options.customer = list
}

async function handlePageChange(pageInfo: any) {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  await fetchInvoiceList()
}

async function removeSelected(record: any) {
  const index = selectedInvoiceKeys.value.findIndex((item) => item === record.key)
  selectedInvoiceKeys.value.splice(index, 1)
  selectedInvoiceList.value.splice(index, 1)
}

const selectedInvoiceKeys = ref<string[]>([])
const selectedInvoiceList = ref<any[]>([])

const rowSelection = {
  getCheckboxProps: (record: any) => {
    const verified = record.invoiceStatus == 1
    const sameCompany = record.companyFinanceAccount.id === selectorParams.value.finaceAccount
    const totalClaimed = record.remainingAmount === 0
    const disabled = !verified || !sameCompany || totalClaimed
    return { disabled }
  },
  selectedRowKeys: selectedInvoiceKeys.value,
  onSelect: (record: any, selected: any, selectedRows: any) => {
    if (selected) {
      selectedInvoiceKeys.value.push(record.key)
      selectedInvoiceList.value.push(record)
    } else {
      const index = selectedInvoiceKeys.value.findIndex((item) => item === record.key)
      selectedInvoiceKeys.value.splice(index, 1)
      selectedInvoiceList.value.splice(index, 1)
    }
  },
}

function handleSelect() {
  emit('select', selectedInvoiceList.value)
}

watch(() => props.open, (value) => {
  if (value) fetchInvoiceList()
})
</script>

<style lang="sass" scoped>
.filters-section
  margin-bottom: 16px

.offer-info
  label
    color: #999

.invoice-card
  border-radius: 6px
  padding: 12px
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15)

  .fade-title
    color: #999
    font-size: 12px

  .head
    display: flex
    align-items: center
    .actions
      margin-left: auto

  .value
    text-align: right
    font-size: 20px
    .price
      font-weight: bold
      color: #FF9111
      display: inline
      margin-right: 4px

    .unit
      display: inline
      font-size: 12px
      color: #999
</style>