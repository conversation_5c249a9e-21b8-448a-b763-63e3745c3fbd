<template lang="pug">
a-modal(
  v-model:open="status.visible"
  title="添加合同"
  :destroyOnClose="true"
  :width="1000"
  @cancel="$emit('close')"
)
  a-spin(:spinning="status.loading")
    a-form(ref="formInstanceRef" :model="contract" layout="vertical")
      //- 合同基本信息配置
      .section
        .section-title 合同基本信息配置
        a-row(:gutter="24")
          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请上传合同附件'}"
              name="fileId"
              label="合同附件"
            )
              UploadFile(v-model:value="contract.fileId")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请选择合同类型'}"
              name="contractType"
              label="合同类型"
            )
              a-select(v-model:value="contract.contractType" :options="dict.contractTypeList" )

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请选择货币类型'}"
              :name="['financeConfig', 'currencyType']"
              label="货币类型"
            )
              a-select(v-model:value="contract.financeConfig.currencyType" :options="dict.currencyList")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入最低收费'}"
              :name="['financeConfig', 'minPrice']"
              label="最低收费"
            )
              a-input-number(v-model:value="contract.financeConfig.minPrice" style="width: 100%" :min="0" :controls="false")
                template(#addonAfter) 元

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入超期天数'}"
              :name="['financeConfig', 'exceedConfirmDays']"
              label="超期天数"
            )
              a-input-number(v-model:value="contract.financeConfig.exceedConfirmDays" style="width: 100%" :min="0" :controls="false")
                template(#addonAfter) 天

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入滞纳金'}"
              :name="['financeConfig', 'lateFeePermillage']"
              label="滞纳金"
            )
              a-input-number(v-model:value="contract.financeConfig.lateFeePermillage" style="width: 100%" :min="0" :max="1000" :controls="false")
                template(#addonAfter) ‰

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入开票抬头'}"
              :name="['financeConfig','invoiceTitle']"
              label="开票抬头"
            )
              a-input(v-model:value="contract.financeConfig.invoiceTitle")
          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入公司税号'}"
              :name="['financeConfig','invoiceTaxNumber']"
              label="税号"
            )
              a-input(v-model:value="contract.financeConfig.invoiceTaxNumber")
          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入公司地址'}"
              :name="['financeConfig','invoiceAddress']"
              label="公司地址"
            )
              a-input(v-model:value="contract.financeConfig.invoiceAddress")
          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请选择开票类型'}"
              :name="['financeConfig', 'invoiceName']"
              label="开票类型"
            )
              a-select(v-model:value="contract.financeConfig.invoiceName" :options="dict.invoiceNameList")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请选择开票主体'}"
              :name="['financeConfig', 'financeAccountId']"
              label="开票主体"
            )
              a-select(v-model:value="contract.financeConfig.financeAccountId" :options="dict.entityList")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入开票税率'}"
              :name="['financeConfig', 'invoiceTaxRate']"
              label="开票税率"
            )
              a-input-number(v-model:value="contract.financeConfig.invoiceTaxRate" style="width: 100%" :min="0" :max="100" :controls="false")
                template(#addonAfter) %

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入电话号码'}"
              :name="['financeConfig', 'invoicePhoneNumber']"
              label="电话号码"
            )
              a-input(v-model:value="contract.financeConfig.invoicePhoneNumber" style="width: 100%" :min="0" :max="100" :controls="false")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入开户银行'}"
              :name="['financeConfig', 'invoiceBankName']"
              label="开户银行"
            )
              a-input(v-model:value="contract.financeConfig.invoiceBankName" style="width: 100%" :min="0" :max="100" :controls="false")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入银行账号'}"
              :name="['financeConfig', 'invoiceBankAccount']"
              label="银行账号"
            )
              a-input(v-model:value="contract.financeConfig.invoiceBankAccount" style="width: 100%" :min="0" :max="100" :controls="false")

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请选择薪资区间类型'}"
              :name="['financeConfig', 'salaryTimeUnit']"
              label="薪资区间类型"
            )
              a-select(v-model:value="contract.financeConfig.salaryTimeUnit")
                a-select-option(:value="0") 月薪
                a-select-option(:value="2") 年薪

          a-col(:span="8")
            a-form-item(
              :rules= "{required: true, message: '请输入年薪月数'}"
              :name="['financeConfig', 'salaryTime']"
              label="年薪月数"
            )
              a-input-number(v-model:value="contract.financeConfig.salaryTime" style="width: 100%" :min="0" :max="100" :controls="false")
                template(#addonAfter) 月

      //- 客户付费周期配置
      .section
        .section-title 客户付费周期配置
        template(v-for="(item, index) in contract.financeConfig.paymentCycles" :key="item.key")
          a-row(:gutter="24")
            a-col(:span="8")
              a-form-item(
                :rules= "{required: true, message: '请输入名称'}"
                :name="['financeConfig','paymentCycles', index, 'name']"
                label="名称"
              )
                a-input(v-model:value="item.name")

            a-col(:span="5")
              a-form-item(
                :rules= "{required: true, message: '请选择节点名称'}"
                :name="['financeConfig','paymentCycles', index, 'taskDefinitionKey']"
                label="节点名称"
              )
                a-select(v-model:value="item.taskDefinitionKey" :options="dict.taskDefinitionKeys" )

            a-col(:span="5")
              a-form-item(
                :rules= "{required: true, message: '请输入收款时机'}"
                :name="['financeConfig','paymentCycles', index, 'onFinished']"
                label="收款时机"
              )
                a-select(v-model:value="item.onFinished")
                  a-select-option(:value="0") 开始时
                  a-select-option(:value="1") 结束时

            a-col(:span="4")
              a-form-item(
                :rules= "{required: true, message: '请输入收款比例'}"
                :name="['financeConfig','paymentCycles', index, 'paymentPercent']"
                label="收款比例"
              )
                a-input-number(v-model:value="item.paymentPercent" style="width: 100%" :min="0" :max="100" :controls="false")
                  template(#addonAfter) %

            a-col(:span="2")
              a-form-item(label="操作")
                a-space
                  .pointer(@click="() => handleClickPaymentCycles('del', index)" v-if="contract.financeConfig.paymentCycles.length > 1")                    
                    MinusCircleOutlined
                  .pointer(v-show="index === contract.financeConfig.paymentCycles.length - 1" @click="() => handleClickPaymentCycles('add')")
                    PlusOutlined

      //- 佣金阶梯配置
      .section
        .section-title 佣金阶梯配置 
        template(v-for="(item, index) in contract.financeConfig.quotedPriceOnDelivery" :key="item.key")
          a-row(:gutter="24")
            a-col(:span="8")
              a-form-item(
                :rules= "[{type: 'number', index, required:true, validator: salaryRangeValidator}]"
                :name="['financeConfig','quotedPriceOnDelivery', index, 'salaryFrom']"
                label="薪资阶梯（留空表示不限）"
              )
                a-form-item-rest
                  a-input-group(compact)
                    a-input-number(v-model:value="item.salaryFrom" style="width: 136px" :min="-1" :max="9999999999" :controls="false" placeholder="不限" @change="(value)=>handleSalaryFromChange(index, value)")
                    a-input(style="width: 30px; border-left: 0; background-color: #fff; pointer-events: none" placeholder="~" disabled)
                    a-input-number(v-model:value="item.salaryTo" style="width: 136px; border-left-width: 0;" :min="-1" :max="9999999999" placeholder="不限" :controls="false" @change="(value)=>handleSalaryToChange(index, value)")

            a-col(:span="4")

              a-form-item(
                :rules= "[{type: 'number', required:true, message: '请选择佣金计算单位'}]"
                :name="['financeConfig','quotedPriceOnDelivery', index, 'commissionTimeUnit']"
                label="佣金类型"
              )
                a-select(v-model:value="item.commissionTimeUnit" style="width: 100%")
                  a-select-option(:value="0") 月薪
                  a-select-option(:value="1") 日薪
                  a-select-option(:value="2") 年薪

            a-col(:span="5")
              a-form-item(
                v-if="item.symbol === 'percentageAgentFee'"
                :rules= "{required: true, message: '请输入佣金百分比'}"
                :name="['financeConfig','quotedPriceOnDelivery', index, 'percentageAgentFee']"
                label="佣金百分比"
              )
                a-input-number(v-model:value="item.percentageAgentFee" :min="0" :max="500" :controls="false")
                  template(#addonAfter)
                    a-select(v-model:value="item.symbol" @change="(val) => handleChanegSymbol(val, index)" style="width: 80px")
                      a-select-option(value="percentageAgentFee") %
                      a-select-option(value="fixedAgentFee") 固定

              a-form-item(
                v-if="item.symbol === 'fixedAgentFee'"
                :rules= "{required: true, message: '请输入佣金固定值'}"
                :name="['financeConfig','quotedPriceOnDelivery', index, 'fixedAgentFee']"
                label="佣金固定值"
              )
                a-input-number(v-model:value="item.fixedAgentFee" style="width: 100%" :min="0" :max="10000000" :controls="false")
                  template(#addonAfter)
                    a-select(v-model:value="item.symbol"  @change="(val) => handleChanegSymbol(val, index)" style="width: 100px")
                      a-select-option(value="percentageAgentFee") %
                      a-select-option(value="fixedAgentFee") 固定值

            a-col(:span="5")
              a-form-item(
                :rules= "{required: true, message: '请输入保证期'}"
                :name="['financeConfig','quotedPriceOnDelivery', index, 'insuranceValue']"
                label="保证期"
              )
                a-input-number(v-model:value="item.insuranceValue" :min="0" :max="500" :controls="false")
                  template(#addonAfter)
                    a-select(v-model:value="item.insuranceType")
                      a-select-option(:value="1") 月
                      a-select-option(:value="2") 日

            a-col(:span="2")
              a-form-item(label="操作")
                a-space
                  .pointer(@click="() => handleClickQuotedPriceOnDelivery('del', index)" v-if="contract.financeConfig.quotedPriceOnDelivery.length > 1")
                    MinusCircleOutlined
                  .pointer(@click="() => handleClickQuotedPriceOnDelivery('add', index)")
                    PlusOutlined

  template(#footer)
    a-button(@click="$emit('close')") 取消
    a-button(type="primary" :loading="status.actionLoading" @click="submitContractForm") 确定

</template>

<script lang="ts" setup>
import {
  getContractTypeList,
  getCurrencyList,
  getInvoiceNameList,
  getPerformanceRoleList
} from '@/api/dictionary'
import { createDeliveryContract, getDeliveryContractDetail, updateDeliveryContract, getEntityList } from '@/api/finance'
import { getTaskDefinitionKeys } from '@/api/job'
import { reactive, ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ConsoleSqlOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import UploadFile from '@/components/upload/file.vue'
import { contentQuotesLinter } from 'ant-design-vue/es/_util/cssinjs/linters'

const emit = defineEmits(['close', 'success'])
const props = defineProps<{ customerId: number, contractId?: number }>()

const status = reactive({
  visible: true,
  loading: false,
  dictLoading: false,
  actionLoading: false
})

// 各种字典
const dict = reactive({
  invoiceNameList: [] as any,
  currencyList: [] as any,
  performanceRoleList: [] as any,
  contractTypeList: [] as any,
  contractStatusList: [] as any,
  entityList: [] as any,
  taskDefinitionKeys: [] as any,
})

interface TPaymentCycles {
  key: number
  name: string
  taskDefinitionKey: string
  onFinished: number | null
  paymentPercent: number | null
}

interface TQuotedPriceOnDelivery {
  key: number
  commissionTimeUnit: number | null
  symbol: string
  fixedAgentFee: number | null
  percentageAgentFee: number | null
  salaryFrom: number | null
  salaryTo: number | null,
  insuranceType: number,
  insuranceValue: number | null,
}

interface TPerformanceRoleConfig {
  key: number
  role: number | null
  percent: number | null
}

// 合同详情
interface TContract {
  id: number,
  companyId: number,
  customerId: number,
  contractType: number | null,
  contractTypeStr: string,
  currencyType: number | null,
  currencyTypeStr: string,
  contractStatus: number,
  contractStatusStr: string,
  fileId: string,
  fileUrl: string,
  createdBy: number,
  financeConfig: {
    invoicePhoneNumber: string | null,
    invoiceBankName: string | null,
    invoiceBankAccount: string | null,
    financeAccountId: number | null,
    salaryTimeUnit: number | null,
    salaryTime: number | null,
    currencyType: number | null,
    minPrice: number | null,
    paymentCycles: TPaymentCycles[],
    exceedConfirmDays: number | null,
    lateFeePermillage: number | null,
    invoiceName: number | null,
    invoiceTaxRate: number | null,
    invoiceTitle: string | null,
    invoiceTaxNumber: string | null,
    invoiceAddress: string | null,
    performancePercentage: number | null,
    performanceRoleConfig: TPerformanceRoleConfig[],
    quotedPriceOnDelivery: TQuotedPriceOnDelivery[],
  }
}

const contract = reactive<TContract>({
  id: 0,
  companyId: 0,
  customerId: 0,
  contractType: null,
  contractTypeStr: '',
  currencyType: null,
  currencyTypeStr: '',
  contractStatus: 0,
  contractStatusStr: '',
  fileId: '',
  fileUrl: '',
  createdBy: 0,
  financeConfig: {
    invoicePhoneNumber: null,
    invoiceBankName: null,
    invoiceBankAccount: null,
    financeAccountId: null,
    salaryTimeUnit: null,
    salaryTime: null,
    currencyType: null,
    minPrice: null,
    paymentCycles: [{
      key: Date.now() / 10000,
      name: '',
      taskDefinitionKey: '',
      onFinished: null,
      paymentPercent: null
    }],
    exceedConfirmDays: null,
    lateFeePermillage: null,
    invoiceName: null,
    invoiceTaxRate: null,
    invoiceTitle: '',
    invoiceTaxNumber: '',
    invoiceAddress: '',
    performancePercentage: null,
    performanceRoleConfig: [{
      key: Date.now() / 10000,
      role: null,
      percent: null
    }],
    quotedPriceOnDelivery: [{
      key: Date.now() / 10000,
      commissionTimeUnit: 0,
      symbol: 'percentageAgentFee',
      fixedAgentFee: null,
      percentageAgentFee: null,
      salaryFrom: 0,
      salaryTo: null,
      insuranceType: 1,
      insuranceValue: null,
    }]
  },
})
const formInstanceRef = ref<FormInstance>();

// 进行薪资的校验，前提是需要对薪资按照commissionTimeUnit 和 salaryFrom 进行排序
function salaryRangeValidator(rule: any, value: any) {
  const quotedList = contract.financeConfig.quotedPriceOnDelivery
  const quota = quotedList[rule.index]
  const nextQuota = quotedList[rule.index + 1]
  const preQuota = quotedList[rule.index - 1]
  const isFirst = rule.index === 0 
  const isLast = rule.index === quotedList.length - 1 

  if (isFirst && quota.salaryFrom) {
    return Promise.reject('第一阶梯不应填写起始薪资')
  }

  if (isLast && quota.salaryTo) {
    return Promise.reject('最后一阶梯不应填写截止薪资')
  }

  if (!isFirst && !isLast) {
    if (quota.salaryTo! <= quota.salaryFrom!) {
      return Promise.reject('截止薪资应大于起始薪资')
    }
  }

  if (!isFirst && !quota.salaryFrom) {
    return Promise.reject('请填写起始薪资(含)')
  }

  if (!isLast && !quota.salaryTo) {
    return Promise.reject('请填写截止薪资(不含)')
  }

  if (!isFirst && (quota.salaryFrom! != preQuota.salaryTo!)) {
    return Promise.reject('与上一阶梯不连续')
  }

  return Promise.resolve()
}

function handleSalaryToChange(index:number, value:number | null) {
  const quotedList = contract.financeConfig.quotedPriceOnDelivery
  const isLast = index === quotedList.length - 1

  if (isLast) return
  const nextQuota = quotedList[index + 1]
  nextQuota.salaryFrom = value
}

function handleSalaryFromChange(index:number, value:number | null) {
  const quotedList = contract.financeConfig.quotedPriceOnDelivery
  const isFirst = index === 0

  if (isFirst) return
  const preQuota = quotedList[index - 1]
  preQuota.salaryTo = value
}

const handleClickPaymentCycles = (type: string, index: number | undefined) => {
  switch (type) {
    case 'add':
      contract.financeConfig.paymentCycles.push({
        key: Date.now() / 10000,
        name: '',
        taskDefinitionKey: '',
        onFinished: null,
        paymentPercent: null
      })
      break;
    case 'del':
      contract.financeConfig.paymentCycles.splice(index as number, 1)
      break;
    default:
      console.error('不存在的 type')
      break;
  }
}

const handleClickQuotedPriceOnDelivery = (type: string, index: number) => {
  switch (type) {
    case 'add':
      const commissionTimeUnit = contract.financeConfig.quotedPriceOnDelivery[index].commissionTimeUnit
      const salaryFrom = contract.financeConfig.quotedPriceOnDelivery[index].salaryTo

      contract.financeConfig.quotedPriceOnDelivery.push({
        key: Date.now() / 10000,
        commissionTimeUnit: commissionTimeUnit,
        symbol: 'percentageAgentFee',
        fixedAgentFee: null,
        percentageAgentFee: null,
        salaryFrom: salaryFrom,
        salaryTo: null,
        insuranceType: 1,
        insuranceValue: null
      })
      break;
    case 'del':
      contract.financeConfig.quotedPriceOnDelivery.splice(index as number, 1)
      break;
    default:
      console.error('不存在的 type')
      break;
  }
}

// const handleClickPerformanceRoleConfig = (type: string, index: number | undefined) => {
//   switch (type) {
//     case 'add':
//       contract.financeConfig.performanceRoleConfig.push({
//         key: Date.now() / 10000,
//         role: null,
//         percent: null
//       })
//       break;
//     case 'del':
//       contract.financeConfig.performanceRoleConfig.splice(index as number, 1)
//       break;
//     default:
//       console.error('不存在的 type')
//       break;
//   }
// }

const handleChanegSymbol = (val: string, index: number) => {
  if (val === 'percentageAgentFee') {
    contract.financeConfig.quotedPriceOnDelivery[index].fixedAgentFee = null
  } else {
    contract.financeConfig.quotedPriceOnDelivery[index].percentageAgentFee = null
  }
}

async function initDictionary() {
  status.actionLoading = true

  try {
    const [
      dictInvoiceName, dictCurrencyList, dictPerformanceRole, dictContractType,
      dictEntityList, dictTaskDefinitionKeys
    ] = await Promise.all([
      getInvoiceNameList(), getCurrencyList(), getPerformanceRoleList(), getContractTypeList(),
      getEntityList(), getTaskDefinitionKeys()
    ])
    dict.invoiceNameList = dictInvoiceName.data.map((item: any) => { return { label: item.name, value: item.id } })
    dict.currencyList = dictCurrencyList.data.map((item: any) => { return { label: item.name, value: item.id } })
    dict.performanceRoleList = dictPerformanceRole.data.map((item: any) => { return { label: item.name, value: item.id } })
    dict.contractTypeList = dictContractType.data.map((item: any) => { return { label: item.name, value: item.id } })
    dict.entityList = dictEntityList.data.map((item: any) => { return { label: item.companyFullName, value: item.id } })
    dict.taskDefinitionKeys = dictTaskDefinitionKeys.data.map((item: any) => { return { label: item.name, value: item.id } })
  } catch (err: any) {
    message.error(err.message)
  }

  status.actionLoading = false
}

async function initDeliveryContractDetail() {
  if (!props.contractId) return

  try {
    status.loading = true
    const { data } = await getDeliveryContractDetail(props.customerId, props.contractId)
    data.financeConfig.paymentCycles = data.financeConfig.paymentCycles.map((item: any, index: number) => {
      return { ...item, key: index, onFinished: item.onFinished ? 1 : 0 }
    })

    data.financeConfig.performanceRoleConfig = data.financeConfig.performanceRoleConfig.map((item: any, index: number) => {
      return { ...item, key: index }
    })

    data.financeConfig.quotedPriceOnDelivery = data.financeConfig.quotedPriceOnDelivery.map((item: any, index: number) => {
      // 在这里统一将读取的“不限”设置为null
      if (item.salaryFrom === -1) item.salaryFrom = null
      if (item.salaryTo === -1) item.salaryTo = null
      return { ...item, key: index, symbol: item.percentageAgentFee ? 'percentageAgentFee' : 'fixedAgentFee' }
    })

    if (data.financeConfig.quotedPriceOnDelivery.length === 0) {
      data.financeConfig.quotedPriceOnDelivery.push({
        key: Date.now() / 10000,
        commissionTimeUnit: null,
        symbol: 'percentageAgentFee',
        fixedAgentFee: null,
        percentageAgentFee: null,
        salaryFrom: null,
        salaryTo: null,
        insuranceType: 1,
        insuranceValue: null,
      })
    }
    Object.assign(contract, data)
    sortQuotaList()
  } catch (err: any) {
    message.error(err.message)
  }

  status.loading = false
}

async function checkTotalPercent() {
  const paymentCycles = contract.financeConfig.paymentCycles
  const totalPercent = paymentCycles.reduce((aggr: number, item: any) => {
    return aggr + (item.paymentPercent || 0)
  }, 0)
  if (totalPercent !== 100) {
    message.error('付款比例总和须为100%')
    return false
  }
}

async function sortQuotaList() {
  const quotedList = contract.financeConfig.quotedPriceOnDelivery
  quotedList.sort((a, b) => {
    const hight = (a.commissionTimeUnit! - b.commissionTimeUnit!) * 1000
    const low = (a.salaryFrom! - b.salaryFrom!)
    return hight + low
  })
}

async function submitContractForm() {
  try {

    if (!formInstanceRef.value) return
    // 这里提前对 quotedList 进行排序
    await sortQuotaList()
    await formInstanceRef.value.validate()
    if (await checkTotalPercent() === false) return

    status.actionLoading = true
    const nPaymentCycles = contract.financeConfig.paymentCycles.map((item: any) => {
      return { ...item, onFinished: !!item.onFinished }
    })

    const params = JSON.parse(JSON.stringify(contract))
    params.financeConfig.quotedPriceOnDelivery = params.financeConfig.quotedPriceOnDelivery.map((item: any) => {
      // 将null转换为-1
      if (item.salaryFrom === null) item.salaryFrom = -1
      if (item.salaryTo === null) item.salaryTo = -1
      return item
    })
    params.financeConfig.paymentCycles = nPaymentCycles

    if (params.financeConfig.currencyType !== 0) {
      Modal.info({
        title: "请确认签约公司是否正确",
        content:
          "您选择的是非人民币币种，原则上当使用非人民币进行结算时，签约公司需要选择香港公司作为签约主体，请确认无误后再次提交。",
        okCancel: true,
        onOk: async () => {
          await submitFinalContractForm(params)
        },
      })
    } else {
      await submitFinalContractForm(params)
    }
  } catch (err: any) {
    console.log(err.errorFields)
    if (err.errorFields) {
      message.error(err.errorFields[0].errors)
    } else {
      message.error(err.message)
    }
  }
  status.actionLoading = false
}

async function submitFinalContractForm(params: any) {
  if (props.contractId) {
    await updateDeliveryContract(
      props.customerId,
      props.contractId,
      { ...params, customerId: props.customerId }
    )
  } else {
    await createDeliveryContract(
      props.customerId,
      { ...params, customerId: props.customerId }
    )
  }
  message.success('操作成功')
  emit('success')
}

onMounted(async () => {
  await initDictionary()
  initDeliveryContractDetail()
})
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.section-title {
  font-size: 14px;
  line-height: 24px;
  padding-left: 16px;
  margin-bottom: 20px;
  font-weight: bold;
  position: relative;

  &::before {
    content: "";
    display: block;
    width: 4px;
    height: 20px;
    top: 2px;
    left: 0;
    position: absolute;
    border-radius: 2px;
    background-color: #FF9111;
  }
}

.tip {
  font-size: 12px;
  color: #ff4d4f;
}
</style>