<template lang="pug">
.job-progress-actions
  template(v-if="status.showForm")
    template(v-if="action === 'weed_out'")
      ObsoleteAction(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_pm_talent_get'")
      PmTalentGet(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_pm_talent_get_to_customer'")
      PmTalentToCustomer(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_pm_talent_interview'")
      PmTalentInterview(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_pm_talent_to_salary'")
      PmTalentToSalary(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_pm_talent_to_offer'")
      PmTalentToOffer(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm" :update="actionConfig.isUpdate")
    template(v-else-if="action === 'position_pm_talent_to_inspect'")
      PmTalentToInspect(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm" :update="actionConfig.isUpdate")
    template(v-else-if="action === 'position_pm_talent_to_hired'")
      PmTalentToHired(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm" :update="actionConfig.isUpdate")
    template(v-else-if="action === 'position_pm_talent_in_keep'")
      PmTalentInKeep(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm")
    template(v-else-if="action === 'position_interview_task'")
      PositionInterviewTask(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm" :parentTaskId="selectedTaskId" @update="()=>fetchInterviewList(actionConfig)")
    template(v-else-if="action === 'position_pm_talent_interview_schedule'")
      PmTalentInterviewSchedule(:jobId="jobRequirementId" :taskId="actionConfig.taskId" :talentId="actionConfig.talentId" v-model:open="status.showForm" :parentTaskId="selectedTaskId" @update="()=>fetchInterviewList(actionConfig)")

  a-drawer(v-model:open="status.showInterviewList" :destroyOnClose="true" :width="720" title="面试列表" :bodyStyle="{padding: 0}")
    .interview-list-container
      a-table(:columns="interviewColumnsConfig" :data-source="interviewList" :loading="status.interviewListLoading")
        template(#bodyCell="{column, record, index}")
          template(v-if="column.key === 'time'")
            a-tag(v-if="index == 0" color="#ff9111") 初试
            a-tag(v-else) 复试
            span {{ record.startDate }}
          template(v-if="column.key === 'result'")
            template(v-if="!record.interviewResult")
              a-button(@click="()=>{ trigger('position_interview_task', record.taskId, record.talentId) }") 反馈结果
            template(v-else)
              .interview-result
                CheckCircleFilled(style="color:#03E3B0;margin-right:8px;" v-if="record.interviewResult == '通过'")
                CloseCircleFilled(style="color:#F9470D;margin-right:8px;" v-if="record.interviewResult == '淘汰'")
                span {{ record.interviewResult }}
          template(v-if="column.key === 'action'")
            a-popconfirm(title="确认要删除此条面试记录？该操作不可恢复。" ok-text="删除", cancel-text="取消" @confirm="()=>{deleteInterview(record.talentId, record.taskId)}" :ok-button-props="{loading: status.deleteInterviewLoading}")
              a-button 删除
      .drawer-action
        a-space(:size="16")
          a-button(@click="() => { status.showInterviewList = false }") 关闭
          a-button(type="primary" @click="() => {trigger('position_pm_talent_interview_schedule', selectedTaskId, selectedTalentId)}") 添加面试

</template>

<script lang="ts" setup>
import {
  addInterviewTask,
  finishJobTask,
  getInterviewTaskList,
  getJobRequirementTask,
  updateInterviewTaskVariables,
  updateTaskVariables
} from '@/api/job'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { reactive, ref, toRef } from 'vue'
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue'
import { getCalculatedOfferPrice } from '@/api/finance'
import { getCurrencyList } from '@/api/dictionary'
import { onMounted, watch } from 'vue'
import ObsoleteAction from './progress-actions/obsolete.vue'
import PmTalentGet from './progress-actions/pm-talent-get.vue'
import PmTalentToCustomer from './progress-actions/pm-talent-to-customer.vue'
import PmTalentInterview from './progress-actions/pm-talent-interview.vue'
import PmTalentToSalary from './progress-actions/pm-talent-to-salary.vue'
import PmTalentToOffer from './progress-actions/pm-talent-to-offer.vue'
import PmTalentToInspect from './progress-actions/pm-talent-to-inspect.vue'
import PmTalentToHired from './progress-actions/pm-talent-to-hired.vue'
import PmTalentInKeep from './progress-actions/pm-talent-in-keep.vue'
import PositionInterviewTask from './progress-actions/interview-task.vue'
import PmTalentInterviewSchedule from './progress-actions/pm-talent-interview-schedule.vue'

type ActionKeys = 'weed_out' |
  'position_pm_talent_get' |
  'position_pm_talent_get_to_customer' |
  'position_pm_talent_interview' |
  'position_pm_talent_to_salary' |
  'position_pm_talent_to_offer' |
  'position_pm_talent_to_inspect' |
  'position_pm_talent_to_hired' |
  'position_pm_talent_in_keep'

const action = ref<ActionKeys>()

const props = defineProps<{ jobRequirementId: number | undefined }>()
const emit = defineEmits(['update'])
const jobRequirementId = toRef(props, 'jobRequirementId')
const userStore = useUserStore()
const actionConfig = ref<any>({})
const interviewList = ref<any[]>([])
const selectedTaskId = ref<string>()
const selectedTalentId = ref<number>()

const status = reactive({
  interviewListLoading: false,
  actionLoading: false,
  showForm: false,
  showInterviewList: false,
  deleteInterviewLoading: false,
})

const interviewColumnsConfig = [
  { title: '面试时间', key: 'time', dataIndex: 'startDate' },
  { title: '方式', key: 'type', dataIndex: 'interviewType' },
  { title: '面试官', key: 'interviewer', dataIndex: 'interviewer' },
  { title: '终面', key: 'finalInterview', dataIndex: 'isFinalInterview' },
  { title: '结果', key: 'result', dataIndex: 'interviewResult' },
  { title: '操作', key: 'action' }
]

async function fetchInterviewList(action:any) {
  status.interviewListLoading = true
  try {
    const res = await getInterviewTaskList(action.jobRequirementId, action.talentId)
    interviewList.value = res.data.tasks.map((item: any, index: number) => {

      return {
        startDateTimeStamp: dayjs(item.task.variables.startDate, 'YYYY-MM-DD HH:mm').valueOf(),
        startDate: item.task.variables.startDate,
        interviewType: item.task.variables.interviewType,
        interviewer: item.task.variables.interviewer,
        isFinalInterview: item.task.localVariables.isFinalInterview == '1' ? '是' : '',
        interviewResult: item.task.localVariables.interviewResult,
        taskDefinitionKey: item.task.taskDefinitionKey,
        taskId: item.task.id,
        talentId: item.talent.id
      }

    }).sort((a: any, b: any) => a.startDateTimeStamp - b.startDateTimeStamp)
  } catch (err: any) {
    message.error(err.message)
  }
  status.interviewListLoading = false
}

async function deleteInterview(talentId: number, taskId: string) {
  status.deleteInterviewLoading = true
  try {
    const params = {
      variables: {
        isDeleted: true,
        deleteBy: userStore.id
      }
    }
    const res = await updateInterviewTaskVariables(jobRequirementId.value!, taskId, params)
    fetchInterviewList({jobRequirementId: jobRequirementId.value, talentId: talentId})
    message.success('操作成功！')
  } catch (err: any) {
    message.error(err.message)
  }
  status.deleteInterviewLoading = false
}

async function trigger(actionKey: ActionKeys, taskId: string, talentId: number, isUpdate: boolean | undefined) {
  action.value = actionKey
  actionConfig.value = Object.assign({}, {jobRequirementId: jobRequirementId.value, taskId, talentId, isUpdate })
  status.showForm = true
}

async function showInterviewList(taskId: string, talentId: number) {
  status.showInterviewList = true
  selectedTalentId.value = talentId
  selectedTaskId.value = taskId
  await fetchInterviewList({jobRequirementId: jobRequirementId.value, talentId: talentId})
}

defineExpose({
  trigger,
  showInterviewList
})

watch(() => status.showForm, (val) => {
  if (!val) { emit('update') }
})

</script>

<style lang="scss" scoped>
.interview-list-container {
  padding: 16px;

  .drawer-action {
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    padding: 16px;
    text-align: right;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
  }
}
</style>


