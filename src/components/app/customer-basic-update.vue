<template lang="pug">
.customer-detail-component
  .customer-detail-form
    a-spin(:spinning="status.loading")
      a-form(ref="formInstance" :model="form" layout="vertical")
        a-form-item(
          label="客户LOGO"
          name="customerLogo"
        )
          a-upload.logo_uploader(
            name="image",
            accept="png,jpg,jpeg",
            :show-upload-list="false",
            :action="API_URL.LOGO_UPLOAD",
            :headers="{'Authorization': userStore.token}",
            @change="handleLogoUploadChange"
          )
            //- :before-upload="beforeUploadAvatar",
            a-avatar.avatar(
              v-if="form.logoUrl",
              :src="form.logoUrl",
              alt="avatar"
              :size="140"
              shape="square"
            )
            div.upload-area(v-else)
              LoadingOutlined(v-if="status.logoUploading")
              PlusOutlined(v-else)

        a-form-item(
          label="客户名称(全称)"
          name="customerFullName"
          :rules="[{ type: 'string', required: true, message: '客户名称不能为空' }]"
        )
          a-input(v-model:value="form.customerFullName")

        a-form-item(
          label="客户简称"
          name="customerExternalName"
          :rules="[{ type: 'string', required: false, message: '客户名称不能为空' }]"
        )
          a-input(v-model:value="form.customerExternalName")

        a-form-item(
          label="所在地区"
          name="areaId"
          :rules="[{ type: 'number', required: true, message: '客户所在地区不能为空' }]"
        )
          a-tree-select( 
            style="width:100%"
            :fieldNames="{ label: 'title', value: 'id' }",
            placeholder="请在下拉列表中选择城市",
            v-model:value="form.areaId",
            treeNodeFilterProp="title",
            show-search,
            :tree-data="dict.areas",
          )

        a-form-item(
          label="详细地址"
          name="customerAddress"
          :rules="[{ type: 'string', required: false }]"
        )
          a-input(type="text" v-model:value="form.customerAddress")

        a-form-item(
          label="所属行业"
          name="industryId"
          :rules="[{ type: 'array', required: true, message: '客户所属行业不能为空' }]"
        )
          a-cascader(
            placeholder="请选择所属行业",
            v-model:value="form.industryId",
            :fieldNames="{ label: 'title', value: 'id' }",
            treeNodeFilterProp="title",
            :options="dict.industry",
          )

        a-form-item(
          label="公司规模"
          name="companyScale"
          :rules="[{required: true, message: '客户名称不能为空' }]"
        )
          //- 这里scale传从接口来的index
          a-select(
            placeholder="请选择公司规模",
            v-model:value="form.companyScale",
            :options="dict.scale",
          )

        a-form-item(
          label="公司性质"
          name="companyType"
          :rules="[{required: true, message: '客户名称不能为空' }]"
        )
          //- 这里scale传从接口来的index
          a-select(
            placeholder="请选择公司性质",
            v-model:value="form.companyType",
            :options="dict.type",
          )

        a-form-item(
          label="融资规模"
          name="investStatus"
          :rules="[{required: true, message: '客户名称不能为空' }]"
        )
          //- 这里scale传从接口来的index
          a-select(
            placeholder="请选择公司融资规模",
            v-model:value="form.investStatus",
            :options="dict.fundingStage",
          )

        a-form-item(
          label="客户详细介绍"
          name="customerIntroduction"
          :rules="[{required: true, message: '公司简介不能为空' }]"
        )
          a-textarea(
            placeholder="请选择公司融资规模",
            v-model:value="form.customerIntroduction",
            :autoSize= "{minRows: 4, maxRows: 10}"
          )

        a-form-item(
          label="客户负责人"
          name="companyUserId"
          :rules="[{ required: true, type: 'number', message: '负责的员工不能为空' }]"
        )
          a-select(
            placeholder="请选择负责的员工",
            v-model:value="form.companyUserId",
            show-search
            :filterOption="true",
            :optionFilterProp="'label'",
            :options="dict.staff",
          )

        a-form-item(
          label="客户标签"
        )
          template(v-for="(tag, index) in form.tags" :key="tag")
            a-tag(:closable="true" @close="deleteTag(index)") {{ tag }}
          a-input(
            v-if="status.editTags"
            ref="tagInputInstance"
            v-model:value="inputTag"
            type="text"
            size="small"
            :style="{ width: '78px' }"
            @blur="newTag"
            @keyup.enter="newTag"
          )
          a-tag(v-else style="background: #fff; border-style: dashed" @click="tagEdit")
            plus-outlined
            span 添加新标签

  .customer-detail-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="handleUpdateCustomer" type="primary") 保存

</template>

<script lang="ts" setup>
import { getCustomerDetail, updateCustomerDetail, API_URL } from '@/api/customer'
import { dictionary, getAllIndustryList, getCompanyFundingStageList, getCompanyScaleList, getCompanyTypeList } from '@/api/dictionary'
import { getCompanyUserList } from '@/api/system/users'
import { useUserStore } from '@/store/user.store'
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { message } from 'ant-design-vue'
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { nextTick, onMounted, reactive, ref, toRef, toRefs } from 'vue'

interface CustomerDetail {
  id: number,
  customerFullName: string
  customerExternalName: string
  areaId: number
  investStatus: number
  companyType: number
  companyScale: number
  industryId: number[]
  customerAddress: string
  customerIntroduction: string
  customerLogo: string
  logoUrl: string,
  tags: string[],
  companyUserId: number,
  source: number,
}

const userStore = useUserStore()
const upload = ref<any>({})
const status = reactive({
  initDict: false,
  loading: false,
  logoUploading: false,
  editTags: false,
})

const dict = reactive({
  areas: [] as any,
  industry: [] as any[],
  scale: [],
  type: [],
  fundingStage: [] as any[],
  staff: [] as any[],
})

const props = defineProps({ customerId: Number })
const customerId = toRef(props, 'customerId')
const propRefs = toRefs(props)
const emit = defineEmits(['close', 'update'])
const form = ref<CustomerDetail>({
  id: 0,
  customerFullName: '',
  customerExternalName: '',
  areaId: 0,
  investStatus: 0,
  companyType: 0,
  companyScale: 0,
  industryId: [],
  customerAddress: '',
  customerIntroduction: '',
  customerLogo: '',
  logoUrl: '',
  tags: [],
  companyUserId: 0,
  source: 0
} as CustomerDetail)
const formInstance = ref()

async function initDict() {
  status.initDict = true
  try {
    const [dictCompanyType, dictCompanyScale, dictArea, dictIndustry, dictFundingStag] = await Promise.all([getCompanyTypeList(), getCompanyScaleList(), dictionary.getAllAreaList(), getAllIndustryList(), getCompanyFundingStageList()])
    const dictStaff = await getCompanyUserList({ onboard: true })
    dict.type = dictCompanyType.data.map((item: any, index: number) => { return { value: item.id, label: item.type } })
    dict.scale = dictCompanyScale.data.map((item: any, index: number) => { return { value: item.id, label: item.type } })

    const [areaDictTreeData] = areaDictToTreeData(dictArea.data)
    dict.areas = areaDictTreeData

    for (let key in dictFundingStag.data) {
      dict.fundingStage.push({ value: Number(key), label: dictFundingStag.data[key] })
    }

    const dictIndustryMap = new Map()
    const tempIdustryList = [] as any[]
    dictIndustry.data.forEach((item: any, index: number) => {
      const targetObj = Object.assign({}, item, { title: item.industryName, children: [] })
      dictIndustryMap.set(item.id, targetObj)
      if (item.parentId === 0) tempIdustryList.push(targetObj)
    })
    dictIndustryMap.forEach((item, key) => {
      if (item.parentId === 0) return
      const parent = dictIndustryMap.get(item.parentId)
      parent.children.push(item)
    })

    dict.industry = [...dict.industry, ...tempIdustryList]
    dict.staff = dictStaff.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

async function fetchCustomerDetail(customerId: number | undefined) {
  if (!customerId) return
  status.loading = true
  try {
    const res = await getCustomerDetail(customerId)
    // form.value = res.data

    form.value.id = res.data.id
    form.value.customerFullName = res.data.customerFullName
    form.value.customerExternalName = res.data.customerExternalName
    form.value.areaId = res.data.areaId === 0 ? null : res.data.areaId
    form.value.investStatus = res.data.investStatus
    form.value.companyType = res.data.companyType
    form.value.companyScale = res.data.companyScale
    dict.industry.unshift({
      title: res.data.industryStr,
      id: res.data.industryId
    })

    form.value.industryId = [res.data.industryId]
    form.value.customerAddress = res.data.customerAddress
    form.value.customerIntroduction = res.data.customerIntroduction
    form.value.customerLogo = res.data.customerLogo
    form.value.tags = Array.isArray(res.data.tags) ? res.data.tags.map((item: any) => item.name) : []

    form.value.companyUserId = res.data.companyUserId
    form.value.source = res.data.source

    form.value.logoUrl = `https://image.itp.smartdeer.work/images/${res.data.customerLogo}`

    // console.log(form.value)

  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleUpdateCustomer() {
  status.loading = true
  try {
    await formInstance.value.validate()

    const params = {
      ...form.value,
      industryId: form.value.industryId[form.value.industryId.length - 1]
    }
    const res = await updateCustomerDetail(params)
    emit('update')
    message.success('客户详情更新成功！')
  } catch (err: any) {
    if (err.errorFields && err.errorFields.length > 0) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

function handleLogoUploadChange(info: any) {
  if (info.file.status === 'uploading') {
    status.logoUploading = true
  } else if (info.file.status === 'done') {
    const fileResponse = info.file.response.data
    status.logoUploading = false
    form.value.logoUrl = fileResponse.fileAbsolutePath
    form.value.customerLogo = fileResponse.id
  }
}

const inputTag = ref<string>('')
const tagInputInstance = ref()
function newTag() {
  status.editTags = false
  if (inputTag.value) form.value!.tags.push(inputTag.value)
  inputTag.value = ''
}

function deleteTag(index: number) {
  form.value.tags.splice(index, 1)
}

function tagEdit() {
  status.editTags = true
  nextTick(() => {
    tagInputInstance.value.focus()
  })
}

onMounted(() => {
  initDict()
  if (customerId.value) {
    fetchCustomerDetail(props.customerId)
  }
})

</script>

<style lang="scss" scoped>
.customer-detail-component {
  padding-bottom: 56px;

  .customer-detail-form {
    padding: 12px 24px;
  }

  .customer-detail-action {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px 24px;
    box-sizing: border-box;
    text-align: right;
  }
}

.logo_uploader {
  .upload-area {
    width: 140px;
    height: 140px;
    border: 1px dotted #999;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>