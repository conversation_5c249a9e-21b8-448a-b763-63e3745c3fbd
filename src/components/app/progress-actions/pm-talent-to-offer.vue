<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="已发放OFFER"
  :open="open"
  :destroyOnClose="true"
  okText="确认"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="薪资类型"
      name="salaryUnit"
      :rules="[{ required: true, message: '请选择薪资类型' }]"
    )
      a-radio-group(
        v-model:value="form.salaryUnit"
        :options="[{ label: '月薪', value: 'monthly' }, { label: '年薪', value: 'yearly' }]"
      )

    a-form-item(
      label="薪资（请填写完整薪资，10K，请填写10000）"
      name="salary"
      :rules="[{ required: true, message: '请输入薪资' }]"
    )
      a-input-number(
        v-model:value="form.salary"
        :min="0"
        style="width: 100%"
        @blur="handleChange"
        suffix="RMB"
      )
        template(#addonAfter) 
          a-select(
            v-model:value="form.currencyType"
            :options="options.curreny"
          )

    a-form-item(
      v-if="form.salaryUnit === 'monthly'"
      label="发薪月数"
      name="payMonth"
      :rules="[{ required: true, message: '请输入发薪月数' }]"
    )
      a-input-number(
        v-model:value="form.payMonth"
        :min="12"
        style="width: 100%"
        @change="handleChange"
      )
        template(#addonAfter) 月


    a-row(:gutter="[12, 12]")
      a-col(:span="12")
        a-form-item(
          name="expectOnboardingDate"
          label="预计入职时间"
          :rules="[{ required: true, message: '请选择预计入职时间' }]"
        )
          a-date-picker(
            v-model:value="form.expectOnboardingDate"
            style="width: 100%"
            @change="handleChange"
          )
      a-col(:span="12")
        a-form-item(
          name="expectOverinsureDate"
          label="预计过保时间"
          :rules="[{ required: true, message: '请选择预期过保时间' }]"
        )
          a-date-picker(
            v-model:value="form.expectOverinsureDate"
            style="width: 100%"
          )

    a-form-item(
      name="expectCommission"
      label="预计佣金 (单位: 元)"
      :rules="[{ type: 'number', min: 1, required: true, message: '预期佣金应大于0' }]"
    )
      a-input-number(
        :min="0"
        style="width: 100%"
        v-model:value="form.expectCommission"
      )
        template(#addonAfter) 
          a-select(
            @change="handleChange"
            v-model:value="form.commissionCurrencyType"
            :options="options.curreny"
          )

</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, getJobRequirementTask, updateTaskVariables } from '@/api/job'
import { onMounted } from 'vue'
import { getCurrencyList } from '@/api/dictionary'
import { getCalculatedOfferPrice } from '@/api/finance'
import dayjs, { Dayjs } from 'dayjs'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean, update?: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const isUpdate = toRef(props, 'update')
const processInstanceId = ref()
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const options = reactive({
  curreny: []
})

const form = reactive({
  commissionCurrencyType: 'RMB',
  expectCommission: undefined as any,

  salaryUnit: 'monthly',
  currencyType: 'RMB',
  salary: undefined as any,
  payMonth: undefined as any,
  expectOnboardingDate: null as Dayjs | null,
  expectOverinsureDate: null as Dayjs | null,
})

function handleChange() {
  if (!form.salary) return
  let salaryTimeUnit = 0
  if (form.salaryUnit === 'yearly') {
    salaryTimeUnit = 2
  }
  getOfferExpects(
    form.salary, salaryTimeUnit, form.payMonth,
    form.currencyType, form.commissionCurrencyType
  )
}

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()

    let payMonth = form.payMonth
    if (form.salaryUnit === 'yearly') {
      payMonth = ''
    }

    await finishTask({
      commissionCurrencyType: form.commissionCurrencyType,
      expectCommission: form.expectCommission,
      salaryUnit: form.salaryUnit,
      currencyType: form.currencyType,
      salary: form.salary,
      payMonth: payMonth,
      expectOnboardingDate: form.expectOnboardingDate!.format('YYYY-MM-DD'),
      expectOverinsureDate: form.expectOverinsureDate!.format('YYYY-MM-DD'),
    })
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }
  if (isUpdate.value === true) {
    await updateTaskVariables(jobId.value, taskId.value, form)
  } else {
    await finishJobTask(jobId.value, taskId.value, params)
  }
}

async function initCurrentDict() {
  const res = await getCurrencyList()
  options.curreny = res.data.map((item: any) => {
    return {
      label: `${item.name} - ${item.key}`,
      value: item.key
    }
  })
}

async function getOfferExpects(salary: number, salaryTimeUnit: number, payMonth: number, currencyType: string, commissionCurrencyType: string) {
  status.loading = true
  try {
    const res = await getCalculatedOfferPrice(
      jobId.value!, processInstanceId.value!,
      salary, salaryTimeUnit, payMonth, currencyType,
      commissionCurrencyType
    )

    if (form.expectCommission !== '') {
      form.expectCommission = res.data.totalAmount / 100
    }

    if (form.expectOnboardingDate) {
      const quota = res.data.quotedPrice
      if ([1, 2].includes(quota.insuranceType)) {
        const dataUnit = quota.insuranceType === 1 ? 'month' : 'day'
        const expectDate = dayjs(form.expectOnboardingDate).add(quota.insuranceValue, dataUnit)
        form.expectOverinsureDate = expectDate
      }
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function getProcessInstanceId(jobId: number, taskId: string) {
  try {
    const res = await getJobRequirementTask(jobId, taskId)
    return res.data.processInstanceId
  } catch (err: any) {
    message.error(err.message)
  }
}

async function initForm(jobId: number, taskId: string) {
  try {
    const res = await getJobRequirementTask(jobId, taskId)

    // 所有值在提交给后端后，被保存为字符串，所以需要前端单独处理
    const localVariables = res.data.localVariables

    form.commissionCurrencyType = localVariables.commissionCurrencyType
    form.expectCommission = parseInt(localVariables.expectCommission)
    form.salaryUnit = localVariables.salaryUnit
    form.currencyType = localVariables.currencyType
    form.salary = parseInt(localVariables.salary)
    form.payMonth = !Number.isNaN(parseInt(localVariables.payMonth)) ? parseInt(localVariables.payMonth) : null
    form.expectOnboardingDate = dayjs(localVariables.expectOnboardingDate)
    form.expectOverinsureDate = dayjs(localVariables.expectOverinsureDate)

  } catch (err: any) {
    message.error(err.message)
  }
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

onMounted(async () => {
  await initCurrentDict()
  processInstanceId.value = await getProcessInstanceId(jobId.value!, taskId.value!)
  if (isUpdate.value) {
    initForm(jobId.value!, taskId.value!)
  }
})
</script>

<style lang="sass" scoped>

</style>