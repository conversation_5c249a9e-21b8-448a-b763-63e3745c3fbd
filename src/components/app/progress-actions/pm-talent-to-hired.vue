<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="已入职"
  :open="open"
  :destroyOnClose="true"
  okText="确定"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="入职日期"
      name="onboardingDate"
      :rules="[{ required: true, message: '请选择入职时间。' }]"
    )
      a-date-picker(
        v-model:value="form.onboardingDate"
        style="width: 100%"
      )
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, getJobRequirementTask, updateTaskVariables } from '@/api/job'
import { onMounted } from 'vue'
import dayjs, { Dayjs } from 'dayjs'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean, update?: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const isUpdate = toRef(props, 'update')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  onboardingDate: null as Dayjs | null,
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await finishTask({
      onboardingDate: form.onboardingDate?.format('YYYY-MM-DD')
    })
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }

  if (isUpdate.value === true) {
    await updateTaskVariables(jobId.value, taskId.value, form)
  } else {
    const res = await finishJobTask(jobId.value, taskId.value, params)
  }
}

async function initForm(jobId: number, taskId: string) {
  try {
    const res = await getJobRequirementTask(jobId, taskId)
    const localVariables = res.data.localVariables
    form.onboardingDate = dayjs(localVariables.onboardingDate)
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

onMounted(() => {
  if (isUpdate.value) {
    initForm(jobId.value!, taskId.value!)
  }
})

</script>

<style lang="sass" scoped>

</style>