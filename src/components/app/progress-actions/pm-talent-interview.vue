<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="进入谈薪"
  :open="open"
  :destroyOnClose="true"
  okText="确认"
  :confirmLoading="status.loading"
  @cancel="() => emit('update:open', false)"
)
  p 确认该候选人已进入谈薪阶段？
      
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, addInterviewTask } from '@/api/job'
import { onMounted } from 'vue'
import { Modal } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const talentId = toRef(props, 'talentId')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  startTime: null as Dayjs | null,
  duration: 60,
  interviewType: '',
  interviewAddress: '',
  interviewer: ''
})

async function handleConfirm() {
  status.loading = true
  try {
    await finishTask({})
    message.success('操作成功！')
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }
  const res = await finishJobTask(jobId.value, taskId.value, params)
}

</script>

<style lang="sass" scoped>

</style>