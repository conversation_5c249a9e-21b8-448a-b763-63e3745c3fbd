<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="约面试"
  :open="open"
  :destroyOnClose="true"
  okText="约面试"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="面试开始时间"
      name="startTime"
      :rules="[{ required: true, message: '请选择面试时间' }]"
    )
      a-date-picker(
        :show-time="{ format: 'HH:mm', minuteStep: 15 }"
        format="YYYY-MM-DD HH:mm"
        v-model:value="form.startTime"
        style="width: 100%"
      )

    a-form-item(
      label="时长"
      name="duration"
      :rules="[{ required: true, message: '请选择面试时长'}]"
    )
      a-select(
        :options="[{ label: '半小时', value: 30 }, { label: '1小时', value: 60 }, { label: '1.5小时', value: 90 }, { label: '2小时', value: 120 }]"
        v-model:value="form.duration"
      )

    a-form-item(
      label="面试方式"
      name="interviewType"
      :rules="[{ required: true, message: '请选择面试方式' }]"
    )
      a-radio-group(v-model:value="form.interviewType")
        a-radio(value="线上") 线上
        a-radio(value="线下") 线下

    a-form-item(
      label="面试地点"
      name="interviewAddress"
    )
      a-input(v-model:value="form.interviewAddress")

    a-form-item(
      label="面试官"
      :rules="[{ required: false }]"
    )
      a-input(v-model:value="form.interviewer")
      
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, addInterviewTask } from '@/api/job'
import { onMounted } from 'vue'
import { Modal } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean, parentTaskId: string }>()
const emit = defineEmits(['update:open', 'update'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const talentId = toRef(props, 'talentId')
const parentTaskId = toRef(props, 'parentTaskId')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  startTime: null as Dayjs | null,
  duration: 60,
  interviewType: '',
  interviewAddress: '',
  interviewer: ''
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()

    const start = form.startTime!.format('YYYY-MM-DD HH:mm')
    const end = dayjs(form.startTime!.valueOf() + form.duration).format('YYYY-MM-DD HH:mm')
    await createInterviewTask({
      startDate: start,
      endDate: end, 
      interviewType: form.interviewType,
      interviewAddress: form.interviewAddress,
      interviewer: form.interviewer
    })
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
    emit('update')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function createInterviewTask(form: any) {
    const params = {
      completeParentTask: false,
      isParentSuccessComplete: true,
      parentTaskId: parentTaskId.value,
      talentId: talentId.value,
      variables: form,
    }
  const res = await addInterviewTask(jobId.value, params)
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

</script>

<style lang="sass" scoped>

</style>