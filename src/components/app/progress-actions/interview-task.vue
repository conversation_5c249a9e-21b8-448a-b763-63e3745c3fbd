<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="面试结果反馈"
  :open="open"
  :destroyOnClose="true"
  okText="确定"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="是否是终面"
      name="isFinalInterview"
      :rules="[{ required: true, message: '请选择面试时间' }]"
    )
      a-radio-group(
        v-model:value="form.isFinalInterview"
        :options="[{ label: '是', value: '1' }, { label: '否', value: '0' }]"
      )

    a-form-item(
      label="面试结果"
      :rules="[{ required: true}]"
      name="interviewResult"
    )
      a-select(
        :options=`[
          { label: '通过', value: '通过' }, 
          { label: '淘汰', value: '淘汰' }, 
          { label: '候选人缺席', value: '候选人缺席' }, 
          { label: '面试官改期', value: '面试官改期' }
        ]`
        v-model:value="form.interviewResult"
      )

    a-form-item(
      label="面试内容"
      name="interviewContent"
      :rules="[{ required: true, message: '请选择面试方式' }]"
    )
      a-textarea(v-model:value="form.interviewContent")
      
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, addInterviewTask, updateInterviewTaskVariables } from '@/api/job'
import { onMounted } from 'vue'
import { Modal } from 'ant-design-vue'
import dayjs, { Dayjs } from 'dayjs'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean, parentTaskId: string }>()
const emit = defineEmits(['update:open', 'update'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const talentId = toRef(props, 'talentId')
const parentTaskId = toRef(props, 'parentTaskId')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  isFinalInterview: '0',
  interviewResult: '',
  interviewContent: ''
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await createInterviewTask(form)
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
    emit('update')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function createInterviewTask(form: any) {
  const parentTaskVariables =
    (form.isFinalInterview && form.interviewResult == '通过')
      ? { finalInterviewResult: true }
      : {}

  const params = {
    parentTaskId: parentTaskId.value,
    completeParentTask: false,
    isParentSuccessComplete: false,
    parentTaskVariables,
    variables: {
      isFinalInterview: form.isFinalInterview,
      interviewResult: form.interviewResult,
      interviewContent: form.interviewContent
    }
  }
  const res = await updateInterviewTaskVariables(jobId.value, taskId.value, params)
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

</script>

<style lang="sass" scoped>

</style>