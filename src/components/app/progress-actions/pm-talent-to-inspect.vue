<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="已完成背调"
  :open="open"
  :destroyOnClose="true"
  okText="背调完成"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="背调信息"
      name="inspect"
      :rules="[{ required: true, message: '请输入背调信息' }]"
    )
      a-textarea(
        v-model:value="form.inspect"
        :rows="4"
        placeholder="请输入背调信息"
        :auto-size="{ minRows: 4, maxRows: 4 }"
      )
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, getJobRequirementTask, updateTaskVariables } from '@/api/job'
import { onMounted } from 'vue'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean, update?: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const isUpdate = toRef(props, 'update')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  inspect: '',
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await finishTask(form)
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }
  if (isUpdate.value === true) {
    await updateTaskVariables(jobId.value, taskId.value, form)
  } else {
    const res = await finishJobTask(jobId.value, taskId.value, params)
  }
}

async function initForm(jobId: number, taskId: string) {
  try {
    const res = await getJobRequirementTask(jobId, taskId)

    // 所有值在提交给后端后，被保存为字符串，所以需要前端单独处理
    const localVariables = res.data.localVariables

    form.inspect = localVariables.inspect
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

onMounted(()=>{
  if (isUpdate.value) {
    initForm(jobId.value!, taskId.value!)
  }
})

</script>

<style lang="sass" scoped>

</style>