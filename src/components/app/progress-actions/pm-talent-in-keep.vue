<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="已过保"
  :open="open"
  :destroyOnClose="true"
  okText="确定"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-form(ref="formInstance" :model="form" layout="vertical")
    a-form-item(
      label="过保时间"
      name="overInsuranceDate"
      :rules="[{ required: true, message: '请选择过保时间。' }]"
    )
      a-date-picker(
        v-model:value="form.overInsuranceDate"
        style="width: 100%"
      )
</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask } from '@/api/job'
import { onMounted } from 'vue'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false
})

const form = reactive({
  overInsuranceDate: '',
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await finishTask(form)
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, {
      overInsuranceDate: form.overInsuranceDate.format('YYYY-MM-DD')
    }),
    completeAssigneeId: userStore.id
  }
  const res = await finishJobTask(jobId.value, taskId.value, params)
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

onMounted(()=>{
  console.log(props)
})

</script>

<style lang="sass" scoped>

</style>