<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="淘汰候选人"
  :open="open"
  :destroyOnClose="true"
  okText="淘汰"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  a-spin(:spinning="status.init")
    a-form(ref="formInstance" :model="form" layout="vertical")
      a-form-item(
        label="发起方"
        name="obsoleteInitiator"
        :rules="[{ required: true, message: '请选择发起方' }]"
      )
        a-radio-group(
          v-model:value="form.obsoleteInitiator"
          :options="[{label: '候选人放弃', value: '1'}, {label: '客户淘汰', value: '2'}]"
        )

      a-form-item(
        label="后续操作"
        name="obsoleteFollowUpAction"
        :rules="[{ required: true, message: '请选择后续操作' }]"
        v-if="status.showFollowUpAction"
      )
        a-radio-group(
          v-model:value="form.obsoleteFollowUpAction"
          :options="[{label: '补人', value: '1'}, {label: '退款', value: '2'}, {label: '不补不退', value: '3'}]"
        )

      a-form-item(
        label="淘汰原因"
        name="obsoleteReason"
        :rules="[{ required: true, message: '请输入淘汰原因' }]"
      )
        a-textarea(
          v-model:value="form.obsoleteReason"
          :rows="4"
          placeholder="请输入淘汰原因"
          :auto-size="{ minRows: 4, maxRows: 4 }"
        )
</template>

<!-- 一个是单选按钮名称是发起方，两个选项分别是，候选人放弃和客户淘汰。另一个也是单选按钮，名称是后续操作，三个选项分别是，补人，退款和不补不退 -->

<script lang="ts" setup>
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask, getJobRequirementTask } from '@/api/job'
import { onMounted, watch, reactive, ref, toRef } from 'vue'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const formInstance = ref()
const userStore = useUserStore()

const status = reactive({
  loading: false,
  init: false,
  showFollowUpAction: false
})

const form = reactive({
  obsoleteReason: '',
  obsoleteInitiator: '',
  obsoleteFollowUpAction: ''
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    await finishTask(form)
    message.success('操作成功！')
    formInstance.value.resetFields()
    emit('update:open', false)
  } catch (err: any) {
    if (err.errorFields && err.errorFields.length > 0) {
      message.error(err.errorFields[0].errors.join(','))
    } else {
      message.error(err.message)
    }
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: false,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }
  const res = await finishJobTask(jobId.value, taskId.value, params)
}

function handleClose() {
  formInstance.value.resetFields()
  emit('update:open', false)
}

async function initForm(jobId:number, taskId:string) {
  status.init = true
  try {
    const res = await getJobRequirementTask(jobId, taskId)
    const taskKey = res.data.taskDefinitionKey
    if (['position_pm_talent_in_keep'].includes(taskKey)) {
      status.showFollowUpAction = true
    } else {
      status.showFollowUpAction = false
    }
  } catch (err:any) {
    message.error(err.message)
  }
  status.init = false
}

onMounted(async ()=>{
  await initForm(jobId.value, taskId.value)
  formInstance.value.resetFields()
})

watch(()=> props.taskId, async (val)=>{
  await initForm(jobId.value, val)
  formInstance.value.resetFields()
})

</script>

<style lang="sass" scoped>

</style>