<template lang="pug">
a-modal(
  @ok="handleConfirm"
  title="推荐给客户"
  :open="open"
  :destroyOnClose="true"
  okText="确认"
  :confirmLoading="status.loading"
  @cancel="() => handleClose()"
)
  p 确认要将该简历推荐给客户么？

</template>

<script lang="ts" setup>
import { reactive, ref, toRef } from 'vue'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { finishJobTask } from '@/api/job'
import { onMounted } from 'vue'
import { Modal } from 'ant-design-vue'

const props = defineProps<{ talentId: number, taskId: string, jobId: number, open: boolean }>()
const emit = defineEmits(['update:open'])
const open = toRef(props, 'open')
const jobId = toRef(props, 'jobId')
const taskId = toRef(props, 'taskId')
const userStore = useUserStore()

const status = reactive({
  loading: false
})

async function handleConfirm() {
  status.loading = true
  try {
    await finishTask({})
    message.success('推荐成功！')
    emit('update:open', false)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function finishTask(form: any) {
  const params = {
    isSuccessComplete: true,
    variables: Object.assign({}, form),
    completeAssigneeId: userStore.id
  }
  const res = await finishJobTask(jobId.value, taskId.value, params)
}

function handleClose() {
  emit('update:open', false)
}

</script>

<style lang="sass" scoped>

</style>