<script lang=ts setup>
import { defineComponent, reactive, toRaw, ref } from 'vue';
import type { UnwrapRef } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';
import { Modal, message } from 'ant-design-vue';

import { getCurrencyList } from '@/api/dictionary';
import { getEntityList, setCreateInvoice, getCalculatedInvoiceAmount, getJobRequirementFinanceConfig } from '@/api/finance';
import { getProcessInstances, getJobRequireSearch } from '@/api/job';
import { onMounted } from 'vue';

const emit = defineEmits(['close', 'success'])
const status = reactive({ loading: false, showNoContract: false })

const props = defineProps<{ processName: string, id: number, customerId: number }>()

interface TDataForm {
  processName: string;
  financeAccountId: string | number
  currencyType: string | number
  customerName: string
  customerTaxNumber: string
  customerAddress: string
  taxRate: string
  dueDate: string | Dayjs
  grandTotal: number
  taxAmount: number
  totalAmount: number
  invoiceType: number
  invoiceAction: number
  invoiceDetails: any[]
  customerBankFullName: string
  customerBankAccount: string
  customerPhoneNumber: string,
  remark: string,
  postInfo: string
}

const visible = ref(true)
const dataFormRef = ref()
const dataForm: UnwrapRef<TDataForm> = reactive({
  processName: props.processName || '',
  financeAccountId: '',
  customerBankFullName: '',
  customerBankAccount: '',
  customerPhoneNumber: '',
  currencyType: '',
  customerName: '',
  customerTaxNumber: '',
  customerAddress: '',
  taxRate: '',
  dueDate: '',
  grandTotal: 0,
  taxAmount: 0,
  totalAmount: 0,
  invoiceAction: 0,
  invoiceType: 1,
  remark: '',
  postInfo: '',
  invoiceDetails: [{
    realName: '',
    grandTotal: '',
    processInstanceId: '',
    currencyType: '',
    salary: '',
    salaryUnit: '',
    totalAmount: 0,
    paymentCycle: [],
    selected: false
  }]
});
const labelCol = { style: { width: '110px' } }

const processList = ref([] as any[])
const filterOption = async (value: string) => {
  if (props.id) return
  const p = {
    name: value,
    queries: [],
    status: 1,
    size: 20,
    current: 1,
    customerId: props.customerId
  }

  const res = await getJobRequireSearch(p)
  const { jobRequirements } = res.data
  const list = jobRequirements.map((item: any) => {
    return {
      value: item.id + '',
      label: item.processName,
    }
  })

  processList.value = list
}

function handleInvoiceTypeChange(value: any) {
  if ([1, 2].includes(dataForm.invoiceType)) {
    dataForm.currencyType = 0
    handleChangeCurrency(0)
  }
}

filterOption('')

const handleChangeProcess = (val: string) => {
  _getProcessInstances()
}

const talentList = ref([] as any[])
const _getProcessInstances = async () => {
  if (!dataForm.processName) return

  const id = props.id || dataForm.processName
  const res = await getProcessInstances(Number(id))

  const list = res.data.map((item: any) => {
    return {
      variables: item.process.variables,
      realName: item.talent.realName,
      talentId: item.talent.id,
      processInstanceId: item.process.processInstanceId
    }
  })

  talentList.value = list
}
_getProcessInstances()


const currencyList = ref([] as any[])
const _getCurrencyList = async () => {
  const res = await getCurrencyList()
  currencyList.value = res.data
}
_getCurrencyList()


const entityList = ref([] as any[])
const _getEntityList = async () => {
  const res = await getEntityList()

  entityList.value = res.data
}
_getEntityList()

const financeConfig = ref<any>({})
const _getFinanceConfig = async () => {
  try {
    const res = await getJobRequirementFinanceConfig(props.id)
    dataForm.customerBankFullName = res.data.invoiceBankName
    dataForm.customerBankAccount = res.data.invoiceBankAccount
    dataForm.customerPhoneNumber = res.data.invoicePhoneNumber
    dataForm.customerName = res.data.invoiceTitle
    dataForm.currencyType = res.data.currencyType
    dataForm.taxRate = res.data.invoiceTaxRate
    dataForm.customerTaxNumber = res.data.invoiceTaxNumber
    dataForm.customerAddress = res.data.invoiceAddress
    dataForm.financeAccountId = res.data.financeAccountId
    dataForm.invoiceType = res.data.invoiceName
  } catch (err: any) {
    if (err.message === '无法找到对应的财务配置信息') {
      Modal.error({
        title: '提示',
        content: '无法找到对应的财务配置信息，请先配置财务信息',
        onOk: () => {
          emit('close')
        }
      })
    }
    message.error(err.message)
  }
}

const handleClickAdd = () => {
  dataForm.invoiceDetails.push({
    realName: '',
    grandTotal: '',
    processInstanceId: '',
    currencyType: '',
    salary: '',
    salaryUnit: '',
    totalAmount: 0,
    paymentCycle: [],
    selected: false
  })
}

const handleClickDel = (index: number) => {
  dataForm.invoiceDetails.splice(index, 1);
  handleCountGrandTotal()
}

const handleChangeCurrency = (val: number) => {
  dataForm.invoiceDetails.forEach((detail, index) => {
    const cur = talentList.value.find((item: any) => item.processInstanceId === detail.processInstanceId)
    if (cur !== undefined) {
      getPaymentCycle(cur, index)
    }
  })
}

const handleChangeRealName = (val: string, item: any, index: number) => {
  const cur = talentList.value.find((item: any) => item.processInstanceId === val)

  dataForm.invoiceDetails[index].currencyType = cur.variables.currencyType || 'RMB'
  dataForm.invoiceDetails[index].salary = cur.variables.salary
  dataForm.invoiceDetails[index].processInstanceId = val
  dataForm.invoiceDetails[index].paymentCycle = [];
  getPaymentCycle(cur, index)
}

const getPaymentCycle = async (talent: any, index: number) => {
  let res: any
  try {
    res = await getCalculatedInvoiceAmount(talent.processInstanceId, dataForm.currencyType)
  } catch (err: any) {
    console.log(err.message)
    if (err.message === '没有找到与薪资匹配的财务配置') {
      message.error('没有找到对应的佣金阶梯配置，请确认「合同配置」和候选人的「OFFER金额」')
      return
    } else {
      message.error(err.message)
      return
    }
  }
  dataForm.invoiceDetails[index].paymentCycle = [];
  let totalAmount = 0;
  for (let i = 0; i < res.data.paymentAmounts.length; i++) {
    const paymentAmountItem = res.data.paymentAmounts[i]
    let remarks = ''
    if (paymentAmountItem.isExpired && paymentAmountItem.appliedInvoiceAmount === 0) {
      remarks = '（可开票）'
      totalAmount += paymentAmountItem.expectedPayAmount;
    }
    if (paymentAmountItem.appliedInvoiceAmount > 0) {
      remarks = '（已开票）'
    }
    const cycleConfig = {
      name: res.data.paymentAmounts[i].cycleName,
      amount: res.data.paymentAmounts[i].expectedPayAmount / 100,
      remarks: remarks
    }
    dataForm.invoiceDetails[index].paymentCycle.push(cycleConfig)
  }
  dataForm.invoiceDetails[index].paymentCycle.push({
    name: '全款',
    amount: res.data.totalAmount / 100
  })
  dataForm.invoiceDetails[index].grandTotal = totalAmount / 100
  handleChangeGrandTotal(totalAmount / 100, talent, index)
}

const handleChangeGrandTotal = (val: any, item: any, index: number) => {
  handleCountGrandTotal()
}

const handleCountGrandTotal = () => {
  let grandTotal = 0
  dataForm.invoiceDetails.forEach((item: any) => {
    grandTotal = grandTotal + Number(item.grandTotal)
  })
  const totalAmount = grandTotal / (1 + Number(dataForm.taxRate) / 100)
  dataForm.totalAmount = Number(totalAmount.toFixed(2))
  dataForm.grandTotal = Number(grandTotal.toFixed(2))
  dataForm.taxAmount = Number((grandTotal - dataForm.totalAmount).toFixed(2))
}

const handleOk = async () => {
  if (!dataFormRef.value) return

  status.loading = true
  try {
    await dataFormRef.value.validate()
    const invoiceDetails = dataForm.invoiceDetails.map((item: any) => {
      const totalAmount = Number(item.grandTotal) / (1 + Number(dataForm.taxRate) / 100)

      return {
        grandTotal: Number(item.grandTotal) * 100,
        jobRequirementId: props.id || dataForm.processName,
        processInstanceId: item.processInstanceId,
        taxRate: Number(dataForm.taxRate),
        totalAmount: Number(totalAmount.toFixed(2)) * 100
      }
    })

    const params = {
      currencyType: dataForm.currencyType,
      financeAccountId: dataForm.financeAccountId,
      customerAddress: dataForm.customerAddress,
      customerName: dataForm.customerName,
      customerTaxNumber: dataForm.customerTaxNumber,
      dueDate: dayjs(dataForm.dueDate).format('YYYY-MM-DD'),
      grandTotal: dataForm.grandTotal * 100,
      taxAmount: dataForm.taxAmount * 100,
      totalAmount: dataForm.totalAmount * 100,
      taxRate: Number(dataForm.taxRate),
      invoiceType: dataForm.invoiceType,
      customerBankFullName: dataForm.customerBankFullName,
      customerBankAccount: dataForm.customerBankAccount,
      customerPhoneNumber: dataForm.customerPhoneNumber,
      invoiceAction: dataForm.invoiceAction,
      remark: dataForm.remark,
      postInfo: dataForm.postInfo,
      invoiceDetails
    }
    await setCreateInvoice(props.customerId + '', params)
    message.success('申请成功');
    emit('success')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const handleCancel = () => {
  emit('close')
}

onMounted(() => {
  _getFinanceConfig()
})

</script>

<template lang=pug>

template(v-if="status.showNoContract")
a-modal(
  v-model:open="visible" 
  title="申请开票" 
  @cancel="handleCancel"
  @ok="handleOk"
  :width="900"
  :maskClosable="false"
  :confirmLoading="status.loading"
)
  a-form(:model="dataForm" layout="vertical" :label-col="labelCol" ref="dataFormRef")
    a-row(:gutter="24")
      a-col(:span="8")
        a-form-item(
          label="项目"
          name="processName"
          :rules="[{ required: true, message: '请选择项目' }]"
        )
          a-input(v-if="props.id" v-model:value="dataForm.processName" disabled)
          a-select(
            v-else
            v-model:value="dataForm.processName"
            show-search
            placeholder=" "
            style="width: 100%"
            :options="processList"
            :filter-option="false"
            @search="filterOption"
            @change="handleChangeProcess"
          )

      a-col(:span="8")
        a-form-item(
          label="开票方"
          name="financeAccountId"
          :rules="[{ required: true, message: '请选择开票方' }]"
        )
          a-select(v-model:value="dataForm.financeAccountId")
            a-select-option(v-for="item in entityList" :key="item.id" :value="item.id") {{ item.companyFullName }}


      a-col(:span="8")
        a-form-item(
          label="票据类型"
          name="invoiceType"
          :rules="[{ required: true, message: '请选择票据类型' }]"
        )
          a-radio-group(v-model:value="dataForm.invoiceType" button-style="solid" @change="handleInvoiceTypeChange")
            a-radio-button(:value="0") Invoice
            a-radio-button(:value="1") 普通发票
            a-radio-button(:value="2") 专用发票

      a-col(:span="8")
        a-form-item(
          label="抬头"
          name="customerName"
          :rules="[{ required: true, message: '请输入抬头' }]"
        )
          a-input(v-model:value="dataForm.customerName" placeholder="请输入抬头")

      a-col(:span="8")
        a-form-item(
          label="税号"
          name="customerTaxNumber"
          :rules="[{ required: dataForm.invoiceType !== 0, message: '请输入税号' }]"
        )
          a-input(v-model:value="dataForm.customerTaxNumber" placeholder="请输入税号")

      a-col(:span="8")
        a-form-item(
          label="地址"
          name="customerAddress"
          :rules="[{ required: dataForm.invoiceType === 2, message: '请输入地址' }]"
        )
          a-input(v-model:value="dataForm.customerAddress")

      a-col(:span="8")
        a-form-item(
          label="税率"
          name="taxRate"
          :rules="[{ required: true, message: '请输入税率' }]"
        )
          a-input-number(v-model:value="dataForm.taxRate" style="width: 100%" :min="0" :max="100")
            template(#addonAfter) %

      a-col(:span="8")
        a-form-item(
          label="预计收款日期"
          name="dueDate"
          :rules="[{ required: true, message: '请选择收款日期' }]"
        )
          a-date-picker(v-model:value="dataForm.dueDate" style="width: 100%")

      a-col(:span="8")
        a-form-item(
          label="货币"
          name="currencyType"
          :rules="[{ required: true, message: '请选择货币' }]"
        )
          a-select(v-model:value="dataForm.currencyType" @change="(val) => handleChangeCurrency(val)" :disabled="[1,2].includes(dataForm.invoiceType)")
            a-select-option(v-for="item in currencyList" :key="item.id" :value="item.id") {{ item.name }}

      template(v-if="dataForm.invoiceType === 2")
        a-col(:span="8")
          a-form-item(
            :rules= "{required: true, message: '请输入电话号码'}"
            :name="'customerPhoneNumber'"
            label="电话号码"
          )
            a-input(v-model:value="dataForm.customerPhoneNumber" style="width: 100%" :min="0" :max="100" :controls="false")

        a-col(:span="8")
          a-form-item(
            :rules= "{required: true, message: '请输入开户银行'}"
            :name="'customerBankFullName'"
            label="开户银行"
          )
            a-input(v-model:value="dataForm.customerBankFullName" style="width: 100%" :min="0" :max="100" :controls="false")

        a-col(:span="8")
          a-form-item(
            :rules= "{required: true, message: '请输入银行账号'}"
            :name="'customerBankAccount'"
            label="银行账号"
          )
            a-input(v-model:value="dataForm.customerBankAccount" style="width: 100%" :min="0" :max="100" :controls="false")

      template(v-if="true")
        a-col(:span="16")
          a-form-item(
            label="邮寄地址"
            name="postInfo"
            :rules="[{ required: true, message: '请输入邮寄地址' }]"
          )
            a-textarea(v-model:value="dataForm.postInfo" style="width: 100%")

        a-col(:span="8")
          a-form-item(
            label="票面备注"
            name="remark"
            :rules="[{ required: false, message: '请输入票面备注' }]"
          )
            a-textarea(v-model:value="dataForm.remark" style="width: 100%")

      a-col(:span="24")
        a-divider

    a-row(
      :gutter="24"
      v-for="(item, index) in dataForm.invoiceDetails"
      :key="item.id"
      align="middle"
    )
      a-col(:span="8")
        a-form-item(
          label="姓名"
          :name="['invoiceDetails', index, 'realName']"
          :rules="[{ required: true, message: '请选择候选人' }]"
          )
          a-select(v-model:value="item.realName" show-search @change="(val) => handleChangeRealName(val, item, index)")
            a-select-option(v-for="row in talentList" :key="row.processInstanceId" :value="row.processInstanceId") {{ row.realName }}

      a-col(:span="8")
        a-form-item(
          label="薪资"
          :name="['invoiceDetails', index, 'salary']"
          :rules="[{ required: true, message: '请输入薪资' }]"
          )
          a-input(v-model:value="item.salary" disabled)
            template(#addonAfter) {{ item.currencyType }}

      a-col(:span="5")
        a-form-item(
          label="开票金额(含税)"
          :name="['invoiceDetails', index, 'grandTotal']"
          :rules="[{ required: true, message: '请输入开票金额' }]"
          :labelCol={ style: { width: '120px' } }
          )
          a-popover(
            tigger="focus"
            title="开票信息"
          )
            template(#content)
              p(class="grand-popover-p" v-for="(cycleItem, index) in item.paymentCycle") {{ cycleItem.name }}: {{ cycleItem.amount }}
                span {{ cycleItem.remarks }}
            a-input-number(
              v-model:value="item.grandTotal"
              @change="(val) => handleChangeGrandTotal(val, item, index)" 
              :min="0"
              style="width: 100%"
            )

      a-col(:span="2")
        a-form-item(label="操作")
          a-space
            .pointer(v-show="dataForm.invoiceDetails.length > 1" @click="handleClickDel(index)")
              MinusCircleOutlined
            .pointer(v-show="dataForm.invoiceDetails.length === (index + 1)" @click="handleClickAdd")
              PlusOutlined

    a-row(:gutter="24" align="middle")
      a-col(:span="8")
        a-space(:size="12")
          span 总金额(不含税)
          span {{ dataForm.totalAmount }}
      a-col(:span="8")
        a-space(:size="12")
          span 税率
          span {{ dataForm.taxRate }} %
      a-col(:span="8")
        a-space(:size="12")
          span 票面总额(含税)
          span {{ dataForm.grandTotal }}

</template>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.grand-popover-p {
  line-height: 25px;
  margin: 0;

  span {
    color: #FF6600;
  }
}
</style>