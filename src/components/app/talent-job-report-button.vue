<template lang="pug">
.talent-job-report-button
  a-row(:gutter="[16, 16]" v-if="report")
    a-col(:span="24")
      a-button(
        block ghost 
        type="primary" 
        @click="handleViewReport"
      ) 查看推荐报告

  a-row(v-else)
    a-col(:span="24")
      a-button(
        block
        type="primary" 
        @click="() => handleUpdateReport()" 
        :loading="status.loading" 
      ) {{ '创建推荐报告' }}

  a-row(v-if="matchScore" :gutter="[16, 16]")
    a-col(:span="24")
      .match-score-container
        .score-title AI匹配分数
        .score-value {{ matchScore.final }}分
        .score-description 基于AI算法对人才与职位要求的匹配度评估
        .score-details
          .score-item
            .item-label 核心能力
            .item-value {{ matchScore.核心能力 }}
          .score-item
            .item-label 硬性条件
            .item-value {{ matchScore.硬性条件 }}
          .score-item
            .item-label 软性素质
            .item-value {{ matchScore.软性素质 }}
          .score-item(v-if="matchScore.附加分 !== '+0'")
            .item-label 附加分
            .item-value {{ matchScore.附加分 }}
            .item-details(v-if="matchScore.附加分详情 && matchScore.附加分详情.length > 0")
              .detail-item(v-for="(detail, index) in matchScore.附加分详情" :key="index") {{ detail }}

  a-row(v-else-if="status.loadingScore" :gutter="[16, 16]")
    a-col(:span="24")
      .match-score-container
        .score-title AI匹配分数
        .loading-container
          a-spin
          .loading-text 正在计算匹配分数...

  a-drawer(title="推荐报告内容" v-model:open="status.showReport" :destroyOnClose="true" :width="720" :bodyStyle="{padding: 0}")
    .html-container
      div(v-html="report?.htmlContent")

    .drawer-action
      a-space(:size="16")
        a-button(@click="status.showReport = false") 取消
        a-button(@click="handleUpdateReport") 编辑报告
          template(#icon)
            EditOutlined
        a-button(type="primary" @click="handleDownloadPdf") 下载PDF
          template(#icon)
            CloudDownloadOutlined
</template>

<script lang="ts" setup>
import { getTalentReports } from '@/api/talent/talent'
import { getTalentMatchingParams, getTalentMatchScore, runWorkflow, getCozeAppConfig } from '@/api/ai'
import { message } from 'ant-design-vue'
import {CloudDownloadOutlined, EditOutlined} from '@ant-design/icons-vue'
import {reactive, onMounted, ref, toRef, watch } from 'vue'
import router from '@/router'
import { saveTalentReportToFile } from '@/api/talent/talent'

const props = defineProps<{
  talentId: number,
  processInstanceId: string,
  jobRequirementId: number
}>()
const talentId = toRef(props, 'talentId')
const processInstanceId = toRef(props, 'processInstanceId')
const jobRequirementId = toRef(props, 'jobRequirementId')
const reports = ref<any[]>([])
const status = reactive({ 
  loading: false, 
  showReport: false,
  loadingScore: false 
})
const buttonText = ref('')
const emit = defineEmits(['close'])
const report = ref<any>()
const matchScore = ref<any>()

const chat = reactive<any>({
  config: {}
})

const conversationId = ref(0)

async function fetchTalentJobReports(talentId: number) {
  status.loading = true
  try {
    const talentJobReports = await getTalentReports(talentId)
    reports.value = talentJobReports.data.talentReportItems
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function fetchMatchScore() {
  status.loadingScore = true
  try {
    // First get matching parameters
    const params = await getTalentMatchingParams(jobRequirementId.value, talentId.value)
    
    // Then get the match score
    const talentScore = await runWorkflow(
      chat.config.appId,
      chat.config.scoreWorkFlowId,
      {
        conversationId: conversationId.value,
        params: params.data
      }
    )
    const scoreObj = JSON.parse(talentScore.data)
    // Remove ```json and ``` symbols if they exist
    const cleanScore = scoreObj.score.replace(/```json\n?|\n?```/g, '')
    matchScore.value = JSON.parse(cleanScore)
  } catch (err: any) {
    message.error('获取AI匹配分数失败: ' + err.message)
  } finally {
    status.loadingScore = false
  }
}

function renderReportButton(processInstanceId: string) {
  try {
    const reportItem = reports.value.find(item => item.processInstanceId === processInstanceId)
    report.value = reportItem
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleUpdateReport() {
  router.push({
    path: `/talent/${talentId.value}/job/${jobRequirementId.value}/process/${processInstanceId.value}/report`
  })
}

function handleViewReport() {
  status.showReport = true
}

async function handleDownloadPdf() {
  window.open(report.value.fileUrl)
}

onMounted(async () => {
  const appConf = await getCozeAppConfig()
  chat.config = appConf.data
  await fetchTalentJobReports(talentId.value)
  renderReportButton(processInstanceId.value)
  await fetchMatchScore()
})

watch(processInstanceId, async () => {
  renderReportButton(processInstanceId.value)
  await fetchMatchScore()
})
</script>

<style lang="sass" scoped>
.talent-job-report-button
  margin-top: 12px

.html-container
  padding: 16px

.drawer-action
  border-top: 1px solid #f0f0f0
  background-color: #fff
  padding: 16px
  text-align: right
  position: absolute
  bottom: 0
  left: 0
  width: 100%
  box-sizing: border-box

.match-score-container
  margin-top: 16px
  padding: 16px
  background-color: #f5f5f5
  border-radius: 4px
  text-align: center

  .score-title
    font-size: 14px
    color: #666
    margin-bottom: 8px

  .score-value
    font-size: 24px
    font-weight: bold
    color: #1890ff
    margin-bottom: 8px

  .score-description
    font-size: 12px
    color: #999
    margin-bottom: 12px

  .score-details
    text-align: left
    margin-top: 12px
    padding-top: 12px
    border-top: 1px solid #e8e8e8

    .score-item
      margin-bottom: 8px
      font-size: 12px

      .item-label
        color: #666
        margin-bottom: 4px

      .item-value
        color: #333
        line-height: 1.4

      .item-details
        margin-top: 4px
        padding-left: 12px

        .detail-item
          color: #666
          font-size: 11px
          line-height: 1.4

  .loading-container
    display: flex
    flex-direction: column
    align-items: center
    padding: 16px 0

    .loading-text
      margin-top: 8px
      font-size: 12px
      color: #999
</style>