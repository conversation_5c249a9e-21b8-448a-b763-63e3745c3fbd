<template lang="pug">
.create-payment-modal
  a-modal(
    :open="open" 
    title="创建回款单"
    width="90%"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="()=>{emit('update:open', false)}"
    @ok="()=>handleConfirm()"
  )
    a-form(:model="statementForm" ref="statementFormInstance" layout="vertical")
      a-row(:gutter="[16,0]")
        a-col(:span="8")
          a-form-item(
            label="交易流水号"
            :rules="[{ required: true, message: '请输入交易流水号' }]"
            name="paySerialNumber"
          )
            a-input(v-model:value="statementForm.paySerialNumber")

        a-col(:span="8")
          a-form-item(
            label="付款主体"
            name="payAccountName"
            :rules="[{ required: true, message: '请输入付款主体' }]"
          )
            a-input(v-model:value="statementForm.payAccountName")

        a-col(:span="8")
          a-form-item(
            label="收款账户"
            name="financeAccountId"
            :rules="[{ required: true, message: '请选择收款账户' }]"
          )
            a-select(
              style="width: 100%"
              option-label-prop="label"
              v-model:value="statementForm.financeAccountId"
            )
              template(v-for="(item, index) in options.entityList")
                a-select-option(:value="item.id" :label="`${item.companyFullName} - ${item.bankFullName}`")
                  .companyEntity {{ item.companyFullName }}
                  .compnayAccount {{ item.bankFullName }}

        a-col(:span="8")
          a-form-item(
            label="收款金额"
            name="totalAmount"
            :rules="[{required: true, message:'请填写收款金额'}]"
          )
            a-input-number(
              style="width: 100%"
              v-model:value="statementForm.totalAmount"
              @change="handleStatementAmountChange"
              :step="0.01"
            )
              template(#addonAfter)
                a-select(
                  style="width: 100px"
                  v-model:value="statementForm.currencyType"
                  :options="options.currencyType"
                  @change="handleCurrentTypeChange"
                )

        a-col(:span="8")
          a-form-item(
            label="收款日期" 
            :rules="[{required:true, message:'请选择付款日期'}]"
          )
            a-date-picker(
              style="width: 100%"
              v-model:value="dates.paydate"
            )

        a-col(:span="8")
          a-form-item(
            label="付款方式"
            name="payMethod"
            :rules="[{required:true, message:'请选择付款日期'}]"
          )
            a-select(
              :options="options.paymentMethod"
              v-model:value="statementForm.payMethod"
            )

        a-col(:span="24")
          a-table(
            :columns="formInvoiceListColumn"
            :dataSource="dataList"
            :pagination="false"
            :defaultExpandAllRows="true"
            :showExpandColumn="true"
            size="small"
          )

            template(#bodyCell="{ column, record, index }")
              template(v-if="column.key === 'grandTotal'")
                span {{ (record.invoice.grandTotal / 100).toFixed(2) }} {{ record.invoice.currencyTypeKey }}

              template(v-if="column.key === 'remainingAmount'")
                span {{ (record.offer.remainingAmount / 100).toFixed(2) }} {{ record.invoice.currencyTypeKey }}

              template(v-if="column.key === 'allocate'")
                a-input-number(v-model:value="statementDetails[index].totalAmount" :step="0.01")
                  template(#addonAfter v-if="selectedCurrencyType")
                    span {{ selectedCurrencyType }}

            template(#summary)
              a-table-summary-row 
                a-table-summary-cell
                a-table-summary-cell
                a-table-summary-cell
                a-table-summary-cell
                a-table-summary-cell 
                a-table-summary-cell 
                a-table-summary-cell 总计: {{ totalClaim }} {{ selectedCurrencyType }}
                a-table-summary-cell

</template>

<script lang="ts" setup>
import { defineComponent, reactive, toRaw, ref, toRef } from 'vue'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import dayjs, { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'

import { getCurrencyList, getPayMethodList, getPaymentMethodList } from '@/api/dictionary'
import { createStatement, getEntityList, setReceivePayment } from '@/api/finance'
import { getProcessInstances, getJobRequireSearch } from '@/api/job'
import { getDateRanges } from '@/utils/util'
import { onMounted, watch } from 'vue'
import { computed } from 'vue'

const emit = defineEmits(['update:open', 'update'])
const props = defineProps<{ open: boolean, invoiceList: any[] }>()
const open = toRef(props, 'open')
const invoiceList = toRef(props, 'invoiceList')

const formInvoiceListColumn = [
  { title: "发票抬头", dataIndex: ['invoice', 'customerName'], key: "customerName", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  { title: "开票主体", dataIndex: ['invoice', 'vendorName'], key: "vendorName", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  { title: "开票金额", dataIndex: ['invoice', 'totalAmount'], key: "grandTotal", customCell: (cell: any) => ({ rowSpan: cell.span }) },
  {
    title: "OFFER", key: 'offer', children: [
      { title: '职位', dataIndex: ['offer', 'positionTitle'], key: 'positionTitle' },
      { title: '姓名', dataIndex: ['offer', 'talentRealName'], key: 'talentRealName' },
      { title: '待收', dataIndex: ['offer', 'remainingAmount'], key: 'remainingAmount' },
      { title: '实收', key: 'allocate' },
    ]
  },
  { title: "发起人", dataIndex: ['invoice', 'applyUserRealName'], key: "applyUserRealName", width: 100, customCell: (cell: any) => ({ rowSpan: cell.span }) },
]

const options = reactive({
  ranges: getDateRanges(),
  customer: [] as any[],
  paymentMethod: [] as any[],
  currencyType: [] as any[],
  entityList: [] as any[],
  invoiceStatus: [
    { label: '未审核', value: 0 },
    { label: '通过', value: 1 },
    { label: '拒绝', value: 2 },
  ],
  invoiceType: [
    { label: 'INVOICE', value: 1 },
    { label: '普通发票', value: 2 },
    { label: '增值税专用发票', value: 3 },
  ]
})

const dataList = ref<any[]>([])

const totalClaim = computed(()=>{
  let total = 0
  statementDetails.value.forEach((item)=>{
    total += item.totalAmount
  })
  return total
})

const totalRemaining = computed(()=>{
  let total = 0
  dataList.value.forEach((item) => {
    total += item.offer.remainingAmount
  })
  return total
})

async function initDict() {
  try {
    const [paymentMethodRes, entityRes, currencyRes] = await Promise.all([
      getPaymentMethodList(), getEntityList(), getCurrencyList()
    ])
    options.paymentMethod = paymentMethodRes.data.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
    options.entityList = entityRes.data
    options.currencyType = currencyRes.data.map((item: any) => ({
      label: item.name,
      key: item.key,
      value: item.id
    }))
  } catch (err: any) {
    message.error(err.message)
  }
}

async function processInvoiceList(list: any) {
  dataList.value = []

  if (list && list.length) {
    statementForm.customerId = list[0].customerId
    statementForm.currencyType = list[0].currencyType
    const currencyOption = options.currencyType.find((item: any) => item.value === list[0].currencyType)
    selectedCurrencyType.value = `${currencyOption.label} ${currencyOption.key}`
  }

  list.forEach((invoice: any) => {
    const { financeInvoiceDetails } = invoice
    financeInvoiceDetails.forEach((detail: any, index: number) => {
      dataList.value.push({
        offer: detail,
        invoice: invoice,
        span: index === 0 ? financeInvoiceDetails.length : 0
      })

      statementDetails.value.push({
        invoiceDetailId: detail.id,
        invoiceId: invoice.id,
        totalAmount: 0
      })
    })
  })
}

function handleStatementAmountChange(value: any) {
  let total = value
  dataList.value.forEach((item: any, index:any) => {
    if (total <= 0) {
      statementDetails.value[index].totalAmount = 0
      return
    }
    const value = Math.min(total, item.offer.remainingAmount / 100)
    statementDetails.value[index].totalAmount = value
    total -= value
  })
}

function handleCurrentTypeChange(e: any, option: any) {
  selectedCurrencyType.value = `${option.label} ${option.key}`
}

const dates = reactive({
  paydate: dayjs()
})

const selectedCurrencyType = ref('')
const statementFormInstance = ref()

const statementForm = reactive<{
  currencyType: number | null,
  customerId: number | null,
  financeAccountId: number | null,
  payAccountName: string,
  payMethod: number | null,
  paySerialNumber: string,
  totalAmount: number | null,
  payDate: string,
}>({
  currencyType: 0,
  customerId: null,
  financeAccountId: null,
  payAccountName: '',
  payMethod: 1,
  paySerialNumber: '',
  totalAmount: null,
  payDate: '',
})

const statementDetails = ref<StatementDetail[]>([])

type StatementDetail = {
  invoiceDetailId: number,
  invoiceId: number,
  totalAmount: number
}

async function handleConfirm() {
  try {
    await statementFormInstance.value.validate()

    const totalAmount = statementForm.totalAmount || 0
    if (totalClaim.value > totalAmount) throw new Error('offer分配总额不能大于收款金额。')
    if (totalClaim.value == 0) throw new Error('不能确认分配金额总计为0的回款。')

    const params = {
      ...statementForm,
      payDate: dates.paydate.format('YYYY-MM-DD'),
      totalAmount: statementForm.totalAmount! * 100,
      // 这里将金额转换为分后提交
      statementDetails: statementDetails.value.map(item => {
        return {
          ...item,
          totalAmount: item.totalAmount * 100
        }
      })
    }

    const res = await createStatement(params)
    message.success('创建成功！')
    emit('update')
    
  } catch (err: any) {
    if (err.errorFields) {
      message.error(err.errorFields[0].errors)
    } else {
      message.error(err.message)
    }
  }
}

onMounted(async () => {
  await initDict()
  processInvoiceList(invoiceList.value)
})

</script>

<style lang="sass" scoped>
.create-payment-form
  padding-bottom: 56px
  height: 100%
  position: relative
  box-sizing: border-box

  .form-body
    padding: 20px

  .form-action 
    position: absolute
    bottom: 0
    background-color: #fff
    border-top: 1px solid #f0f0f0
    width: 100%
    padding: 12px 24px
    text-align: right
    box-sizing: border-box
</style>