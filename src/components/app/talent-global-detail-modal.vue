<template lang="pug">
.talent-detail-modal
  a-spin(:spinning="status.detailLoading")
    a-row(:gutter="24")
      a-col(:span="24")
        .talent-detail-container
          TalentGlobalDetail(:talent="talent" :attachments="attachments")
          .talent-action
            a-space(:size="24")
              a-button(@click="addToMyTalent" type="primary") 添加到我的人才
              a-button(@click="handleSaveAndJoinJob" type="primary") 添加人才并加入职位
  JobSelector(v-model:visible="status.showJobSelector" :jobId="props.jobId" @select="handleSelectJob" :loading="status.joinJob" :multi="false")
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRefs, watch } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { unionSearchAddGlobalTalent, unionSearchTalentDetail } from '@/api/talent/talent'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/user.store'
import tracker from '@/utils/tracker'
import { getResumeAttachmentsByFileIds } from '@/api/resume'
import JobSelector from '@/components/app/job-selector.vue'

// 人才详情所需要的组件
import TalentGlobalDetail from '@/components/app/talent-global-detail.vue'
import { setSearchBury } from '@/api/track'
import { addTalentToJobPool } from '@/api/job'

const props = defineProps<{talentId:number, trackId?: string}>()
const emit = defineEmits(['close'])
const router = useRouter()
const { talentId, trackId } = toRefs(props)
const userStore = useUserStore()
const route = useRoute()

const status = reactive({
  detailLoading: false,
  attachmentsLoading: false,
  positionLoading: false,
  showEditBasicModal: false,
  showEditDemandModal: false,
  initDict: false,
  updateTalentInfo: false,
  followUpLoading: false,
  showJobSelector: false,
  joinJob: false,
  showSmartDeerWorkModal: false,
  showChatBox: false
})

const drawer = reactive({
  show: false,
  title: '',
  component: null as any,
  params: {} as any,
  width: 480
})

const talent = ref<any>({})

const savedTalent = ref<any>({})

const attachments = ref<any[]>([])

async function fetchTalentDetail(talentId: number) {
  status.detailLoading = true
  try {
    const talentDetailRes = await unionSearchTalentDetail(talentId)
    const talentResumeAttachments = await getResumeAttachmentsByFileIds(talentDetailRes.data.fileIds)
    talent.value = talentDetailRes.data
    attachments.value = talentResumeAttachments.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.detailLoading = false
}

const pagination = reactive({
  current: 1,
  size: 4,
  total: 0,
})

const chatUsers = reactive({
  from: {},
  to: {}
})

function getUsers(position: any, userCategory: 'pm' | 'ca') {
  const users: any[] = []
  position.jobRequirement.properties?.forEach((item: any, index: number) => {
    if (item.key.toLowerCase() === userCategory) {
      users.push(item.valueName)
    }
  })
  return users.join(',')
}

interface Expirence {
  exp: string
  timeRange: string
  type: 'work' | 'education'
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

async function addToMyTalent() {
  try {
    const addTalentRes = await unionSearchAddGlobalTalent(talent.value)
    message.success("将人才加入到我的人才成功！")
    emit('close')
    return addTalentRes.data
  } catch (err: any) {
    message.error(err.message)
    emit('close')
  }
}

async function handleSaveAndJoinJob() {
  const addTalentRes = await unionSearchAddGlobalTalent(talent.value)
  savedTalent.value.id = addTalentRes.data.talentId
  savedTalent.value.realName = talent.value.name
  status.showJobSelector = true
}

async function handleSelectJob(jobs: any[]) {
  const job = jobs[0]
  status.showJobSelector = false
  status.joinJob = true
  try {
    if (props.trackId) {
      setSearchBury({
        action: 2,
        track_id: props.trackId
      })
    }

    const talentId = savedTalent.value.id
    const talentName = savedTalent.value.realName
    const jobName = job.processName
    const taskRes = await addTalentToJobPool(job.id, talentId)
    message.success(`人才[ ${talentName} ]成功加入项目[ ${jobName} ]!`)
    emit('close')
  } catch (err: any) {
    message.error(err.message)
    emit('close')
  }
  status.joinJob = false
}

watch(() => props.talentId, (value: any) => {
  talentId.value = value
  fetchTalentDetail(talentId!.value)
  tracker.event('talent-global-detail-modal-show', { talentId: value, page: route.name })
})

onMounted(async () => {
  await fetchTalentDetail(talentId?.value)
  tracker.event('talent-global-detail-modal-show', { talentId: talentId?.value, page: route.name })
})

onBeforeRouteLeave(()=>{
  emit('close')
})

</script>

<style lang="scss" scoped>
.talent-detail-page {
  .talent-detail-container {
    background-color: #FFF;
    border-radius: 8px;
    overflow: hidden;
  }
}

.talent-action {
  margin-top: 40px;
  position: sticky;
  bottom: 0;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__head {
    padding: 24px 24px 0 32px;

    h4 {
      font-size: 20px;
      margin-bottom: 24px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }

  &__empty {
    padding-bottom: 24px;
  }
}

.follow-up {
  background-color: #fff;
  border-radius: 4px;

  .follow-up-tab {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }

    :deep(.ant-tabs-tab) {
      font-size: 16px;
    }
  }

  &__head {
    padding: 0 24px;
    position: relative;

    h4 {
      font-size: 20px;
      position: relative;
      line-height: 24px;
      color: #ddd;
      margin: 0;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }

  &__textarea {
    padding: 16px 24px 24px;
    text-align: right;
  }

  &__textarea-input {
    background-color: #f9f9f9;
    border: none;
    border-radius: 4px;
    outline: none;
    margin-bottom: 16px;
  }

  &__comments {
    padding: 0 24px;

    .comments-item {
      padding: 16px 0;
      border-top: 1px solid #f0f0f0;

      &__comment {
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      &__name {
        color: #999;
      }

      &__date {
        color: #999;
      }
    }
  }

  .comment-empty {
    padding-bottom: 24px;
  }

  .comment-pagination {
    padding: 16px 0;
    text-align: center;
  }
}
</style>