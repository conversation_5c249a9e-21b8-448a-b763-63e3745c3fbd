<template lang="pug">
mixin talent-form-basic
  .talent-form-group
    h2 基本信息
    .talent-form-content
      a-row(:gutter="[12, 0]")
        a-col(:span="12")
          a-form-item(
            label="姓名",
            :colon="false",
            :name="['talent', 'realName']",
            :rules="[{ required: true, message: '姓名不能为空', trigger: ['change'] }]"
          )
            a-input(v-model:value="talentForm.talent.realName" placeholder="请填写人才的真实姓名")

        a-col(:span="12")
          a-form-item(
            label="人才渠道", 
            name=['talent', 'sourceType'],
            :rules="[{ required: false, message: '人才渠道不能为空', trigger: ['change'] }]"
          )
            a-select(v-model:value="talentForm.talent.sourceType" :options="dict.talentSource" placeholder="请选择人才来源的渠道" :disabled="true")

        a-col(:span="12")
          a-form-item(
            label="性别",
            :name="['talent', 'gender']",
            :rules="[{ required: true, message: '请选择性别' }]"
          )
            a-select(v-model:value="talentForm.talent.gender" placeholder="请选择人才性别")
              a-select-option(:value="1") 男
              a-select-option(:value="2") 女
              a-select-option(:value="3") 保密

        a-col(:span="12")
          a-form-item(
            label="生日",
            :name="['talent', 'birthday']",
            :rules="[{ required: false, message: '请输入人才生日' }]"
          )
            a-date-picker(
              style="width: 100%",
              :allow-clear="false",
              :value="formatDatePickerValue(talentForm.talent.birthday)"
              @change="(value: any) => handleBirthdayChange(value)"
            )

        a-col(:span="12")
          a-form-item(
            label="所在城市",
            :name="['talent', 'areaId']",
            :rules="[{ type: 'number', message: '请选择人才所在城市', required: true, trigger: ['change', 'blur'] }]"
          )
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'id' }",
              :tree-data="dict.area",
              placeholder="请选择人才当前所在城市",
              v-model:value="talentForm.talent.areaId"
            )

        a-col(:span="12")
          a-form-item(
            label="婚姻状况",
            :name="['talent', 'marriage']",
            :rules="[{ required: false, message: '请选择婚姻状况' }]"
          )
            a-select(v-model:value="talentForm.talent.marriage")
              a-select-option(:value="0") 未知
              a-select-option(:value="1") 已婚
              a-select-option(:value="2") 未婚
              a-select-option(:value="3") 离异

        a-col(:span="12")
          a-form-item(label="工作年限", :name="['talent', 'workYears']")
            a-input-number(
              v-model:value="talentForm.talent.workYears",
              style="width:100%",
              :min="0",
              :max="60"
              placeholder="请选择人才工作年限",
            )

mixin talent-form-contact
  .talent-form-group
    h2 联系方式
    .talent-form-content
      a-row(:gutter="[12, 0]")
        a-col(:span="24")
          //- 手机号校验，请注意反斜杠需要进行转义
          a-form-item(
            label="电话",
            :name="['talent', 'mobileNumber']",
            :rules="[{ type: 'string', pattern: /^[\\d]*$/, required: true, message: '请输入正确的手机号码', trigger: ['chane', 'blur'] }]"
          )
            a-input(:maxlength="11" v-model:value="talentForm.talent.mobileNumber" placeholder="请输入人才的手机号码")
              template(#addonBefore)
                a-select(style="width: 140px;", :options="dict.mobileArea", v-model:value="talentForm.talent.mobileArea")

        a-col(:span="12")
          //- 邮箱验证
          a-form-item(
            label="邮箱",
            :colon="false",
            :name="['talent', 'email']",
            :rules="[{ type: 'email', required: true, message: '请输入正确的邮箱', trigger: ['change'] }]"
          )
            a-input(v-model:value="talentForm.talent.email" placeholder="请输入人才常用邮箱地址")

        a-col(:span="12")
          a-form-item(
            label="微信号",
            :colon="false"
          )
            a-input(v-model:value="talentForm.talent.wechatNumber" placeholder="请输入人才的微信账户")

mixin talent-form-demand
  .talent-form-group
    h2 求职意向
    .talent-form-content
      a-row(:gutter="[12, 0]")
        a-col(:span="24")
          a-form-item(
            label="求职状态"
            :name="['talent', 'employeeStatus']",
            :rules="[{ required: true, message: '请选择求职状态' }]"
          )
            a-select(
              placeholder="请在下拉列表中选择当前求职状态",
              v-model:value="talentForm.talent.employeeStatus",
              :options="dict.employeeStatus",
            )
        a-col(:span="24")
          a-form-item(
            label="意向城市",
            :name="['talentDemand', 'areaDemand']",
            :rules="[{ type: 'array', message: '请选择人才期望城市', required: true, trigger: ['change', 'blur'] }]"
          )
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'id' }",
              multiple="",
              placeholder="请选择人才求职意向城市（可多选）"
              treeNodeFilterProp="title",
              :tree-data="dict.area",
              v-model:value="talentForm.talentDemand.areaDemand"
            )

        a-col(:span="24")
          a-form-item(label="期望行业")
            a-tree-select(
              multiple,
              allow-clear="",
              :fieldNames="{ label: 'title', value: 'id' }",
              placeholder="请选择期望行业（可多选）",
              :tree-data="dict.industry",
              treeNodeFilterProp="title",
              v-model:value="talentForm.talentDemand.currentIndustryTypes"
            )

        a-col(:span="24")
          a-form-item(label="期望薪资")
            a-input(v-model:value="talentForm.talentDemand.targetTotalCash")


mixin talent-form-experience
  .talent-form-group
    h2 工作经历
    .education-item(v-for="(experienceItem, index) in talentForm.talentExperiences", :key="index" )
      a-row(:gutter="[12, 0]")
        a-col(:span="12")
          a-form-item(
            label="起始时间",
            :name="['talentExperiences', index, 'fromDate']"
            :rules="[{ required: true, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(experienceItem.fromDate)"
              @change="(value: any) => handleStartDateChange(experienceItem, value)"
            )

        a-col(:span="12")
          a-form-item(
            label="截止时间",
            :name="['talentExperiences', index, 'toDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              v-if="experienceItem.toDate !== null",
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(experienceItem.toDate)"
              @change="(value: any) => handleEndDateChange(experienceItem, value)"
            )
              template(#renderExtraFooter)
                .date-pikcer-footer
                  a-button(type="text", @click="()=>{experienceItem.toDate = null}") 至今
            a-checkbox(
              v-else,
              :default-checked="experienceItem.toDate === null",
              @change="(value: any) => {experienceItem.toDate = '2000-1-1'}"
            ) 至今

        a-col(:span="12")
          a-form-item(
            label="公司",
            :name="['talentExperiences', index, 'companyName']",
            :rules="[{ required: true, message: '请输入公司名称' }]"
          )
            a-input(v-model:value="experienceItem.companyName")

        a-col(:span="12")
          a-form-item(
            label="岗位",
            :name="['talentExperiences', index, 'position']",
            :rules="[{ required: true, message: '请输入所在公司岗位' }]"
          )
            a-input(v-model:value="experienceItem.position")

        a-col(:span="24")
          a-form-item(label="工作介绍")
            a-textarea(
              v-model:value="experienceItem.jobDesc",
              :rows="4"
            )

        a-col(:span="24")
          a-form-item(label="工作职责")
            a-textarea(
              v-model:value="experienceItem.duties",
              :rows="4"
            )

        a-col(:span="24")
          a-form-item(label="所属行业",)
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'industryCode' }",
              show-search,
              treeNodeFilterProp="title",
              :tree-data="dict.industry",
              v-model:value="experienceItem.industryCode"
            )

        a-col(:span="12")
          a-form-item(label="公司规模",)
            a-select(
              placeholder="请选择公司规模",
              v-model:value="experienceItem.companyScale",
              :options="dict.companyScale"
            )
        a-col(:span="12")
          a-form-item(label="公司性质",)
            a-select(
              placeholder="请选择公司性质",
              v-model:value="experienceItem.companyType",
              :options="dict.companyType"
            )

        a-col(:span="24")
          a-form-item(label="公司介绍",)
            a-textarea(
              v-model:value="experienceItem.companyInfo",
              :rows="4"
            )

        a-col(:span="12")
          a-form-item(label="汇报人")
            a-input(v-model:value="experienceItem.reporter")
        a-col(:span="10")
          a-form-item(label="是否管理岗")
            a-checkbox-group(@change="(value: any) => handleSingleCheckBoxChange(value, 'exp_manage', index)")
              a-checkbox(value="1") 管理岗

        a-col(:span="12")
          a-form-item(label="下属人数",)
            a-input-number(v-model:value="experienceItem.juniorNumber" style="width:100%" :min="0")

      a-row
        a-col(:span="12")
          .form-item-action(@click="addExperience" v-if="index == talentForm.talentExperiences.length - 1")
            PlusOutlined
            span 添加

        a-col(:span="12" style="text-align:right;")
          a-popconfirm(
            title="确定要删除本条教育信息吗？",
            placement="left",
            @confirm="delExperience(index)"
          )
            .form-item-action
              MinusOutlined
              span 删除

      .spliter


mixin talent-form-education
  .talent-form-group
    h2 教育信息
    .education-item(v-for="(educationItem, index) in talentForm.talentEducations", :key="index" )
      a-row(:gutter="[12, 0]")
        a-col(:span="12")
          a-form-item(
            label="起始时间",
            :name="['talentEducations', index, 'fromDate']"
            :rules="[{ required: true, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(educationItem.fromDate)"
              @change="(value: any) => handleStartDateChange(educationItem, value)"
            )

        a-col(:span="12")
          a-form-item(
            label="截止时间",
            :name="['talentEducations', index, 'toDate']"
            :rules="[{ required: false, message: '请填写开始和结束时间' }]"
          )
            a-date-picker(
              v-if="educationItem.toDate !== null",
              style="width: 100%",
              picker="month",
              :allow-clear="false",
              :value="formatDatePickerValue(educationItem.toDate)"
              @change="(value: any) => handleEndDateChange(educationItem, value)"
            )
              template(#renderExtraFooter)
                .date-pikcer-footer
                  a-button(type="text" @click="()=>{educationItem.toDate = null}") 至今
            a-checkbox(
              v-else,
              :default-checked="educationItem.toDate === null",
              @change="(value: any) => {educationItem.toDate = '2000-1-1'}"
            ) 至今

        a-col(:span="12")
          a-form-item(
            label="学校名称",
            :name="['talentEducations', index, 'schoolId']",
            :rules="[{ required: true, message: '请选择学校' }]"
          )
            a-select(
              v-model:value="educationItem.schoolId",
              show-search,
              placeholder="请输入关键字并从下拉列表选择",
              :default-active-first-option="false",
              :show-arrow="false",
              :filter-option="false",
              :options="schoolList",
              @search="debouncedSchoolSearch"
            )
              //- template(#notFoundContent)
              //-   p 您输入的学校不存在，是否添加？
              //-   a-button(type="primary" ghost @click="showAddShool(educationItem)") 新增学校

        a-col(:span="12")
          a-form-item(
            label="专业"
            :name="['talentEducations', index, 'major']",
            :rules="[{ required: true, message: '请选择专业' }]"
          )
            a-input.long(v-model:value="educationItem.major")

        a-col(:span="12")
          a-form-item(
            label="学历"
            :name="['talentEducations', index, 'degree']",
            :rules="[{ required: true, message: '请选择学历' }]"
          )
            a-select(
              placeholder="请选择学历",
              v-model:value="educationItem.degree",
              :options="dict.degree"
            )
        a-col(:span="12")
          a-form-item(label="是否统招")
            a-checkbox-group(
              @change="(value: any) => handleSingleCheckBoxChange(value, 'edu_unified', index)"
            )
              a-checkbox(value="1") 统招

        a-col(:span="24")
          a-form-item(label="专业介绍")
            a-textarea(
              v-model:value="educationItem.majorDesc",
              :rows="4"
            )

        a-col(:span="12")
          .form-item-action(@click="addEducation" v-if="index == talentForm.talentEducations.length - 1")
            PlusOutlined
            span 添加

        a-col(:span="12" style="text-align:right;")
          a-popconfirm(
            title="确定要删除本条教育信息吗？",
            placement="left",
            @confirm="delEducation(index)"
          )
            .form-item-action
              MinusOutlined
              span 删除
      .spliter

.talent-update-component
  a-spin(:spinning="status.loading")
    .talent-update-form
      a-form(:model="talentForm", label-align="left", ref="talentFormInstance", :scrollToFirstError="true", layout="vertical")
        //- template(v-if="section.includes('contact')")
        template(v-if="section.includes('basic')")
          +talent-form-basic
          +talent-form-contact
        template(v-if="section.includes('demand')")
          +talent-form-demand
        template(v-if="section.includes('education')")
          +talent-form-education
        template(v-if="section.includes('experience')")
          +talent-form-experience

  .talent-update-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="handleUpdateTalent" type="primary" :loading="status.loading") 保存
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, toRef } from 'vue'
import { message } from 'ant-design-vue'
import {
  getCompanyTypeList, getCompanyScaleList,
  getAllIndustryList, getAllFunctionList, getSchoolList,
  createSchool, getSchoolKind, getSchoolLevel, getTalentSource, dictionary
} from '@/api/dictionary'
import { useRoute } from 'vue-router'
import { getTalentDetail, createOrUpdateTalent, updateTalentBase } from '@/api/talent/talent'
import { areaDictToTreeData, industryDictToTreeData } from '@/utils/form-data-helper'
import { phoneAreaDict } from '@/utils/phone-area-dict'
import dayjs from 'dayjs'
import { debounce } from '@/utils/util'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'

const talentForm = ref<any>({
  talent: {},
  talentDemand: {},
  talentExperiences: {},
  talentProjects: {},
  talentSkills: {}
})

const dict = reactive({
  degree: [] as any[],
  companyType: [] as any[],
  companyScale: [] as any[],
  area: [] as any,
  industry: [] as any,
  function: [] as any[],
  employeeStatus: [] as any[],
  schoolKind: [] as any[],
  schoolLevel: [] as any[],
  talentSource: [] as any[],
  mobileArea: [] as any[]
})

const status = reactive({
  loading: false,
})

const props = defineProps(['talentId', 'section'])
const emit = defineEmits(['update', 'close'])

const talentId = toRef(props, 'talentId')
const section = toRef(props, 'section')
const talentFormInstance = ref()

async function handleUpdateTalent() {
  status.loading = true

  try {
    await talentFormInstance.value?.validate()
    const res = await createOrUpdateTalent(talentForm.value)
    message.success('人才基本信息保存成功！')
    emit('update')
  } catch (err: any) {
    if (err.errorFields) {
      const firstError = err.errorFields[0]
      message.error(firstError.errors.join(','))
      talentFormInstance.value?.scrollToField(firstError.name, { behavior: 'smooth', block: 'center' })
    }
    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

async function fetchTalentDetail(talentId: number) {
  status.loading = true
  try {
    const res = await getTalentDetail(talentId)
    talentDataProcess(res.data)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

function talentDataProcess(talent: any) {
  talentForm.value = talent
  if (!talent.talent.mobileArea) talentForm.value.talent.mobileArea = '+86'
  if (talent.talent.gender === 0) talentForm.value.talent.gender = null

  talent.talentEducations.forEach((item: any, index: Number) => {
    schoolList.value.push({ label: item.schoolName, value: item.schoolId })
  })
}

async function initDict() {
  const dictPromiseList = [
    dictionary.getDegree(), getCompanyTypeList(), getCompanyScaleList(), dictionary.getAllAreaList(),
    getAllIndustryList(), getAllFunctionList(), dictionary.getEmployeeStatus(), getSchoolKind(),
    getSchoolLevel(), getTalentSource()
  ]

  const [
    dictDegree, dictCompanyType, dictCompanyScale, dictArea,
    dictIndustry, dictFunction, dictEmployeeStatus, dictSchoolKindRes,
    dictSchoolLevelRes, dictTalentSource
  ] = await Promise.all(dictPromiseList)

  for (let id in dictDegree.data) {
    dict.degree.push({ value: Number(id), label: dictDegree.data[id] })
  }

  dict.talentSource = Object.entries(dictTalentSource.data).map((item, index) => {
    const [value, label] = item
    return { value: Number(value), label }
  })

  const [areaDictTreeData] = areaDictToTreeData(dictArea.data)
  dict.area = areaDictTreeData

  const [industryDictTreeData] = industryDictToTreeData(dictIndustry.data)
  dict.industry = industryDictTreeData

  dict.schoolKind = dictSchoolKindRes.data

  dict.mobileArea = phoneAreaDict.map((item: any, index: number) => {
    return { label: `${item.number} ${item.areaNameCN}`, value: item.number }
  })

  dict.function = dictFunction.data.map((item: any, index: number) => {
    return { value: item.id, label: item.name }
  })

  dict.employeeStatus = dictEmployeeStatus.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })

  dict.schoolLevel = dictSchoolLevelRes.data.map((item: any, index: number) => {
    return { value: item.id, label: item.title }
  })

  dict.companyScale = dictCompanyType.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })

  dict.companyType = dictCompanyScale.data.map((item: any, index: number) => {
    return { value: item.id, label: item.type }
  })
}

function handleBirthdayChange(value: any) {
  if (value) talentForm.value.talent.birthday = value.format('YYYY-MM-DD')
  else talentForm.value.talent.birthday = value
}

function formatDatePickerValue(data: any) {
  if (data) return dayjs(data)
  else return null
}

const schoolList = ref<any[]>([])
function getDictSchoolList(keyword: string) {
  getSchoolList(keyword, 1, 20).then((res) => {
    const schoolSearchList = [] as any[]
    res.data.forEach((schoolItem: any) => {
      schoolSearchList.push({
        value: schoolItem.id,
        label: schoolItem.name
      })
    })
    schoolList.value = schoolSearchList
  })
}
const debouncedSchoolSearch = debounce(getDictSchoolList)

function addEducation() {
  talentForm.value.talentEducations.push({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    schoolId: null,
    schoolName: '',
    major: '',
    majorDesc: '',
    degree: null,
    isUnified: 0
  })
}

function addExperience() {
  talentForm.value.talentExperiences.push({
    id: null,
    talentId: talentForm.value.talent.id,
    fromDate: dayjs(),
    toDate: null,
    companyName: '',
    companyInfo: '',
    companyScale: 0,
    companyType: 0,
    industryCode: '',
    isManager: 0,
    jobDesc: '',
    juniorNumber: 0,
    position: '',
    reporter: '',
    duties: ''
  })
}

function delExperience(index: number) {
  talentForm.value.talentExperiences.splice(index, 1)
}

function delEducation(index: number) {
  talentForm.value.talentEducations.splice(index, 1)
}

const route = useRoute()
onMounted(async () => {
  await initDict()
  fetchTalentDetail(talentId.value)
})

</script>

<style lang="scss" scoped>
.talent-update-component {
  padding-bottom: 56px;

  .talent-form-group {
    h2 {
      position: relative;
      font-size: 18px;
      line-height: 20px;
      margin: 0 0 24px;
      padding: 16px 0 0 16px;

      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 20px;
        position: absolute;
        border-radius: 2px;
        left: 0;
        background-color: #FF9111;
      }
    }
  }

  .talent-form-content {}

  .talent-update-form {
    padding: 12px 24px;

    .education-item {
      .spliter {
        border-top: 1px solid #f0f0f0;
        margin: 24px 0 40px;
      }

      .form-item-action {
        display: inline;
        cursor: pointer;
        transition: all .2s;

        &:hover {
          color: #FF9111;
          transition: all .2s;
        }
      }
    }
  }

  .talent-update-action {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px 24px;
    text-align: right;
    box-sizing: border-box;
  }
}
</style>