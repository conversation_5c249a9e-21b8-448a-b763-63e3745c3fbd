<template lang="pug">
.celerbrate-notice
  canvas(ref="canvas" :class="{show: status.show}")
  .notice-info
    .title {{ notice.noticeTitle  }}
    .content {{ notice.noticeContent }}
</template>

<script lang="ts" setup>
import { onBeforeUnmount, reactive, nextTick } from 'vue'
import { toRef } from 'vue';
import { onMounted, ref } from 'vue'

const status = reactive({
  show: false
})

class Particle {
  private opacity: number;
  private dx: number;
  private dy: number;

  constructor(
    private ctx: any,
    velocityX: number,
    velocityY: number,
    private x: number,
    private y: number,
    private r: number,
    private color: string,
    private gravity: number,
    private friction: number
  ) {
    this.dx = velocityX;
    this.dy = velocityY;
    this.opacity = 1;
  }

  draw() {
    this.ctx.save()
    this.ctx.globalAlpha = this.opacity
    this.ctx.beginPath()
    this.ctx.arc(this.x, this.y, this.r, 0, Math.PI * 2)
    this.ctx.fillStyle = this.color
    this.ctx.fill()
    this.ctx.restore()
    this.ctx.closePath()
  }
  // We update our particle for each frame;
  update() {
    this.draw()
    this.dy += this.gravity
    this.dx *= this.friction
    this.dy *= this.friction
    this.x += this.dx
    this.y += this.dy
    this.opacity -= 0.005
  }
}

// companyId: 1
// companyUserId: 1
// createTime: 1698132776000
// id: 2042
// noticeContent: "候选人 Bryce 被推荐到了你负责的职位 Java 开发工程师，请及时处理。"
// noticeTitle: "职位 Java 开发工程师 有新进展"
// noticeType: 0
// noticeUrl: "/job/670/detail"
// readStatus: 0
// sendStatus: 1

interface CelebrateNoticeParams {
  id: number,
  noticeTitle: string,
  noticeContent: string,
  noticeUrl: string,
  noticeType: number,
  readStatus: number,
  sendStatus: number,
}
const props = defineProps<{ notice: CelebrateNoticeParams }>()
const notice = toRef(props, 'notice')
const emit = defineEmits(['finish'])
const canvas = ref<any>()

const ANIMA_GRAVITY = 0.2
const ANIMA_FRICTION = 0.99

let animationFrame = null as any
let particles = [] as any[]
let particleCount = 1000

function animate(ctx: any) {
  animationFrame = requestAnimationFrame(() => animate(ctx))
  ctx.clearRect(0, 0, window.innerWidth, window.innerHeight)
  particles.forEach((particle, i) => {
    if (particle.opacity > 0) {
      particle.update()
    } else {
      particles.splice(i, 1)
    }
  })
}

function explode(ctx: any) {
  const position = getRandomPosition()
  const x = position.x
  const y = position.y
  const speed = 40
  const angleIncrement = (Math.PI * 2) / particleCount

  for (let i = 0; i < particleCount; i++) {
    particles.push(
      new Particle(
        ctx,
        Math.cos(angleIncrement * i) * Math.random() * speed,
        Math.sin(angleIncrement * i) * Math.random() * speed,
        x, y, 3, `hsl(${Math.random() * 360}, 100%, 50%)`,
        ANIMA_GRAVITY, ANIMA_FRICTION
      )
    )
  }
}

function getRandomPosition() {
  // 坐标不会出现在整个窗口的60像素内。
  const padding = 60
  const width = window.innerWidth
  const height = window.innerHeight
  const x = (Math.random() * (width - padding * 2)) + padding
  const y = (Math.random() * (height - padding * 2)) + padding
  return { x, y }
}

async function openGlobalNotice(notice: any) {
  const ctx = canvas.value.getContext('2d')
  ctx!.canvas.height = window.innerHeight
  ctx!.canvas.width = window.innerWidth

  particles = []
  cancelAnimationFrame(animationFrame)

  setTimeout(() => explode(ctx), 0)
  setTimeout(() => explode(ctx), 200)
  setTimeout(() => explode(ctx), 600)
  setTimeout(() => explode(ctx), 800)
  animate(ctx)

  setTimeout(() => {
    status.show = false
  }, 4500)

  setTimeout(() => {
    emit('finish', notice)
    particles = []
    cancelAnimationFrame(animationFrame)
  }, 5000)
}

onMounted(() => {
  setTimeout(() => {
    status.show = true
  }, 0)
  openGlobalNotice(props.notice)
})

onBeforeUnmount(() => {
  // console.log('onBeforeUnmount')
})
</script>

<style lang="sass" scoped>
.celerbrate-notice
  canvas
    position: fixed
    top: 0
    left: 0
    width: 100%
    height: 100%
    pointer-events: none
    z-index: 100
    transition: all .3s
    &.show
      transition: all .3s
      background-color: rgba(0, 0, 0, .6)

  .notice-info
    line-height: 1.6
    position: fixed
    animation: example 4.5s
    left: 50%
    top: 0
    color: #000
    transform: translate(-50%, -100%)
    text-align: center
    z-index: 100

    .title
      font-size: 48px
      font-weight: bold
      background: linear-gradient(254deg, #F54A25 -70.15%, #FFAB71 100.01%, #FFAB71 105.02%)
      background-clip: text
      -webkit-background-clip: text
      -webkit-text-fill-color: transparent
    .content
      font-size: 14px
      color: #fff
      font-size: 18px
      white-space: pre-wrap

@keyframes example
  0%
    top:0
    transform: translate(-50%, -100%)

  10%
    top: 40%
    transform: translate(-50%, -50%)

  90%
    top: 40%
    transform: translate(-50%, -50%)

  100%
    top: 0
    transform: translate(-50%, -100%)
</style>