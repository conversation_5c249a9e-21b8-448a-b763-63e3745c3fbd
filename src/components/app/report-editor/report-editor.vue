<template lang="pug">

mixin info-list
  Draggable(v-model="child.list" @start="drag=true" @end="drag=false" item-key="key")
    template(#item="{element, index}")
      div(style="padding-left: 96px; position: relative; margin-bottom: 8px; min-height: 1.6em; line-height: 1.6;" actionable)
        EditElement(style="position: absolute; left: 0; top: 0; font-weight: bold; width: 96px;" contenteditable v-model:content="element.title" data-placeholder="信息标题")
        EditElement(style="margin-left: 12px;"  contenteditable v-model:content="element.content" :data-placeholder="element.title")
        .action
          a-button(@click="handleDeleteElement(child.list, index)") 删除

mixin work-experience-list
  Draggable(v-model="child.list" @start="drag=true" @end="drag=false" item-key="id")
    template(#item="{element, index}")
      div(style="margin-bottom: 24px;" actionable)
        div(style="position:relative;")
          EditElement(style="display: inline-block;" contenteditable v-model:content="element.fromDate" data-placeholder="开始时间")
          span(style="margin: 0 8px;" contenteditable) {{ '-' }}
          EditElement(style="margin-right: 32px; display: inline-block;" contenteditable v-model:content="element.toDate" data-placeholder="结束时间")
          EditElement(style="margin-right: 12px; font-weight: bold; display: inline-block;" contenteditable v-model:content="element.companyName" data-placeholder="公司名称")

        table(style="border:none; border-collapse: collapse; line-height:1.6;")
          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="font-weight: bold; display: inline;margin-right:12px;" contenteditable) 工作职位: 
            td(style="vertical-align: top;")
              EditElement(style="display: inline;" contenteditable v-model:content="element.position" data-placeholder="职位")

          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="font-weight: bold; display: inline; margin-right:12px;" contenteditable) 工作描述: 
            td(style="vertical-align: top;")
              EditElement(style="white-space: pre-wrap;" contenteditable v-model:content="element.jobDesc" data-placeholder="工作描述")

        .action
          a-button(@click="handleDeleteElement(child.list, index)") 删除

mixin education-list
  Draggable(v-model="child.list" @start="drag=true" @end="drag=false" item-key="id")
    template(#item="{element, index}")
      div(style="margin-bottom: 8px;" actionable)
        div(style="position:relative;")
          EditElement(style="display: inline-block;" contenteditable v-model:content="element.fromDate" data-placeholder="开始时间")
          span(style="margin: 0 8px;" contenteditable) {{ '-' }}
          EditElement(style="margin-right:12px; display: inline-block;" contenteditable v-model:content="element.toDate" data-placeholder="结束时间")
          EditElement(style="margin-right:12px; display: inline-block; font-weight: bold;" contenteditable v-model:content="element.schoolName" data-placeholder="学校")
          EditElement(style="margin-right:12px; display: inline-block;" contenteditable v-model:content="element.major" data-placeholder="专业")
        .action
          a-button(@click="handleDeleteElement(child.list, index)") 删除

mixin project-list
  Draggable(v-model="child.list" @start="drag=true" @end="drag=false" item-key="id")
    template(#item="{element}")
      div(style="margin-bottom: 24px;" actionable)
        div(style="line-height: 1.6;")
          EditElement(style="display: inline-block;" contenteditable v-model:content="element.fromDate" data-placeholder="开始时间")
          span(style="margin: 0 8px;" contenteditable) {{ '-' }}
          EditElement(style="margin-right: 32px;display: inline-block;" contenteditable v-model:content="element.toDate" data-placeholder="结束时间")
          EditElement(style="font-weight: bold; display: inline-block;" contenteditable v-model:content="element.projectName" data-placeholder="项目名称") {{ element.projectName }}

        table(style="border:none; border-collapse: collapse; line-height:1.6;")
          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="margin-right: 12px; font-weight: bold;" contenteditable data-placeholder="标题") 项目角色: 
            td(style="vertical-align: top;")
              EditElement(contenteditable v-model:content="element.talentTitle" data-placeholder="项目角色")
          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="margin-right: 12px; font-weight: bold;" contenteditable data-placeholder="标题") 所在公司: 
            td(style="vertical-align: top;")
              EditElement(contenteditable v-model:content="element.companyName" data-placeholder="所在公司")
          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="margin-right: 12px; font-weight: bold;" contenteditable data-placeholder="标题") 项目描述: 
            td(style="vertical-align: top;")
              EditElement(style="white-space: pre-wrap;" contenteditable v-model:content="element.projectDesc" data-placeholder="项目描述")
          tr
            td(style="vertical-align: top; white-space: nowrap;")
              span(style="margin-right: 12px; font-weight: bold;" contenteditable data-placeholder="标题") 项目职责: 
            td(style="vertical-align: top;")
              EditElement(style="white-space: pre-wrap;" contenteditable v-model:content="element.projectDuties" data-placeholder="项目职责")

        .action
          a-button(@click="handleDeleteElement(child.list, index)") 删除

mixin text-item
  EditElement(style="margin-right:12px; white-space:pre-wrap" contenteditable v-model:content="aiContent.recommendation" data-placeholder="顾问评价")

mixin editor-container
  div.editor-container(ref="editorContainer" style="line-height: 1.6; position: relative;" v-if="config")
    section
      EditElement(v-model:content="config.title" contenteditable style="font-size:24px; display: block; color: #ff9111; font-weight: bold; text-align: center; margin: 12px 0;" data-placeholder="报告标题")
    section(v-for="(section, index) in config.sections" style="margin-bottom: 24px;")
      template(v-if="section.visible")
        div(actionable)
          EditElement(v-model:content="section.title" contenteditable style="display: block; font-size: 18px; font-weight:bold; color: #ff9111; margin: 12px 0;" data-placeholder="模块标题")
          .action
            a-button(@click="handleHideSection(section)" style="margin-right: 4px;") 隐藏
            a-button(v-if="section.type === 'info'" @click="handleAddInfo(section.children)") 添加信息
            a-button(v-if="section.type === 'education'" @click="handleAddEducation(section.children)") 添加教育经历
            a-button(v-if="section.type === 'work'" @click="handleAddWorkExperience(section.children)") 添加工作经历
            a-button(v-if="section.type === 'project'" @click="handleAddProject(section.children)") 添加项目经历

        template(v-for="(child, index) in section.children")
          template(v-if="child.type === 'info'")
            +info-list
          template(v-if="child.type === 'education'")
            +education-list
          template(v-if="child.type === 'work'")
            +work-experience-list
          template(v-if="child.type === 'project'")
            +project-list
          template(v-if="child.type === 'text'")
            +text-item

mixin action-container
  .action-container
    InfoSection(title="所有模块")
      a-space(wrap)
        template(v-for="(section, index) in config.sections")
          a-checkbox(v-model:checked="section.visible") {{ section.title || '未命名模块' }}

    InfoSection(title="AI 生成顾问评价")
      a-space(wrap)
        a-button(type="primary" :loading="status.aiLoading" @click="handleAiConsultantComment") 一键生成顾问评价
          template(#icon)
            FileDoneOutlined
      .ai_desc(v-if="status.aiLoading" ) AI 正在思考中，生成过程大概在 10 秒钟左右完成...
    .actions
      a-row(:gutter="[16, 16]")
        a-col(:span="24" align="right")
          a-space(:size="8")
            a-button(type="primary" ghost @click="handleSaveReport") 保存
              template(#icon)
                SaveOutlined
            a-button(type="primary" @click="handleSaveAndExport") 保存 & 导出PDF
              template(#icon)
                FileDoneOutlined

.editor(v-if="config")
  a-row(:gutter="[16, 16]")
    a-col(:span="16")
      +editor-container
    a-col(:span="8")
      +action-container

</template>

<script lang="ts" setup>
import { reactive, onMounted, toRef, ref, watch } from 'vue'
import EditElement from './edit-element.vue'
import { SaveOutlined, FileDoneOutlined } from '@ant-design/icons-vue'
import Draggable from 'vuedraggable'
import InfoSection from '@/components/info-section/info-section.vue'
import {
  attachReportToTalentJob,
  getAIConsultantRecommendation,
  getTalentDetail,
  saveTalentReportToFile
} from '@/api/talent/talent'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { createConversation, getCozeAppConfig, getTalentMatchingParams, runWorkflow } from '@/api/ai'

const props = defineProps<{ talentId: any, jobRequirementId: any, report?: any }>()
const editorContainer = ref()
const report = toRef(props, 'report')
const config = ref<any>()
const status = reactive({
  aiLoading: false
})
const aiContent = reactive({
  recommendation: ''
})

const aiConfig = reactive<any>({})

function handleDeleteElement(list: any[], index: number) {
  list.splice(index, 1)
}

function initConfig(talent: any) {
  if (!talent) return {}
  const tag = new Date().getTime()
  return {
    title: '人才推荐报告',
    sections: [
      {
        title: '个人信息', visible: true, type: 'info', children: [
          {
            type: 'info', list: [
              { title: '姓名', content: talent.talent.realName, key: `${tag}-1` },
              { title: '性别', content: talent.talent.genderStr, key: `${tag}-2` },
              { title: '年龄', content: talent.talent.birthday, key: `${tag}-3` },
              { title: '目前地址', content: '', key: `${tag}-6` },
              { title: '期望地址', content: '', key: `${tag}-7` },
              { title: '工作年限', content: '', key: `${tag}-8` },
              { title: '目前薪资', content: '', key: `${tag}-9` },
              { title: '跳槽原因', content: '', key: `${tag}-10` },
            ]
          }
        ]
      }, {
        title: '顾问评价', visible: true, type: 'intextfo', children: [
          {
            type: 'text', content: talent.talent.comment
          }
        ]
      }, {
        title: '教育经历', visible: true, type: 'education', children: [
          {
            type: 'education',
            list: talent.talentEducations.map((item:any)=>{
              item.fromDate = item.fromDate ? dayjs(item.fromDate).format('YYYY.MM') : ''
              item.toDate = item.toDate ? dayjs(item.toDate).format('YYYY.MM') : ''
              return item
            })
          }
        ]
      }, {
        title: '工作经历', visible: true, type: 'work', children: [
          {
            type: 'work',
            list: talent.talentExperiences.map((item:any) => {
              item.fromDate = item.fromDate ? dayjs(item.fromDate).format('YYYY.MM') : ''
              item.toDate = item.toDate ? dayjs(item.toDate).format('YYYY.MM') : ''
              item.jobDesc = item.jobDesc ? `${item.jobDesc}<br>${item.duties}` : item.duties
              return item
            })
          }
        ]
      }, {
        title: '项目经历', visible: true, type: 'project', children: [
          {
            type: 'project',
            list: talent.talentProjects.map((item:any) => {
              item.fromDate = item.fromDate ? dayjs(item.fromDate).format('YYYY.MM') : ''
              item.toDate = item.toDate ? dayjs(item.toDate).format('YYYY.MM') : ''
              return item
            })
          }
        ]
      }
    ]
  }
}

function handleHideSection(section: any) {
  section.visible = false
}

function handleAddInfo(list: any[]) {
  const infoSection = list.find(item => item.type === 'info')
  infoSection.list.push({
    title: '标题',
    content: '内容'
  })
}

async function handleAddEducation(list: any[]) {
  const infoSection = list.find(item => item.type === 'education')
  infoSection.list.push({
    schoolName: '',
    major: '',
    degreeStr: '',
    fromDate: '',
    toDate: ''
  })
}

async function handleAddWorkExperience(list: any[]) {
  const infoSection = list.find(item => item.type === 'work')
  infoSection.list.push({
    fromDate: '',
    toDate: '',
    companyName: '',
    position: '',
    jobDesc: ''
  })
}

async function handleAddProject(list: any[]) {
  const infoSection = list.find(item => item.type === 'project')
  infoSection.list.push({
    projectName: '',
    projectDesc: '',
    fromDate: '',
    toDate: ''
  })
}

const emit = defineEmits(['save', 'export', 'aiComment'])
async function handleSaveReport() {
  emit('save')
}

async function handleSaveAndExport() {
  emit('export')
}

async function handleAiConsultantComment() {
  status.aiLoading = true
  const config = await  getCozeAppConfig()
  aiConfig.value = config.data

  const params = await getTalentMatchingParams(props.jobRequirementId, props.talentId)

  const conversation = await createConversation({
    objectType: 1,
    objectId: props.jobRequirementId + '-' + props.talentId
  })

  const resRecommendation = await runWorkflow(
    aiConfig.value.appId,
    aiConfig.value.reportWorkFlowId,
    {
      conversationId: conversation.data,
      params: params.data
    }
  )
  const recommendation = JSON.parse(resRecommendation.data)

  aiContent.recommendation = recommendation.output
  status.aiLoading = false
}

async function getConfig() {
  return config.value
}

async function getHtml() {
  return `<style>.action{ display: none;}</style>${editorContainer.value.innerHTML}`
}

const talentDetail = ref<any>({})
async function fetchTalentDetail(talentId: number) {
  try {
    const detailRes = await getTalentDetail(talentId)
    talentDetail.value = detailRes.data

  } catch (err: any) {
    message.error(err.message)
  }
}

defineExpose({
  getConfig, getHtml
})

async function init() {
  if (report.value) {
    try {
      config.value = JSON.parse(report.value.jsonContent)
    } catch (err: any) {
      await fetchTalentDetail(props.talentId)
      config.value = initConfig(talentDetail.value)
    }
  } else {
    await fetchTalentDetail(props.talentId)
    config.value = initConfig(talentDetail.value)
  }
}

onMounted(() => {
  init()
})
</script>

<style lang="sass" scoped>

.editor
  padding-bottom: 24px

.action-container
  background-color: #fff
  border-radius: 8px
  position: sticky
  top: 80px

  .actions
    padding: 16px 24px

.editor-container
  background-color: #fff
  border-radius: 8px
  padding: 24px

  & [actionable]
    position: relative
    &:hover
      outline: 1px solid RGBA(255, 145, 17, 0.1)
      border-radius: 4px

      .action
        display: block

    .action
      display: none
      position: absolute
      color: #ff9111
      top: 50%
      right: 0
      transform: translate(0, -50%)

      &:hover
        display: block
.ai_desc
  margin-top: 20px
</style>