<template lang="pug">
.container(:contenteditable="contenteditable" ref="container") {{ content }}

</template>

<script lang="ts" setup>
import { toRef, watch } from 'vue'
import { onMounted, ref } from 'vue'

const props = defineProps<{
  content: any,
  contenteditable?: boolean
}>()

const contenteditable = toRef(props, 'contenteditable')
const content = toRef(props, 'content')
const emit = defineEmits(['update:content'])
const container = ref<HTMLElement>()

function handleInput() {
  if (container.value?.innerHTML === '<br>') {
    container.value!.innerHTML = ''
  }
  emit('update:content', container.value?.innerHTML)
}

onMounted(() => {
  container.value!.innerHTML = content.value
  if (!container.value?.innerHTML || container.value?.innerHTML === 'undefined') {
    container.value!.innerHTML = ''
  }
})
</script>

<style lang="sass" scoped>
.container
  display: inline-block
  outline: 0px solid transparent

  &:empty:before
    content: attr(data-placeholder)
    color: #AAA
</style>