
<template lang="pug">
.app-sider
  //- 账号信息
  .app-sider-account(:class="{ collapsed: appStore.collapsed }")
    .app-sider-account__avatar
      a-avatar.avatar(:size="appStore.collapsed ? 40 : 60")
        template(#icon)
          UserOutlined
    .app-sider-account__name {{ userStore.name }}
    .app-sider-account__company(v-if="appStore.collapsed === false") {{ userStore.companyName }}

  //- 主导航
  nav.app-sider-nav
    a-menu(v-model:selected-keys="active", mode="inline"  @click="handleMenuItemClick")
      a-menu-item(key="home")
        template(#icon)
          HomeOutlined
        router-link(to="/") 首页
      //- a-menu-item(key="user-data")
      //-   template(#icon)
      //-     DotChartOutlined
      //-   router-link(to="/user/data") 总数据

      a-sub-menu(key="performance" v-if="hasPermission(PERMISSIONS.performance.code)" )
        template(#icon)
          TeamOutlined
        template(#title)
          span 业绩管理

        a-menu-item(key="performance-detail-me" v-if="hasPermission(PERMISSIONS.performance.children.mine.code)" )
          router-link(to="/performance/me") 我的业绩

        a-menu-item(key="performance-offer-list" v-if="hasPermission(PERMISSIONS.performance.children.project.code)")
          router-link(to="/performance/offer/list") 项目业绩

      a-sub-menu(key="talent" v-if="hasPermission(PERMISSIONS.talent.code)" )
        template(#icon)
          TeamOutlined
        template(#title) 
          span 人才管理

        a-menu-item(key="talent-list-me-interview" v-if="hasPermission(PERMISSIONS.talent.children.mine.code)" )
          router-link(to="/talent/list/me") 我的人才

        a-menu-item(key="talent-list-company" v-if="hasPermission(PERMISSIONS.talent.children.company.code)" )
          router-link(to="/talent/list") 公司人才

        a-menu-item(key="talent-list" v-if="hasPermission(PERMISSIONS.talent.children.all.code)")
          router-link(to="/talent/list/all") 全部人才

        a-menu-item(key="talent-create" v-if="hasPermission(PERMISSIONS.talent.children.add.code)" )
          router-link(to="/talent/create") 新增人才

        //- a-menu-item(key="old-talent-web-search")
        //-   router-link(to="/talent/web/old-search") 全网搜(旧)
      a-menu-item(key="web-search")
        template(#icon)
          SearchOutlined
        router-link(to="/talent/web/search") 全网搜
      a-sub-menu(key="job" v-if="hasPermission(PERMISSIONS.project.code)" )
        template(#icon)
          IdcardOutlined
        template(#title) 
          span 职位管理

        a-menu-item(key="my-job-list" v-if="hasPermission(PERMISSIONS.project.children.mine.code)" )
          router-link(to="/job/list/me") 我的项目

        a-menu-item(key="job-list" v-if="hasPermission(PERMISSIONS.project.children.company.code)" )
          router-link(to="/job/list") 所有项目

        a-menu-item(key="job-create" v-if="hasPermission(PERMISSIONS.project.children.add.code)" )
          router-link(to="/job/create") 新增项目

        //- a-menu-item(key="job-platform" v-if="hasPermission(PERMISSIONS.project.children.platform.code)" )
        //-   router-link(to="/job/list") 项目广场

      a-sub-menu(key="customer" v-if="hasPermission(PERMISSIONS.customer.code)" )
        template(#icon)
          UserOutlined
        template(#title) 
          span 客户管理

        a-menu-item(key="my-customer-list" v-if="hasPermission(PERMISSIONS.customer.children.mine.code)")
          router-link(to="/customer/list/me") 我的客户

        a-menu-item(key="customer-list" v-if="hasPermission(PERMISSIONS.customer.children.company.code)" )
          router-link(to="/customer/list") 全部客户

        a-menu-item(key="customer-create" v-if="hasPermission(PERMISSIONS.customer.children.add.code)" )
          router-link(to="/customer/create") 新增客户

      a-sub-menu(key="marketing" v-if="hasPermission(PERMISSIONS.operation.code)" )
        template(#icon)
          CrownOutlined
        template(#title) 
          span 运营管理

        a-menu-item(key="marketing-position-invite-list" v-if="hasPermission(PERMISSIONS.operation.children.promotion.code)" )
          router-link(to="/marketing/activity/list") 推广管理

        a-menu-item(key="marketing-position-intention-list" v-if="hasPermission(PERMISSIONS.operation.children.leads.code)" )
          router-link(to="/marketing/candidate/list") 线索管理

      a-sub-menu(key="stat" v-if="hasPermission(PERMISSIONS.statistic.code)" )
        template(#icon)
          PieChartOutlined
        template(#title) 
          span 报表
        a-menu-item(key="ca-stat" v-if="hasPermission(PERMISSIONS.statistic.children.ca.code)" )
          router-link(to="/stat/ca") CA 人员统计表
        a-menu-item(key="pm-stat" v-if="hasPermission(PERMISSIONS.statistic.children.pm.code)" )
          router-link(to="/stat/pm") PM 项目统计表
        a-menu-item(key="bd-stat"  v-if="hasPermission(PERMISSIONS.statistic.children.bd.code)")
          router-link(to="/stat/bd") BD 统计表
        a-menu-item(key="offer-stat" v-if="hasPermission(PERMISSIONS.statistic.children.offer.code)")
          router-link(to="/stat/offer") offer 统计表
        a-menu-item(key="finance-stat" v-if="hasPermission(PERMISSIONS.statistic.children.finance.code)")
          router-link(to="/stat/offer/receivable") offer应收报表

      a-sub-menu(key="finance" v-if="hasPermission(PERMISSIONS.finance.code)")
        template(#icon)
          BarChartOutlined
        template(#title) 
          span 财务
        a-menu-item(key="invoice-list-mine" v-if="hasPermission(PERMISSIONS.finance.children.mine.code)")
          router-link(to="/finance/invoice/list/my" ) 我的开票列表
        a-menu-item(key="invoice-list" v-if="hasPermission(PERMISSIONS.finance.children.invoice.code)")
          router-link(to="/finance/invoice/list" ) 开票列表
        a-menu-item(key="statment-list" v-if="hasPermission(PERMISSIONS.finance.children.revenue.code)")
          router-link(to="/finance/statement/list") 回款单
        a-menu-item(key="payment-list" v-if="hasPermission(PERMISSIONS.finance.children.claim.code)")
          router-link(to="/finance/payment/list") 回款认领
      a-sub-menu(key="rcn")
        template(#icon)
          BarChartOutlined
        template(#title) 
          span RCN
        a-menu-item(key="rcn-stat")
          router-link(to="/rcn/stat") RCN统计

      a-sub-menu(key="system" v-if="hasPermission(PERMISSIONS.company.code)")
        template(#icon)
          ControlOutlined 
        template(#title) 
          span 公司管理

        a-menu-item(key="system-staff-list" v-if="hasPermission(PERMISSIONS.company.children.staff.code)" )
          router-link(to="/system/staff/list") 员工管理

        a-menu-item(key="system-roles-list" v-if="hasPermission(PERMISSIONS.company.children.role.code)")
          router-link(to="/system/roles/list") 角色设置

        a-menu-item(key="system-department-list" v-if="hasPermission(PERMISSIONS.company.children.department.code)" )
          router-link(to="/system/department/list") 部门设置

        a-menu-item(key="system-performance-edit" v-if="hasPermission(PERMISSIONS.company.children.performance.code)")
          router-link(to="/system/performance/edit") 业绩分配设置

        //- a-menu-item(key="system-company-settings" v-if="hasPermission(PERMISSIONS.company.children.introduce.code)" )
        //-   router-link(to="/system/company/settings") 公司设置

      //- a-sub-menu(key="wechat" v-if="hasPermission(PERMISSIONS.mini_program.code)")
      //-   template(#icon)
      //-     WechatOutlined
      //-   template(#title) 
      //-     span 小程序管理

      //-   a-menu-item(key="knowledge-list" v-if="hasPermission(PERMISSIONS.mini_program.children.industry.code)" )
      //-     router-link(to="/wechat/industry/knowledge/list") 行业知识

    .menu-action(@click="() => { appStore.toggleMenuCollapse() }")
      RightOutlined(v-if="appStore.collapsed === true")
      LeftOutlined(v-if="appStore.collapsed === false")
</template>

<script lang="ts" setup>
import {
  UserOutlined, HomeOutlined, TeamOutlined, IdcardOutlined,
  RightOutlined, LeftOutlined, ControlOutlined, CrownOutlined,
  BarChartOutlined, WechatOutlined, PieChartOutlined, SearchOutlined,
  DotChartOutlined
} from "@ant-design/icons-vue"
import { useUserStore } from '@/store/user.store'
import { reactive } from '@vue/reactivity'
import { useRoute } from 'vue-router'
import { ref } from 'vue'
import { useAppStore } from "@/store/app.store"
import { hasPermission, PERMISSIONS } from '@/utils/permission'
import tracker from "@/utils/tracker"

const appStore = useAppStore()

const userStore = useUserStore()
const route = useRoute()
const active = ref([] as any[])
const isAdmin = ref(userStore.isAdmin)
const userPermissions = ref(userStore.permissions)

active.value = [route.name]

function handleMenuItemClick({key}:any) {
  tracker.click('side-menu-item-click', {key: key})
}

</script>

<style lang="scss" scoped>
.app-sider {
  width: 100%;
  height: 100%;
  border-top-right-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  overflow: scroll;
  padding-bottom: 16px;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.app-sider-account {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 28px 14px;
  align-items: center;
  border-bottom: 1px solid #f7f8f8;

  &__name {
    font-size: 16px;
    font-weight: bold;
    margin: 12px 0 4px 0;
    transition: all .2s;
    text-align: center;
  }

  &__company {
    color: #595959;
    text-align: center;
  }

  &__avatar {
    .avatar {
      transition: all .2s;
    }
  }
}

.collapsed {
  .app-sider-account__avatar {
    .avatar {
      transition: all .2s;
    }
  }

  .app-sider-account__name {
    font-size: 14px;
    transition: all .2s;
  }
}

.app-sider-nav {
  overflow: hidden;

  .menu-action {
    position: absolute;
    bottom: 0;
    height: 32px;
    width: 100%;
    color: #999;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
  }

  .ant-menu-inline {
    border-right: none;
  }
}
</style>
