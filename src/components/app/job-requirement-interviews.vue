<template lang="pug">
.job-interview-component
  .job-interview
    a-row.job-interview-item(
      v-for="(task, index) in taskList",
      :key="index"
    )
      a-col(:span="8")
        JobTalentInfo(:taskDetail="task")
      a-col(:span="16")
        .interview-detail
          span.label 添加面试时间：
          span.info {{ formatDate(task.task.createTime) }}
        .interview-detail
          span.label 面试时间：
          span.info {{ task.task.localVariables.startDate }}
        .interview-detail
          span.label 面试地点：
          span.info {{task.task.localVariables.interviewType}}
          span.address(v-if="task.task.localVariables.interviewAddress")
            EnvironmentOutlined
            span.address-detail {{ task.task.localVariables.interviewAddress }}
        .interview-detail(v-if="task.task.localVariables.interviewer" )
          span.label 面试官：
          span.info {{ task.task.localVariables.interviewer }}
        .interview-detail
          span.label 面试结果：
          span.info
            a-tag(v-if="task.task.localVariables.isFinalInterview=='1'" color="#FF9111") 终面
            a-tag(v-if="!task.task.localVariables.interviewResult" color="#2665FC") 未反馈
            a-tag(v-else color="#FF9111") {{ task.task.localVariables.interviewResult }}
            span {{ task.task.localVariables.interviewContent }}
    .job-interview-pagination
      a-pagination(
        v-model:current="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        @change="handleChangePage"
      )
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { EnvironmentOutlined } from '@ant-design/icons-vue'
import { searchJobRequirementTasks } from '@/api/job'
import { useUserStore } from '@/store/user.store'
import JobTalentInfo from '@/components/app/job-talent-info.vue'
import dayjs from 'dayjs'

const userStore = useUserStore()
const props = defineProps<{jobRequirementId: number, jobCompanyId: number}>()
const jobRequirementId = toRef(props, 'jobRequirementId')
const jobCompanyId = toRef(props, 'jobCompanyId')

const status = reactive({
  interviewLoading: false
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
})

const selectedTask = ref<any>({})
const taskList = ref<any[]>([])
async function fetchTaskList() {
  status.interviewLoading = true
  try {
    const taskParams = {
      searchItems: [
        {
          key: 'processDefinitionKey',
          value: 'linglupin_Position_Interview_Process',
          action: 'eq'
        },
        {
          key: 'jobRequirementId',
          value: jobRequirementId.value,
          action: 'eq'
        }
      ],
      orders: [
        {
          key: 'createTime',
          asc: 'desc'
        }
      ]
    }
    const taskListRes = await searchJobRequirementTasks(jobCompanyId.value, jobRequirementId.value, taskParams, pagination.current, pagination.size)
    taskList.value = taskListRes.data.tasks
    pagination.total = taskListRes.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.interviewLoading = false
}

const handleChangePage = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.size = pageSize
  fetchTaskList()
}

function handleUpdate() {
  fetchTaskList()
}

function formatDate(dateString: string) {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

onMounted(async () => {
  await fetchTaskList()
})

watch(jobRequirementId, (value) => {
  fetchTaskList()
})

defineExpose({
  refresh: () => {
    fetchTaskList()
  }
})

</script>

<style lang="scss" scoped>
.job-interview-item {
  padding: 24px;
  border-bottom: 1px #F8F8F8 solid;
}
.interview-detail {
  line-height: 26px;
  list-style-type: decimal;

  span {
    display: inline-block;
  }
  span.label {
    width: 120px;
  }
  span.info {
  }
  span.address {
    margin-left: 20px;
  }
  span.address-detail {
    margin-left: 5px;
  }
}
.job-interview-pagination {
  margin: 20px 0;
  text-align: right;
}
</style>