<template lang="pug">
.talent-search-item(v-if="talent")
  .talent-exp
    .exps    
      .exps-item(v-for="(exp, i) in talentExperiences" :key="i")
        .exps-icon(v-if="i == 0")
          img(src="@/assets/icons/icon-exp.png")
        .exps-time {{ formatTime(exp.fromDate) }} - {{ formatTime(exp.toDate) }}
        .exps-info
          span.em {{ exp.companyName }}
          span.spliter
          span {{ exp.position }}

    .edu.exps
      .exps-item(v-for="(education, i) in talentEducations" :key="i")
        .exps-icon(v-if="i == 0")
          img(src="@/assets/icons/icon-edu.png")
        .exps-time {{ formatTime(education.fromDate) }} - {{ formatTime(education.toDate) }}
        .exps-info
          span.em {{ education.schoolName }}
          span.spliter
          span {{ education.major }}
          span.spliter
          span {{ degreeDict.get(education.degree) }}

</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { computed, onMounted, ref, toRef, watch } from 'vue'
import { MailOutlined, MobileOutlined } from '@ant-design/icons-vue'
import degreeDict from '@/utils/degree-dict'

const props = defineProps<{talent: any}>()
const talent = toRef(props, 'talent')
const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(val).format('YYYY.MM')
}

const talentEducations = ref([])
const talentExperiences = ref([])

function getTalentExp(talent:any) {
  talentEducations.value = talent.educations.sort((a: any, b: any) => {
    const aTime = dayjs(a.fromDate).valueOf()
    const bTime = dayjs(b.fromDate).valueOf()
    return bTime - aTime
  }).slice(0, 2)

  const expCount = 4 - talentEducations.value.length

  talentExperiences.value = talent.experiences.sort((a: any, b: any) => {
    const aTime = dayjs(a.fromDate).valueOf()
    const bTime = dayjs(b.fromDate).valueOf()
    return bTime - aTime
  }).slice(0, expCount)
}

onMounted(() => {
  getTalentExp(talent.value)
})

watch(()=> props.talent, (val) => {
  getTalentExp(val)
})

</script>

<style lang="sass" scoped>
.talent-search-item
  width: 100%
  .talent-exp
    flex: 1 1 auto
    text-overflow: ellipsis
    white-space: nowrap
    font-size: 13px
    border-left: 1px dashed #eee
    padding-left: 16px
    padding-top: 4px
    width: 100%

    .exps
      width: 100%
      .exps-item
        line-height: 1
        position: relative
        margin-bottom: 12px
        width: 100%
        overflow: hidden
        text-overflow: ellipsis
        padding-left: 22px
        margin-left: -22px

        .exps-icon
          position: absolute
          top: 0
          left: 0
          img
            display: block
            width: 12px

        .exps-time
          font-family: arial
          display: inline-block
          color: #aaa
          width: 120px

        .exps-info
          display: inline

          span.spliter
            margin: 0 8px
            color: #aaa
            &:after
              content: '·'
    .edu
      .exps-item:last-child
        margin-bottom: 0
</style>