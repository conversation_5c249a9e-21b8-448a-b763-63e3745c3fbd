<script setup lang="ts">
import { onMounted, reactive, ref, watch, nextTick, toRefs, onUnmounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { updatePositionTags, getPositionDetail } from '@/api/position'
import { message } from 'ant-design-vue'
import InfoSection from '@/components/info-section/info-section.vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { formatSalary } from '@/utils/salary-helper'

const props = defineProps<{showCustomer:boolean, editable: boolean,position:any }>()

const { showCustomer, editable, position } = toRefs(props)
const env = ref<string>(import.meta.env.VITE_VUE_APP_BUILD_ENV)

</script>

<template lang="pug">
mixin customer-info
  .customer-info
    h4 公司信息
    .customer-info__head
      .customer-info__logo
        a-avatar(:size="48" :src="position.customer.customerLogo") {{ position.customer.customerFullName }}
      .customer-info__desc
        strong {{ position.customer.customerFullName }}
        p {{ position.customer.investStatusStr }} · {{ position.customer.companyScaleStr }} ｜ {{ position.customer.companyTypeStr }}
    .customer-info__intro
      p {{ position.customer.customerIntroduction }}

mixin position-detail
  .position-detail()
    info-section(title="职位信息" :editable="editable" @edit="$emit('edit')")
      a-descriptions(:column="2" :labelStyle="{color: '#999'}" )
        a-descriptions-item(label="职位名称") {{ position.positionTitle }}
        a-descriptions-item(label="HC 数量") {{ position.quantityRequired }} 个
        a-descriptions-item(label="职能") {{ position.functionStr }}
        a-descriptions-item(label="工作地点") 
          span(v-if="position.areaStr") {{ position.areaStr }}
          template(v-if="position.remoteWork")
            span  (可远程)
        a-descriptions-item(label="薪资" :span="2")  {{formatSalary(position)}}
        a-descriptions-item(label="管理岗" :span="2")  {{ position.isManager ? '是' : '否' }}
          span(v-if="position.isManager") （下属人数{{ position.underlingNumber }}人）
        a-descriptions-item(label="面试流程") 
          pre {{ position.interviewProcess}}

    info-section(title="职位描述" v-if="position.workDetail" @edit="$emit('edit')")
      pre {{ position.workDetail }}

.position-detail-component
  a-row(:gutter="16")
    a-col(:span="showCustomer ? 16: 24" v-if="position")
      +position-detail

    a-col(:span="8" v-if="showCustomer")
      +customer-info
</template>

<style lang="scss" scoped>
.position-detail {
  background: #FFFFFF;
  border-radius: 4px;
  border-right: 1px solid #F7F8F8;

  .position-summary {
    line-height: 30px;
  }

  &__head {
    padding: 28px 24px;
    position: relative;
  }

  &__offen {
    position: absolute;
    right: 26px;
    top: 32px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
    user-select: none;
    z-index: 9;

    .anticon {
      margin-left: 8px;
      color: #BFBFBF;
      font-size: 12px;
    }
  }

  &__item {
    margin-bottom: 12px;

    strong {
      font-size: 20px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__info {
    // padding: 0 24px;
    // padding-bottom: 4px;

    h4 {
      padding-left: 12px;
      margin: 0 0 16px 0;
      border-left: 4px solid #FF9111;
      height: 32px;
      line-height: 32px;
      font-weight: 400;
      font-size: 20px;
      margin-left: -16px;
    }
  }

  &__line {
    height: 1px;
    background: #F7F8F8;
  }

  &__row {
    padding: 24px 40px 24px 40px;
    border: 1px solid #fff;
    position: relative;

    .action {
      display: none;
    }

    .pre {
      white-space: pre-wrap;
      display: block;
      unicode-bidi: embed;
      line-height: 1.4;
    }

    .ma-l__110 {
      margin-left: 110px;
    }

    .ma-l__25 {
      margin-left: 25px;
    }

    .p {
      margin-bottom: 0;
      line-height: 24px;
      // padding: 0 11px 0 16px;

      &:last-child {
        padding-bottom: 16px;
      }
    }
  }
}

.customer-info {
  padding: 28px 20px;
  background: white;
  border-radius: 4px;

  h4 {
    height: 32px;
    line-height: 32px;
    border-left: 4px solid #FF9111;
    padding-left: 12px;
    font-weight: 400;
    font-size: 20px;
  }

  &__head {
    margin-top: 29px;
    display: flex;
    margin-left: 16px;
  }

  &__desc {
    margin-left: 16px;

    strong {
      padding-top: 2px;
    }

    p {
      color: #8C8C8C;
      height: 17px;
      font-size: 12px;
      line-height: 17px;
      margin-top: 8px;
      margin-bottom: 0px;
    }
  }

  &__intro {
    padding: 0 16px;
    margin-top: 34px;

    p {
      line-height: 24px;
      margin: 0;
    }
  }
}

.tag-editor {
  .tag-edit-item {
    margin-bottom: 12px;
  }
}
</style>