<template lang="pug">
.account-header-component
  h1.title 账户中心

  .app-header__account
    a-dropdown(placement="bottomRight")
      a-avatar.account-avatar(:src="userStore.avatar") {{  userStore.name  }}
      template(#overlay)
        a-menu
          a-menu-item(:key="1" @click="()=>{$router.push('/account/home')}") 个人账户设置
            template(#icon)
              SettingOutlined
          a-menu-item(:key="2" @click="() => { $router.push('/account/password/change') }") 更换密码
            template(#icon)
              UnlockOutlined
          a-menu-divider
          a-menu-item(:key="0" @click="userStore.logout()") 退出
            template(#icon)
              LogoutOutlined
</template>

<script lang="ts" setup>
import {SettingOutlined, UnlockOutlined, LogoutOutlined} from '@ant-design/icons-vue'
import { useUserStore } from '@/store/user.store'
const userStore = useUserStore()


</script>

<style lang="scss" scoped>
.account-header-component {
  display: flex;
  justify-content: space-between;
}
</style>