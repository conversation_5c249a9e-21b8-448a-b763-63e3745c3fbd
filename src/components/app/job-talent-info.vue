<template lang="pug">

mixin task-finish-reason
  .resume-item__finish-reason
    a-tag(v-if="process.variables.position_pm_talent_in_keep_pass === 1" color="#ff9111") 已过保
    a-tag(v-else-if="process?.variables.position_pm_talent_in_keep_pass === 0") 过保淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_to_hired_pass === 0") 入职淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_to_inspect_pass === 0") 背调淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_to_inspect_pass === 0") 背调淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_to_offer_pass === 0") Offer淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_to_salary_pass === 0") 谈薪淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_interview_pass === 0") 面试淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_get_to_customer_pass === 0") 客户淘汰
    a-tag(v-else-if="process?.variables.position_pm_talent_get_pass === 0") PM淘汰

.talent-search-item(v-if="talent")
  .talent-head
    a-badge-ribbon(text="我推荐" color="#ff9111" placement="start" :style="{opacity: userStore.id == suggestUser.id ? 1 : 0, top: '-4px'}" :show="false")
      a-avatar(:size="64", shape="square", :src="talent.photo") {{ talent.realName }}
      .gender
    //- .site {{ getSite(item.site) }}
  .talent-info 
    .talent-basic-name 
      span.name {{ talent.realName }}
      template(v-if="talentStatus")
        a-divider(type="vertical")
        span.status {{ talent.employeeStatusStr }}
    .talent-basic-other
      span(v-if="talent.gender") {{ talent.gender === 1 ? '男' : '女' }}
      span(v-else) 性别未知
      a-divider(type="vertical")
      span {{ getFromTimeBirthday(talent.birthday) }}
      a-divider(type="vertical")
      span {{ getFromTimeBeginWorkDate(talent.beginWorkDate) }}

    .talent-create-update-info
      span {{suggestUser?.realName}} 推荐于 {{ formatTime(process.startTime) }}
      //- span(v-if="talent.updateTime") 更新于 {{ formatTime(talent.updateTime) }}

    .reason
      +task-finish-reason

    //- .talent-mobile
    //-   span(v-if="talent.mobileNumber")
    //-     MobileOutlined(style="margin-right: 4px; color: #ccc;")
    //-   span {{ talent.mobileNumber }}
    //- .talent-email
    //-   span(v-if="talent.email")
    //-     MailOutlined(style="margin-right: 6px; color: #ccc;")
    //-   span {{ talent.email }}

</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { computed, onMounted } from 'vue'
import { MailOutlined, MobileOutlined } from '@ant-design/icons-vue'
import { toRef } from 'vue'
import { useUserStore } from '@/store/user.store';

const props = defineProps<{taskDetail: any}>()
const taskDetail = toRef(props, 'taskDetail')
const talent = computed(()=>taskDetail.value.talent)
const process = computed(()=>taskDetail.value.process)
const suggestUser = computed(() => taskDetail.value.suggestUser)
const userStore = useUserStore()
const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(val).format('YYYY.MM.DD')
}

function getFromTimeBeginWorkDate(strDate: string) {
  if (!strDate || strDate === "0") {
    return "未知工作年限";
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}年工作年限`
  }
}

function getFromTimeBirthday(strDate: string) {
  if (!strDate || strDate === "0") {
    return "年龄未知"
  } else {
    const year = dayjs(strDate).year()
    const now = dayjs()
    const diff = now.diff(strDate, 'year')
    return `${diff}岁`
  }
}

const talentStatus = computed(()=>{
  if (talent.value.updateTimeEmployeeStatus && talent.value.employeeStatus !== 0) {
    const now = dayjs()
    const diff = now.diff(talent.value.updateTimeEmployeeStatus, 'day')
    if (diff < 90) return talent.value.employeeStatus
    else return ''
  } else {
    return ''
  }
})

onMounted(()=>{

})

</script>

<style lang="sass" scoped>
.talent-search-item
  width: 100%
  color: #444
  cursor: pointer
  position: relative
  padding-left: 78px
  .talent-head
    position: absolute
    left: 0

  .talent-info
    line-height: 24px
    font-size: 13px
    .talent-basic-name

      .name
        font-weight: 700
        font-size: 16px
      .status
        font-weight: 400
        font-size: 13px
        color: #999

    .talent-mobile, .talent-email
      width: 100%
      overflow: hidden
      text-overflow: ellipsis
      white-space: nowrap

    .talent-intro
      color: #999
      width: 100%
      display: -webkit-box
      -webkit-line-clamp: 3
      -webkit-box-orient: vertical
      text-overflow: ellipsis
      overflow: hidden
</style>