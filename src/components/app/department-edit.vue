<template lang="pug">
.department-add-component
  .department-add-form
    a-form(layout="vertical", :modal="addDepartmentForm")
      a-form-item(
        label="部门名称",
        :rules="[{ required: true, message: '请填写部门名称' }]"
      )
        a-input(v-model:value="addDepartmentForm.name", placeholder="部门名称")

      a-form-item(
        label="父级部门",
        :rules="[{ required: true, message: '请选择父级部门' }]"
      )
        a-tree-select(
          v-model:value="addDepartmentForm.parentId",
          :tree-data="departmentTreeData",
          :tree-line="true"
        )

      //- a-form-item(
      //-   label="部门负责人",
      //-   :rules="[{ required: true, message: '请选择部门负责人' }]"
      //- )
      //-   a-input(v-model:value="addDepartmentForm.userId", placeholder="部门负责人")

  .department-add-action
    a-space(:size="8")
      a-button(@click="handleCancelClick", type="primary", ghost) 取消
      a-button(
        type="primary",
        :loading="status.loading",
        @click="handleComfirmClick"
      ) 确定
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { addCompanyDepartment, getCompanyDepartment } from '@/api/system/department'
import { useUserStore } from '@/store/user.store'

interface Department {
  id: number,
  companyId: number,
  parentId: number,
  deptName: string,
  userId: number,
  children?: Department[],
  title?: string,
  value?: number,
}


const emit = defineEmits(['close', 'refresh'])

const addDepartmentForm = reactive({
  name: '',
  parentId: null,
  userId: null
})

const userStore = useUserStore()

const status = reactive({
  loading: false
})

function handleCancelClick() {
  emit('close')
}

async function handleComfirmClick() {
  status.loading = true
  try {
    const res = await addCompanyDepartment(addDepartmentForm)
    emit('close')
    emit('refresh')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const departmentTreeData = ref([] as any[])

async function getDepartment() {
  try {
    const res = await getCompanyDepartment()
    departmentTreeData.value = _processDepartmentData(res.data)
  } catch (err: any) {
    message.error(err.message)
  }
}

function _processDepartmentData(departments: Department[]) {
  const deptMap = new Map<number, Department>()
  const deptTree = new Array<Department>()

  departments.forEach((dept) => {
    dept.children = []
    dept.title = dept.deptName
    dept.value = dept.id
    deptMap.set(dept.id, dept)

    // 如果父级部门ID是0， 则表示上级无企业
    if (dept.parentId === 0) deptTree.push(dept)
  })

  departments.forEach((dept) => {
    if (dept.parentId) {
      const parent = deptMap.get(dept.parentId)
      parent?.children?.push(dept)
    }
  })

  return [{ title: userStore.companyName, children: deptTree, value: 0, id: 0 }]
}

getDepartment()

</script>

<style lang="scss" scoped>
.department-add-component {
  height: 100%;
  position: relative;

  .department-add-form {
    padding: 24px;
  }

  .department-add-action {
    position: absolute;
    width: 100%;
    bottom: 0px;
    padding: 12px 24px;
    border-top: 1px solid #e8e8e8;
    text-align: right;
    box-sizing: border-box;
  }
}
</style>