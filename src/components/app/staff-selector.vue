<template lang="pug">
mixin staff-selector
  a-spin(:spinning="status.loading")
    .staff-selector
      .staff-selector-left
        .staff-selector-search
          a-input(@change="filterStaffName" v-model:value="searchKeyword" )
            template(#prefix)
              SearchOutlined
        .staff-selector-table
          a-table(
            :row-selection="{ selectedRowKeys:selectedStaff, onChange:onSelectChange, type: multi === false ? 'radio' : 'checkbox' }"
            :columns="columnConfig",
            :data-source="staffList",
            :pagination="false",
            size="middle"
            :scroll="{ y: winHeightStore.value - 350 }" 
          )
            template(#bodyCell="{ column, record }")
              //- 头像部分
              template(v-if="column.dataIndex === 'name'")
                a-avatar(:src="record.formalPhoto") {{ record.realName }}
                .staff-item__name {{ record.realName }}

              template(v-if="column.dataIndex === 'department'") 
                span {{ record.departments.length > 0 ? record.departments[0].name : '' }}
        .staff-selector-list

      .staff-selector-right
        .staff-select-selected(:style="{ height: winHeightStore.value - 247 + 'px'}")
          .staff-item(v-for="(staff, index) in selectedStaff", :key="index")
            a-avatar(:src="staff.formalPhoto") {{ staff.realName }}
            .staff-item__name {{ staff.realName }}
            .staff-item__action
              a-button(type="link" @click="() => { removeStaff(staff) }") 移除

a-modal(
  v-model:open="visible",
  @update:open="emit('update:visible')"
  title="选择员工",
  :width="720",
  style="top: 30px"
  :ok-button-props="{ disabled: selectedStaff.length === 0 }"
  :destroy-on-close="true",
  @ok="staffListConfirm"
)
  a-spin(:spinning="status.loading")
    +staff-selector
</template>

<script lang="ts" setup>

import { reactive, ref, toRefs, watch, onMounted, toRef } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { getCompanyUserList } from '@/api/system/users'
import { message } from 'ant-design-vue'
import { useWinHeightStore } from '@/store/winHeight.store'

const winHeightStore = useWinHeightStore()

interface Staff {
  id: number,
  name: string,
  department: string[],
  gender: string,
}

const props = defineProps<{
  visible: boolean, 
  multi: boolean,
  filter?: string,
  loading: boolean
}>()

const { visible, multi, loading} = toRefs(props)
const filter = toRef(props, 'filter')

const emit = defineEmits(['update:visible', 'select'])
const showModal = ref(false)
showModal.value = visible.value

const columnConfig = [
  { title: '员工', key: 'name', dataIndex: 'name' },
  { title: '部门', key: 'name', dataIndex: 'department' },
]

const selectedStaff = ref<any[]>([])
const staffList = ref([] as any[])
const originStaffList = ref([] as any[])
const searchKeyword = ref<any>('')
const status = reactive({
  loading: false
})

function onSelectChange(rowKeys: string[]) {
  selectedStaff.value = rowKeys
}

function removeStaff(staff: Staff) {
  const index = selectedStaff.value.indexOf(staff)
  selectedStaff.value.splice(index, 1)
}

async function getCheckboxProps(record: any) {
  if (multi.value === false) {
    return {
      disabled: true
    }
  }
  return {}
}

function filterByDepartment(companyUserList:any, department:number[]) {
  return companyUserList.filter((staff:any) => {
    return staff.departments.some((dept:any) => {
      return department.includes(dept.id)
    })
  })
}

const functionMap:{[key:string]: any} = {
  "department": filterByDepartment
}

async function applyFilter(companyUserList:any, filter:string) {
  try {
    const [funcName, funcParamStr] = filter.split(':') as [string, string]
    const funcParams = JSON.parse(funcParamStr)
    const func = functionMap[funcName]

    if (func) {
      return func(companyUserList, funcParams)
    } else {
      return companyUserList
    }
  } catch (err:any) {
    return companyUserList
  }
}

async function initUserList() {
  status.loading = true
  try {
    const res = await getCompanyUserList()
    const list = await applyFilter(res.data, filter.value!)
    originStaffList.value = list.map((staff: any) => {
      return Object.assign({}, staff, { key: staff })
    })
    staffList.value = list.map((staff: any) => {
      return Object.assign({}, staff, { key: staff })
    })
  } catch (err) {
    message.error('获取员工列表失败，请重试。')
  }
  status.loading = false
}

const filterStaffName = () => {
  let filtered = [] as any
  for (let i = 0; i < originStaffList.value.length; i++) {
    if (originStaffList.value[i].realName.includes(searchKeyword.value)) {
      filtered.push(originStaffList.value[i])
    }
  }
  staffList.value = filtered
}

function staffListConfirm() {
  emit('select', selectedStaff.value)
}

onMounted(() => {
  // initUserList()
})

watch (visible, (val) => {
  if (val) {
    initUserList()
  }
})

watch(filter, () => {
  initUserList()
})

</script>

<style lang="scss" scoped>
.staff-selector {
  border-radius: 6px;
  display: flex;
  border: 1px solid #e8e8e8;
  // min-height: 540px;

  &-left {
    flex: 1 1 auto;
    width: 50%;
  }

  &-right {
    flex: 1 1 auto;
    width: 50%;
    border-left: 1px solid #e8e8e8;
  }

  &-search {
    padding: 12px;
  }
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;

  &__name {
    padding: 0 12px;
    display: inline;
    flex: 1 1 auto;
  }

  &__action {
    width: 40px;
    flex: 0 0 auto;
  }
}

.staff-select-selected {
  // height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>