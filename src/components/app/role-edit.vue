<template lang="pug">
.role-add-component
  .role-add-form
    a-form(layout="vertical" ref="form" :model="roleForm")
      a-form-item(
        name="roleCnName",
        label="角色中文名称",
        :rules="[{ required: true, message: '请填写角色名称' }]"
      )
        a-input(v-model:value="roleForm.roleCnName", placeholder="角色名称")

      a-form-item(
        name="roleName",
        label="角色英文名称",
        :rules="[{ required: true, message: '请填写角色名称' }]"
      )
        a-input(v-model:value="roleForm.roleName", placeholder="Role english name.")

  .role-add-action
    a-space(:size="8")
      a-button(@click="handleCancelClick", type="primary", ghost) 取消
      a-button(
        type="primary",
        :loading="status.loading",
        @click="submit"
      ) 确定
</template>

<script lang="ts">
import { reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import { addCompanyRole } from '@/api/system/roles'

interface RoleInfo {
  permissions: string[],
  roleCnName: string,
  roleName: string
}

export default {
  emits: ['close','update:success'],
  setup(props: any, { emit }: any) {
    const form = ref()

    const roleForm = reactive({
      roleCnName: '',
      roleName: '',
      permissions: []
    } as RoleInfo)

    const status = reactive({
      loading: false
    })

    async function submit() {
      try {
        await form.value.validate()
        const res = await addCompanyRole(roleForm)
        message.success('增加角色成功！')
        emit('update:success')
      } catch (err) {
      }
    }

    async function handleCancelClick() {
      emit('close')
    }


    return {roleForm, form, status, submit, handleCancelClick}
  }
}
</script>

<style lang="scss" scoped>
.role-add-component {
  height: 100%;
  position: relative;

  .role-add-form {
    padding: 24px;
  }

  .role-add-action {
    position: absolute;
    width: 100%;
    box-sizing: border-box;
    bottom: 0px;
    padding: 12px 24px;
    border-top: 1px solid #e8e8e8;
    text-align: right;
  }
}
</style>