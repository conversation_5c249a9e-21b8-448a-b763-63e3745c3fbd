<template lang=pug>
.position-update-component
  .position-update-form
    a-spin(:spinning="status.loading")
      a-form(labelAlign="left" ref="formInstance" :model="form" layout="vertical")

        template(v-if="type == 'positionInfo'")
          a-form-item(
            name="positionTitle"
            label="职位名称"
            :rules="[{ type: 'string', required: true, message: '职位名称不能为空', trigger: ['change'] }]"
          )
            a-input(
              placeholder="请输入职位名称",
              v-model:value="form.positionTitle",
            )
              template(#addonAfter)
                a-select(
                  :options="dict.positionType"
                  placeholder="请选择职位类型",
                  v-model:value="form.type",
                )

          a-form-item(
            label="招聘数量"
            name="quantityRequired"
            :rules="[{ type: 'number', required: true, message: '招聘数量为必填项', trigger: ['change'] }, { type: 'number', min: 1, message: '招聘数量须大于1' }]"
          )
            a-input-number(
              v-model:value="form.quantityRequired",
              style="width:100%",
              placeholder="招聘数量,至少为1"
            )

          a-form-item(
            name="areaId"
            label="工作地点"
            :rules="[{ required: !form.isRemote, message: '工作地点不能为空', trigger: ['change'] }]"
          )
            a-row(:gutter="[16,16]")
              a-col(flex="auto")
                a-tree-select(
                  :fieldNames="{ label: 'title', value: 'id' }",
                  placeholder="请选择该职位的工作地点",
                  v-model:value="form.areaId",
                  :tree-data="dict.areas",
                  multiple
                )
              a-col(flex="auto")
                a-form-item-rest
                  a-checkbox(
                    v-model:checked="form.isRemote"
                  ) 远程工作

          a-row(:gutter="16") 
            a-col(:span="12")
              a-form-item(
                name="salaryFromK",
                label="薪资范围"
                :rules=`[{ asyncValidator: salaryFromKValidator, trigger:['change', 'blur']}]`,
              )
                a-input(suffix="k" v-model:value="form.salaryFromK" style="width:100%" placeholder="最低")
                  template(#addonAfter)
                    a-select(
                      show-search
                      v-model:value="form.salaryUnit" 
                      style="width:100px;"
                      :options="dict.CurrencyList"
                      :filter-option="filterOption"
                    )
            a-col(:span="12")
              a-form-item(
                name="salaryToK",
                label="-"
                :rules=`[{ asyncValidator: salaryToKValidator, trigger:['change', 'blur']}]`,
              )
                a-input(suffix="k" v-model:value="form.salaryToK" style="width:100%"  placeholder="最高")
                  template(#addonAfter)
                    a-select(
                      show-search
                      v-model:value="form.salaryUnit" 
                      style="width:100px;"
                      :options="dict.CurrencyList"
                      :filter-option="filterOption"
                    )

          a-row(:gutter="[16,16]")
            a-col(:span="12")
              a-form-item(                        
                name="salaryTimeUnit",
                label="计薪方式"
                :rules="[{required: true}]"
              )
                a-select(
                  v-model:value="form.salaryTimeUnit",
                  @change="(value:number)=>{ form.salaryTime = value!==0 ? 1 : form.salaryTime }",
                )
                  a-select-option(:value="0") 月薪
                  a-select-option(:value="1") 日薪
                  a-select-option(:value="2") 年薪

            a-col(:span="12" v-if="form.salaryTimeUnit === 0")
              a-form-item(                        
                name="salaryTime",
                label="计薪月数"
                :rules=`[
                  { asyncValidator: salaryCalcMonthShouldAbove12 }
                ]`)
                a-input(v-model:value="form.salaryTime" style="width:100%" suffix="个月")

          a-form-item(
            label="职能"
            name="functionId"
            :rules="[{ type: 'number', required: true, message: '请选择该职位职能', trigger: ['change'] }]"
          )
            a-select(
              placeholder="请选择该职位的职能",
              v-model:value="form.functionId",
              show-search,
              :filterOption="true",
              :options="dict.function",
            )

          a-row(:gutter="[12,12]")
            a-col(:span="12")

              a-form-item(
                label="是否是管理岗"
              )
                a-radio-group(v-model:value="form.isManager")
                  a-radio(:value="true") 是
                  a-radio(:value="false") 否

            a-col(:span="12")
              a-form-item(
                label="下属人数"
                :label-col="{ span: 8 }",
                :wrappercol="{ span: 16 }"
                v-if="form.isManager === true"
              )
                a-input-number(:min="0" v-model:value="form.underlingNumber" style="width:100%")

          a-form-item(
            label="面试流程"
            name="interviewProcess"
            :rules="[{ type: 'string', required: false, message: '面试流程为必填选项', trigger: ['change'] }]"
          )
            a-textarea(v-model:value="form.interviewProcess" :rows="4")

          a-form-item(
            label="职位描述"
            name="workDetail"
            :rules="[{ type: 'string', required: true, message: '职位描述为必填选项', trigger: ['change'] }]"
          )
            a-textarea(v-model:value="form.workDetail" :rows="4")

        template(v-if="type == 'positionRequirement'")
          a-form-item(label="性别要求")
            a-radio-group(v-model:value="form.requireGender")
              a-radio(:value="0") 不限
              a-radio(:value="1") 男
              a-radio(:value="2") 女

          a-form-item(label="年龄要求")
            a-row(:gutter="16") 
              a-col(:span="12")
                a-input-number(
                  style="width:100%" 
                  :min="ageRequire.min ? 18 : 0" max="80" 
                  placeholder="不限" 
                  v-model:value="ageRequire.min"
                )
              a-col(:span="12")
                a-input-number(
                  style="width:100%" 
                  :min="ageRequire.min || 18" max="80" 
                  v-model:value="ageRequire.max"
                  placeholder="不限" 
                )


          a-form-item(label="经验要求")
            //- a-row(:gutter="16") 
            //-   a-col(:span="12")
            //-     a-input-number(suffix="年" style="width:100%" placeholder="不限" v-model:value="form.requireWorkYearsFrom" )

            //-   a-col(:span="12")
            //-     a-input-number(suffix="年" style="width:100%"  v-model:value="form.requireWorkYearsTo"  placeholder="不限")
            a-select(
              placeholder="请选择经验要求",
              v-model:value="form.requireWorkYears",
              :options="dict.workYears",
            )

          a-form-item(
            label="学历要求"
            name="requireDegree"
            :rules="[{ required: false, message: '学历要求不能为空', trigger: ['change'] }]"
          )
            a-select(
              placeholder="请选择学历要求",
              v-model:value="form.requireDegree",
              :options="dict.degree",
            )

          a-form-item(
            label="学校要求"
            name="requireEliteSchool"
            :rules="[{ required: false, message: '负责的员工不能为空' }]"
          )
            a-select(
              placeholder="请选择学校要求",
              v-model:value="form.requireEliteSchool",
              :options="dict.eliteSchool",
            )

          a-form-item(label="对标公司")
            a-select(
              mode="tags"
              v-model:value="form.targetFirmsArray"
              style="width: 100%"
              placeholder="请输入公司名称，如：阿里 / 腾讯 / 百度",
              :token-separators="[',','，', ' ']",
              :notFoundContent="''"
            )

          a-form-item(label="偏好标签")
            a-select(
              mode="tags"
              v-model:value="form.tags"
              style="width: 100%"
              :token-separators="[',','，', ' ']",
              placeholder="请输入偏好标签"
            )

          a-form-item(label="任职要求")
            a-textarea(
              :auto-size="{ minRows: 4, maxRows: 6 }" 
              placeholder="请输入其他要求" 
              v-model:value="form.workRequirement"
            )

  .position-update-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="handleUpdatePosition" type="primary") 保存
</template>


<script lang=ts setup>
import { getAllAreaList, dictionary, getAllFunctionList, getWorkYearList, getCurrencyList } from '@/api/dictionary'
import { getPositionDetail } from '@/api/position'
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { message } from 'ant-design-vue'
import { nextTick, onMounted, reactive, ref, toRef, toRefs, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { updatePositionDetail } from '@/api/position'

interface PositionDetail {
  id: number,
  positionId: number | null,
  areaId: any[],
  customerId: number | null,
  functionId: number | null,
  positionTitle: string,
  priority: null | number,
  quantityRequired: null | number,
  requireAgeFrom: null | number,
  requireAgeTo: null | number,
  requireDegree: null | number,
  requireEliteSchool: null | number,
  requireEnglishLevel: null | number,
  requireWorkYears: null | number,

  salaryTime: null | number,
  salaryTimeUnit: number | null,
  salaryFromK: null | number,
  salaryFrom: null | number,
  salaryToK: null | number,
  salaryTo: null | number,
  salaryUnit: null | number,

  startDate: string,
  teamScale: number,
  workDetail: string,
  workRequirement?: string,
  targetFirms: string,
  targetFirmsArray: [],
  underlingNumber: number,
  isManager: boolean,
  type: number,
  tags: string[],
  isRemote: boolean,
  remoteWork: number
}

const dict = reactive({
  areas: [] as any,
  function: [],
  degree: [] as any[],
  eliteSchool: [{ label: '无要求', value: 0 }, { label: '211', value: 1 }, { label: '985', value: 2 }, { label: 'QS200', value: 4 }, { label: '211 & 985', value: 3 }, { label: '211 & 985 & QS200', value: 7 }],
  englishLevel: [{ label: '未知', value: 0 }, { label: '不限', value: 1 }, { label: '听说读写熟练', value: 2 }, { label: '日常交流', value: 3 }, { label: '阅读资料文献', value: 4 }],
  workYears: [] as any[],
  BDUsers: [{ label: 'aa', value: 1 }, { label: 'ab', value: 2 }, { label: 'ac', value: 3 }],
  PMUsers: [{ label: 'ba', value: 1 }, { label: 'bb', value: 2 }, { label: 'bc', value: 3 }],
  CAUsers: [{ label: 'ca', value: 1 }, { label: 'cb', value: 2 }, { label: 'cc', value: 3 }],
  positionType: [{ label: '实习', value: 0 }, { label: '校招', value: 1 }, { label: '社招', value: 3 }],
  CurrencyList: [] as any,
})

const status = reactive({
  initDict: false,
  loading: false,
  fetchCustomer: false,
  editTags: false,
})

const props = defineProps<{ positionId: number, type: 'positionInfo' | 'positionRequirement' }>()
const positionId = toRef(props, 'positionId')
const type = toRef(props, 'type')
const emit = defineEmits(['close', 'update'])
const form = ref<PositionDetail>({} as PositionDetail)
const ageRequire = reactive<{min: null | number, max: null | number}>({ min: null, max: null })
const formInstance = ref()

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

async function initDict() {
  status.initDict = true
  try {
    const dictPromises = [
      dictionary.getAllAreaList(), dictionary.getDegree(), getWorkYearList(), getAllFunctionList(), getCurrencyList()
    ]
    const [
      dictArea, dictDegree, dictWorkYear, dictFunction, dictCurrencyList
    ] = await Promise.all(dictPromises)

    const [areaTreeData, areaMap] = areaDictToTreeData(dictArea.data, false)
    dict.areas = areaTreeData

    dict.CurrencyList = dictCurrencyList.data.map((item: any, index: number) => { return { value: item.id, label: item.name } })
    dict.function = dictFunction.data.map((item: any, index: number) => { return { value: item.id, label: item.name } })
    dict.workYears.push({ value: 0, label: '未知' })

    for (let id in dictDegree.data) {
      dict.degree.push({ value: Number(id), label: dictDegree.data[id] })
    }

    for (let id in dictWorkYear.data) {
      dict.workYears.push({ value: Number(id), label: dictWorkYear.data[id] })
    }

  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

async function fetchPositionDetail(positionId: number | undefined) {
  if (!positionId) return
  status.loading = true
  try {
    const res = await getPositionDetail(positionId)
    form.value = res.data

    ageRequire.min = form.value.requireAgeFrom || null
    ageRequire.max = form.value.requireAgeTo || null

    form.value.isRemote = res.data.remoteWork ? true : false

    if (res.data.targetFirms) {
      form.value.targetFirmsArray = res.data.targetFirms.split(',')
    } else {
      form.value.targetFirmsArray = []
    }

    form.value.tags = res.data.tags.map((tag: any) => tag.name)
    form.value.functionId = res.data.functionId === 0 ? null : res.data.functionId
    form.value.teamScale = res.data.teamScale === 0 ? null : res.data.teamScale
    form.value.salaryFromK = res.data.salaryFrom / 1000
    form.value.salaryToK = res.data.salaryTo / 1000
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function handleUpdatePosition() {
  status.loading = true
  try {
    await formInstance.value.validate()
    form.value.requireAgeFrom = ageRequire.min || 0
    form.value.requireAgeTo = ageRequire.max || 0
    form.value.salaryFrom = Number(form.value.salaryFromK) * 1000
    form.value.salaryTo = Number(form.value.salaryToK) * 1000
    form.value.targetFirms = form.value.targetFirmsArray.join(',')
    form.value.remoteWork = form.value.isRemote ? 1 : 0
    const res = await updatePositionDetail(form.value)
    emit('update')
    message.success('职位更新成功！')
  } catch (err: any) {
    if (err.errorFields && err.errorFields.length > 0) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

async function handleRemoteChange(e: any) {
  // if (e.target.checked) { form.value.areaId = 99999999 }
  // else { form.value.areaId = null }
}

async function handleAreaChange(value: any) {
  // form.value.isRemote = false
}

const inputTag = ref<string>('')
const tagInputInstance = ref()
function newTag() {
  status.editTags = false
  if (inputTag.value) form.value!.tags.push(inputTag.value)
  inputTag.value = ''
  tagInputInstance.value.focus()
}

function deleteTag(index: number) {
  form.value.tags.splice(index, 1)
}

function tagEdit() {
  status.editTags = true
  nextTick(() => {
    tagInputInstance.value.focus()
  })
}

function salaryCalcMonthShouldAbove12(rule: any, value: string, callback: Function) {
  if (form.value.salaryTimeUnit === 0) {
    if (value === '') callback();
    if (!/^[\d]*$/.test(value)) callback(new Error('请填写数值'))
    if (value && Number(value) >= 12) callback()
    else callback(new Error('计算月数应大于等于12'))
  } else {
    callback()
  }
}

function salaryFromKValidator(rule: any, value: string, callback: Function) {
  try {
    if (value === '') {
      callback(new Error('请填写职位最小薪资'))
      return
    }
    if (Number(value).toString() === 'NaN') {
      callback(new Error('请填写数值'))
      return
    }
    const from = Number(form.value.salaryFromK)
    const to = Number(form.value.salaryToK)
    if (to && from > to) {
      callback(new Error('最小薪资应该小于最大薪资'))
      return
    }
    callback()
  } catch (err) {
    callback(new Error('请填写数值'))
  }
}

function salaryToKValidator(rule: any, value: string, callback: Function) {
  try {
    if (value === '') {
      callback(new Error('请填写职位最大薪资'))
      return
    }
    if (Number(value).toString() === 'NaN') {
      callback(new Error('请填写数值'))
      return
    }
    const from = Number(form.value.salaryFromK)
    const to = Number(form.value.salaryToK)
    if (from && to < from) {
      callback(new Error('最大薪资应大于最小薪资'))
      return
    }
    callback()
  } catch (err) {
    callback(new Error('请填写数值'))
  }
}

onMounted(() => {
  initDict()
  if (positionId.value) {
    fetchPositionDetail(props.positionId)
  }
})

</script>

<style lang="scss" scoped>
.position-update-component {
  padding-bottom: 56px;

  .position-update-form {
    padding: 12px 24px;
  }

  .position-update-action {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px 24px;
    text-align: right;
    box-sizing: border-box;
  }
}
</style>