<template lang="pug">
InfoSection(:title="'进展中项目'")
  span 12341234123
  a-spin(:spinning="status.jobLoading")
    template(v-for="(job, index) in jobList" v-if="jobList.length")
      a-divider(v-if="index !== 0")
      .job-item(@click="$router.push(`/job/${job.jobRequirementId}/detail`)")
        a-space.job-head(:size="0")
          JobPriority(:priority="job.priority")
          strong {{job.positionTitle}}
        .job-info {{ getJobInfoList(job).join(' · ') }}
        .job-user
          a-row(type="flex" :gutter="[8, 4]")
            a-col(flex="1 1 auto") BD: {{ getUsers('bd', job.properties) }}
            a-col(flex="1 1 auto") PM: {{ getUsers('pm', job.properties) }}
            a-col(flex="1 1 auto") CA: {{ getUsers('ca', job.properties) }}

    template(v-if="jobList.length == 0 && status.jobLoading === false")
      a-empty(description="暂无进行中项目")
</template>

<script lang="ts" setup>
import { getCustomerJobRequirements } from '@/api/job'
import { getPositionDetail } from '@/api/position'
import InfoSection from '@/components/info-section/info-section.vue'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref, toRef, watch } from 'vue'
import { formatSalary } from '@/utils/salary-helper'

const props = defineProps<{customerId:number}>()
const customerId = toRef(props, 'customerId')

const status = reactive({
  // detailLoading: false,
  jobLoading: false,
  // positionLoading: false,

  // showCreateInvoice: false,
  // showCreatePayment: false,
  // contractLoading: false,
})

const jobList = ref<any[]>([])
async function fetchCustomerJobRequirement(customerId: number) {
  status.jobLoading = true

  try {
    const res = await getCustomerJobRequirements(customerId)
    const tempJobList: any[] = []
    const positionReqeustList = res.data.jobRequirements.map((item: any, index: number) => {
      tempJobList.push({ jobRequirementId: item.id, properties: item.properties, priority: item.priority })
      return getPositionDetail(item.positionId)
    })

    const positionResList = await Promise.all(positionReqeustList)
    positionResList.forEach((item, index) => {
      tempJobList[index] = Object.assign({}, item.data, tempJobList[index])
    })

    jobList.value = tempJobList.sort((a, b) => {
      return  b.priority - a.priority
    })
  } catch (err: any) {
    message.error(err.message)
  }

  status.jobLoading = false
}

function getUsers(type: 'bd' | 'pm' | 'ca', source: any) {
  const users: any[] = []
  source.forEach((item: any, index: number) => {
    if (item.key?.toLowerCase() === type) {
      users.push(item.valueName)
    }
  })
  return users.join(' / ')
}

function getJobInfoList(job:any) {
  const list = []
  if (job.areaStr) list.push(job.areaStr)
  if (job.requireDegreeStr !== '未知') list.push(job.requireDegreeStr)
  if (job.requireWorkYearsStr !== '未知') list.push(job.requireWorkYearsStr)
  list.push(formatSalary(job))

  return list
} 

onMounted(()=>{
  fetchCustomerJobRequirement(customerId.value)
})

watch(()=>props.customerId, (value) =>{
  fetchCustomerJobRequirement(value)
})

</script>

<style lang="sass" scoped>

</style>
