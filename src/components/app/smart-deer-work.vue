<template lang="pug">
a-modal(
  title="小鹿推",
  :visible="visible",
  :destroy-on-close="true",
  :confirm-loading="status.loading",
  @ok="() => { matchSmartDeerPosition(); }",
  @cancel="() => { $emit('cancel'); }"
)
  a-spin(:spinning="status.loading")
    a-form(:model="form", layout="vertical", ref="formInstance")
      // 检查是否有 id 并且大于0，大于0是修改。其他是新增，新增不显示主键ID
      a-form-item.form-item(
        label="推荐人才姓名",
        name="realName",
        :rules="[{ required: true, message: '未选择候选人' }]"
      )
        a-input(v-model:value="form.realName", disabled)
      a-form-item.form-item(
        label="推荐者用户（从用户池随机）",
        name="recommender",
        :rules="[{ required: true, message: '请选择推荐人' }]"
      )
        a-select(
          show-search,
          placeholder="请输入用户姓名或手机号",
          :default-active-first-option="false",
          :show-arrow="false",
          :filter-option="false",
          :options="recommenders",
          v-model:value="form.recommender",
          @search="getRecommenderList"
        )
      a-form-item.form-item(label="求职者用户（自动生成用户）")
        a-select(
          show-search,
          placeholder="自动生成用户",
          :default-active-first-option="false",
          :show-arrow="false",
          :filter-option="false",
          :options="delivers",
          @search="getDeliverList",
          disabled
        )
      a-form-item.form-item(
        label="目标公司",
        name="company",
        :rules="[{ required: true, message: '请选择目标公司' }]"
      )
        a-select(
          show-search,
          placeholder="请输入公司名称",
          :default-active-first-option="false",
          :show-arrow="false",
          :filter-option="false",
          :options="targetCompanies",
          v-model:value="form.company",
          @search="getCompanyList",
          @change="handleCompanyChange"
        )
      a-form-item.form-item(
        label="目标职位",
        name="position",
        :rules="[{ required: true, message: '请选择目标职位' }]"
      )
        a-select(
          show-search,
          placeholder="请输入职位标题",
          :default-active-first-option="false",
          :show-arrow="false",
          :filter-option="false",
          :options="targetPositions",
          v-model:value="form.position",
          @search="getPositionList"
        )
</template>

<script lang="ts">
import {
  getLingLuTuiRecommenders,
  getLingLuTuiDelivers,
  getLingLuTuiCompanies,
  getLingLuTuiPositions,
  matchLingLuTuiPosition
} from '@/api/linglu'
import { message, FormInstance } from 'ant-design-vue'
import { reactive, ref, onMounted, toRefs, watch, SetupContext } from 'vue'

export default {
  name: 'SmartDeerWork',
  props: ['visible', 'talent'],
  emits: ['success', 'cancel'],
  setup(props:any , { emit }:SetupContext) {
  
    const { talent } = toRefs(props) as any

    const form = reactive({
      realName: '',
      talentId: null as number | null,
      recommender: null as number | null,
      position: null as number | null,
      company: ''
    })

    const status = reactive({
      loading: false,
      currentCompanyId: 0,
    })

    const delivers = ref([] as any[])
    const recommenders = ref([] as any[])
    const targetCompanies = ref([] as any[])
    const targetPositions = ref([] as any[])

    onMounted(() => {
      form.realName = talent.value.realName
      form.talentId = talent.value.id
    })

    watch(talent, (value, old) => {
      form.realName = talent.value.realName
      form.talentId = talent.value.id
    })

    function getCompanyList(keyword: string) {
      getLingLuTuiCompanies(keyword).then((res) => {
        targetPositions.value = []
        targetCompanies.value = res.data.map((item: any) => {
          return {
            value: item.companyId,
            label: item.companyName
          }
        })
      })
    }

    function handleCompanyChange(companyId: number) {
      status.currentCompanyId = companyId
      targetPositions.value = []
    }

    function getPositionList(keyword: string) {
      getLingLuTuiPositions(status.currentCompanyId, keyword).then((res) => {
        targetPositions.value = res.data.map((item: any) => {
          return {
            value: item.positionId,
            label: item.positionName
          }
        })
      })
    }

    function getRecommenderList(keyword: string) {
      getLingLuTuiRecommenders(keyword).then((res) => {
        recommenders.value = res.data.map((mail: any) => {
          return {
            value: mail,
            label: mail
          }
        })
      })
    }

    function getDeliverList(keyword: string) {
      getLingLuTuiDelivers(keyword).then((res) => {
        // console.log(res)
      })
    }

    const formInstance = ref<FormInstance>()
    async function matchSmartDeerPosition() {
      try {
        await formInstance.value!.validate()
      } catch (err) {
        console.log(err)
        return false
      }

      status.loading = true
      try {
        const res = await matchLingLuTuiPosition(form.recommender!, form.talentId!, form.position!)
        message.success('推荐成功！')
        emit('success')
      } catch (err: any) {
        message.error(err.message)
      }
      status.loading = false
    }

    return {
      getCompanyList, handleCompanyChange, getPositionList, getRecommenderList,matchSmartDeerPosition,
      getDeliverList, form,delivers, status, recommenders, targetCompanies, targetPositions, formInstance
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item {
  margin-bottom: 12px;
}
</style>
