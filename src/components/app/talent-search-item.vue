<script lang="ts" setup>
import { onMounted, ref, toRef } from 'vue'
import dayjs from "dayjs"
import { MailOutlined, MobileOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { getFromTimeBirthday, getFromTimeBeginWorkDate } from "@/utils/string"
import { watch } from 'vue';

const props = defineProps<{ item: any, degree: {}, reportContent?: any }>()
const emit = defineEmits(['open', 'chat'])
const item = toRef(props, 'item')
const showReport = toRef(props, 'reportContent')

// TODO: Degree 从字典中获取
const degree = toRef(props, 'degree')

const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(Number(val) * 1000).format('YYYY.MM')
}

const talentExp = ref<any[]>([])
const talentEdu = ref<any[]>([])

function sortTalentExp(webSearchTalent: any) {
  if (!webSearchTalent || !webSearchTalent.exps) {
    talentExp.value = []
  } else {
    talentExp.value = webSearchTalent.exps.sort((a: any, b: any) => {
      const aTime = dayjs(a.startTime).valueOf()
      const bTime = dayjs(b.startTime).valueOf()
      return bTime - aTime
    })
  }

  if (!webSearchTalent || !webSearchTalent.educations) {
    talentEdu.value = []
  } else {
    talentEdu.value = webSearchTalent.educations.sort((a: any, b: any) => {
      const aTime = dayjs(a.startTime).valueOf()
      const bTime = dayjs(b.startTime).valueOf()
      return bTime - aTime
    })
  }
}

onMounted(() => {
  sortTalentExp(props.item)
})

watch(() => props.item, (val) => {
  sortTalentExp(val)
})

const getSite = (val: string) => {
  switch (Number(val)) {
    case 1:
      return 'ITP'
    case 2:
      return '猎聘'
    case 3:
      return '脉脉'
    case 4:
      return '领英'
    case 5:
      return 'Boss直聘'
    case 6:
      return 'SmartDeer'
    default:
      break;
  }
}

const getBtnText = (val: string) => {
  if (!isNaN(Number(val))) {
    return '打开ITP简历'
  }

  if (val.indexOf('liepin') !== -1) {
    return '打开猎聘'
  }

  if (val.indexOf('maimai') !== -1) {
    return '打开脉脉'
  }

  if (val.indexOf('linkedin') !== -1) {
    return '打开领英'
  }

  if (val.indexOf('zhipin') !== -1) {
    return '打开Boss直聘'
  }
}

const getScore = (score: any) => {
  if (typeof score === 'string') {
    try {
      return JSON.parse(score)
    } catch (e) {
      return { final: 0 }
    }
  }
  return score
}

</script>

<template lang="pug">
//- v-for="(item, index) in searchTalentList"
//- :key="index"
//- @click="handleClickTalent(item)"
.talent-search-item
  .talent-head-img
    a-avatar(:size="80", shape="square", :src="item.avatar") {{ item.name }}
    .site {{ getSite(item.site) }}
  .talent-basic 
    .talent-basic-name {{ item.name }}
    .talent-basic-other
      a-space(:size="12")
        span(v-if="item.sex") {{ item.sex === 1 ? '男' : '女' }}
        span(v-else) 性别未知
        span {{ item.age ? `${item.age}岁` : getFromTimeBirthday(item.birthday) }}
        span {{ getFromTimeBeginWorkDate(item.beginWorkDate) }}
        span {{ degree[item.highestDegree + ''] }}
        span(v-if="item.nowLocation") {{ item.nowLocation }}
        div(v-if="item.phone")
          MobileOutlined(style="margin-right: 4px; color: #ccc;")
          span {{ item.phone }}
        div(v-if="item.email")
          MailOutlined(style="margin-right: 6px; color: #ccc;")
          span {{ item.email }}

    .btns
      a-space( :size="10")
        a-button(
          v-if="item.imUserId"
          type="primary" 
          @click.stop="() => emit('chat', {imUserId: item.imUserId})"
        ) 开聊
        template(v-if="item.site !== '1' && Array.isArray(item.repeatTalents)")
          a-button(
            v-for="(r, j) in item.repeatTalents"
            :key="j"
            type="primary" 
            ghost
            @click.stop="() => emit('open', {resumeUrl: r.talentId})"
          ) ITP {{ r.name }}

    .talent-basic-status {{ item.online === 'true' ? '在线' : item.activeStatus }}
  .talent-score(v-if="item.score" ) AI 人才评分 &nbsp;
    strong {{ getScore(item.score).final }} &nbsp;
    a-popover(trigger="hover")
      template(#content)
        .score-detail
          .score-header
            .score-label 评分项
            .score-value 分数
          template(v-for="(value, key) in getScore(item.score)")
            template(v-if="key !== 'final'")
              .score-item
                .score-label {{ key }}: 
                .score-value {{ Array.isArray(value) ? value.join('、') : value }}
      InfoCircleOutlined(style="margin-right: 8px; color: #ccc;")
  .talent-tags
    span(v-if="Array.isArray(item.fields)" )
      a-space(:size="[0, 8]" wrap)

  .talent-report(v-if="showReport")
    a-popover(trigger="hover" placement="top")
      template(#content)
        .report-popover-content {{ showReport }}
      .talent-report-content {{ showReport.length > 200 ? showReport.slice(0, 200) + '...' : showReport }}

  .talent-info
    .exps    
      .exps-item(v-for="(exp, i) in talentExp" :key="i")
        .exps-time {{ formatTime(exp.startTime) }} - {{ formatTime(exp.endTime) }}
        div {{ exp.company }} 
        div(v-if="exp.position") · {{ exp.position }}

    .educations
      .educations-item(v-for="(education, i) in talentEdu" :key="i")
        .educations-time {{ formatTime(education.startTime) }} - {{ formatTime(education.endTime) }}
        div {{ education.school }} 
        div(v-if="education.major") · {{ education.major }}
</template>

<style lang="scss" scoped>
.talent-search-item {
  padding: 24px 0px 24px 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #f0f0f0;
  min-height: 128px;

  &:hover {
    background-color: #fafafa;
    transition: all 0.2s;
  }

  .site {
    background: RGBA(0, 0, 0, .2);
    backdrop-filter: blur(10px);
    color: #fff;
    height: 23px;
    line-height: 23px;
    text-align: center;
  }

  .talent-head-img {
    position: absolute;
    left: 0px;
    top: 24px;

    .site {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .talent-basic {
    display: flex;
    align-items: center;
    position: relative;

    &-name {
      font-size: 16px;
      font-weight: bold;
    }

    &-other {
      margin-left: 24px;
      color: #888;
      font-size: 14px;
    }

    &-status {
      position: absolute;
      right: 0;
      color: #7F7F7F;
    }

    .btns {
      position: absolute;
      right: 0px;
      top: -10px;
    }
  }

  .talent-tags {
    margin-top: 6px;
  }

  .talent-report {
    margin-top: 12px;
    padding: 12px;
    background-color: #f8f8f8;
    border-radius: 4px;
    height: 60px;

    &-content {
      color: #666;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      cursor: pointer;
    }
  }

  .talent-info {
    display: flex;
    margin-top: 10px;
  }
}
.report-popover-content {
  width: 600px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  line-height: 1.5;
  white-space: pre-line;
}
.exps {
  width: 50%;
  padding-right: 20px;
  border-right: 1px solid #d7d7d7;
  font-size: 13px;

  &-time {
    width: 140px;
    min-width: 140px;
  }

  &-item {
    display: flex;
    margin-top: 6px;

    &:first-child {
      font-weight: 700;
      font-size: 13px;
      margin-top: 0px;
    }
  }
}

.educations {
  width: 50%;
  padding-left: 20px;

  &-time {
    width: 140px;
    min-width: 140px;
  }

  &-item {
    display: flex;
    margin-top: 6px;

    &:first-child {
      font-weight: 700;
      font-size: 13px;
      margin-top: 0px;
    }
  }
}

.red {
  color: #ff4d4f;
}
.talent-score {
  margin-top: 5px;
  font-size: 16px;
  font-family: 'Bebas';
  cursor: pointer;
}

.score-detail {
  padding: 8px;
  min-width: 300px;
  
  .score-header {
    display: flex;
    margin-bottom: 8px;
    font-weight: bold;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 4px;
  }

  .score-item {
    display: flex;
    margin-bottom: 4px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .score-label {
    width: 120px;
    flex-shrink: 0;
  }

  .score-value {
    flex: 1;
    word-break: break-all;
  }
}
</style>
