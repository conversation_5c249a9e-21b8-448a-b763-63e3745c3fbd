<template lang="pug">
.create-payment-claim-modal
  a-modal(
    :open="open" 
    title="创建回款认领单"
    width="720px"
    :maskClosable="false"
    :destroyOnClose="true"
    :confirmLoading="status.loading"
    @ok="handleConfirm"
    @cancel="()=>{emit('update:open', false)}"
  )
    a-form(:model="form" ref="formInstance" layout="vertical")
      a-row(:gutter="[16,0]")
        a-col(:span="12")
          a-form-item(
            label="交易流水号"
            :rules="[{ required: true, message: '请输入交易流水号' }]"
            name="paySerialNumber"
          )
            a-input(v-model:value="form.paySerialNumber")

        a-col(:span="12")
          a-form-item(
            label="付款主体"
            name="payAccountName"
            :rules="[{ required: true, message: '请输入付款主体' }]"
          )
            a-input(v-model:value="form.payAccountName")

        a-col(:span="12")
          a-form-item(label="收款账户" name="financeAccountId" :rules="[{ required: true, message: '请选择收款账户' }]")
            a-select(
              style="width: 100%"
              option-label-prop="label"
              v-model:value="form.financeAccountId"
            )
              template(v-for="(item, index) in options.entityList")
                a-select-option(
                  :value="item.id" 
                  :label="`${item.companyFullName} - ${item.bankFullName}`" 
                  placeholder="请选择收款账户"
                )
                  .companyEntity {{ item.companyFullName }}
                  .compnayAccount {{ item.bankFullName }}

        a-col(:span="12")
          a-form-item(
            label="收款金额" 
            name="totalAmount"
            :rules="[{ type: 'number', required: true, message: '请输入收款金额' }]"
          )
            a-input-number(
              style="width: 100%"
              v-model:value="form.totalAmount"
              :step="0.01"
            )
              template(#addonAfter)
                a-select(
                  v-model:value="form.currencyType"
                  style="width: 100px"
                  v-model="form.currencyType"
                  :options="options.currencyType"
                )

        a-col(:span="12")
          a-form-item(label="收款日期")
            a-date-picker(
              v-model:value="dates.dueDate"
              style="width: 100%"
              :allowClear="false"
            )

        a-col(:span="12")
          a-form-item(label="付款方式" name="payMethod" :rules="[{ required: true, message: '请选择付款方式' }]")
            a-select(:options="options.paymentMethod" v-model:value="form.payMethod")

</template>

<script lang="ts" setup>
import { defineComponent, reactive, toRaw, ref, toRef } from 'vue'
import type { UnwrapRef } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'
import dayjs, { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'

import { getCurrencyList, getPayMethodList, getPaymentMethodList } from '@/api/dictionary'
import { createPaymentClaim, getEntityList, setReceivePayment } from '@/api/finance'
import { getProcessInstances, getJobRequireSearch } from '@/api/job'
import { getDateRanges } from '@/utils/util'
import { onMounted, watch } from 'vue'
import {sendFeishuNotice} from '@/api/notice'
import { useUserStore } from '@/store/user.store'


const status = reactive({
  loading: false
})

const emit = defineEmits(['update:open', 'update'])
const props = defineProps<{ open: boolean}>()
const open = toRef(props, 'open')
const formInstance = ref()
const options = reactive({
  paymentMethod: [] as any[],
  currencyType: [] as any[],
  entityList: [] as any[]
})

const form = reactive<{
  currencyType: number | null,
  dueDate: string,
  financeAccountId: number | null,
  payAccountName: string,
  payMethod: number,
  paySerialNumber: string,
  totalAmount: null | number
}>({
  currencyType: 0,
  dueDate: "",
  financeAccountId: null,
  payAccountName: "",
  payMethod: 1,
  paySerialNumber: "",
  totalAmount: null,
})

const dates = reactive({
  dueDate: dayjs()
})

async function handleConfirm() {
  status.loading = true
  try {
    await formInstance.value.validate()
    const params = {
      ...form,
      dueDate: dates ? dates.dueDate.format('YYYY-MM-DD') : '',
      totalAmount: form.totalAmount! * 100
    }
    const res = await createPaymentClaim(params)
    message.success('创建成功, 请在财务，回款认领中查看')

    const userStore = useUserStore()
    const messages = []
    const financeAccount = options.entityList.find((item: any) => item.id === form.financeAccountId)
    const currency = options.currencyType.find((item: any) => item.value === form.currencyType)
    const payMethod = options.paymentMethod.find((item: any) => item.value === form.payMethod)
    messages.push(`付款主体: ${form.payAccountName}`)
    messages.push(`收款账户: ${financeAccount.companyFullName} - ${financeAccount.bankFullName}`)
    messages.push(`收款金额: ${form.totalAmount?.toLocaleString()} ${currency.label}`)
    messages.push(`收款日期: ${dates.dueDate.format('YYYY-MM-DD')}`)
    messages.push(`付款方式: ${payMethod.label}`)
    messages.push(`创建人: ${userStore.name}`)
    await sendNotification(res.data, messages)

    emit('update')
  } catch (err: any) {
    if (err.errorFields) {
      message.error(err.errorFields[0].errors)
    } else {
      message.error(err.message)
    }
  }
  status.loading = false
}

async function sendNotification(claimId: number, messages: string[]) {
  if (import.meta.env.VITE_VUE_APP_BUILD_ENV !== "production") return
  const notificationHeadr = {
    "template": "blue",
    "title": { "content": "有一笔回款等待领取", "tag": "plain_text" }
  }

  const actions = [
    { title: '去ITP系统领取', url: `https://web.itp.smartdeer.work/finance/payment/list` },
  ]

  const notificationActions = {
    "tag": "action",
    "actions": actions.map(action => ({
      "tag": "button",
      "text": { "content": action.title, "tag": "plain_text" },
      "url": action.url
    }))
  }

  const notificationMessages = messages.map(message => ({
    "tag": "div",
    "text": { "content": message, "tag": "plain_text" }
  }))

  await sendFeishuNotice('41149b28-6fe6-4b21-8723-8ee2a984fa6b', {
    msg_type: "interactive",
    card: {
      "header": notificationHeadr,
      "elements": [
        ...notificationMessages,
        notificationActions
      ],
    }
  })
}

async function initDict() {
  try {
    const pres = await getPaymentMethodList()
    options.paymentMethod = pres.data.map((item: any) => ({
      label: item.name,
      value: item.id
    }))

    const eres = await getEntityList()
    options.entityList = eres.data

    const currencyType = await getCurrencyList()
    options.currencyType = currencyType.data.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
  } catch (err: any) {
    message.error(err.message)
  }
}

onMounted(() => {
  initDict()
})
</script>

<style lang="sass" scoped>
.create-payment-form
  padding-bottom: 56px
  height: 100%
  position: relative
  box-sizing: border-box

  .form-body
    padding: 20px

  .form-action 
    position: absolute
    bottom: 0
    background-color: #fff
    border-top: 1px solid #f0f0f0
    width: 100%
    padding: 12px 24px
    text-align: right
    box-sizing: border-box
</style>