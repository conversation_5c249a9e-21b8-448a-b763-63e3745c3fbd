<template lang="pug">
mixin customer-detail
  .customer-detail
    InfoSection(:editable="editable" @edit="handleEditBasicInfo")
      .customer-basic-info
        .customer-basic-info__avatar
          template(v-if="customer.customerLogo")
            .customer-basic-info__logo(:style="`background-image:url(https://image.itp.smartdeer.work/images/${customer.customerLogo});`")
          template(v-else)
            a-avatar(:size="96" alt="avatar" shape="square" :src="`https://image.itp.smartdeer.work/images/${customer.customerLogo}`") {{customer.customerFullName}}
        .customer-basic-info__content
          h2 {{customer.customerFullName}}
          .customer-basic-info__basic
            a-space(:size="12")
              span 公司规模: {{ customer.companyScaleStr }}
              span 所属行业: {{ customer.industryStr }}
              span 融资规模: {{ customer.investStatusStr }}
          .customer-basic-info__contact
            a-space
              a-tag(v-for="(tag, index) in customer.tags") {{tag.name}}

    InfoSection(title="基础信息")
      a-descriptions(:column="2" :labelStyle="{color: '#999'}")
        a-descriptions-item(label="所在地区") {{customer.areaStr}}
        a-descriptions-item(label="所属行业") {{customer.industryStr}}
        a-descriptions-item(label="公司规模") {{customer.companyScaleStr}}
        a-descriptions-item(label="公司性质") {{customer.companyTypeStr}}
        a-descriptions-item(label="融资规模") {{customer.investStatusStr}}
        a-descriptions-item(label="详细地址") {{customer.customerAddress}}
        a-descriptions-item(label="客户网站") {{customer.customerWebsite}}
        a-descriptions-item(label="扩展类别") {{ customer.extendIndustryStr }}
        a-descriptions-item(label="客户级别") {{ customer.customerPriority }}

    InfoSection(title="公司介绍")
      pre {{customer.customerIntroduction}}

.customer-detail-component
  +customer-detail

</template>

<script lang="ts" setup>
import { getCustomerDetail } from '@/api/customer'
import { getCustomerJobRequirements } from '@/api/job'
import { message } from 'ant-design-vue'
import { onActivated, onMounted, reactive, ref, shallowRef } from 'vue'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import { getPositionDetail, getPositionsByCustomer } from '@/api/position'
import InfoSection from '@/components/info-section/info-section.vue'
import { toRef, watch } from 'vue'

const props = defineProps<{ customerId: number, editable: boolean }>()
const customerId = toRef(props, 'customerId')
const editable = toRef(props, 'editable')

const drawer = reactive({
  show: false,
  title: '',
  props: {},
  component: null as any
})

const status = reactive({
  detailLoading: false,
  jobLoading: false,
  positionLoading: false,

  showCreateInvoice: false,
  showCreatePayment: false,
  contractLoading: false,
})

const customer = ref<any>({})
async function fetchCustomerDetail(customerId: number) {
  status.detailLoading = true
  try {
    const res = await getCustomerDetail(customerId)
    customer.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.detailLoading = false
}

function handleEditBasicInfo() {

}

onMounted(async () => {
  fetchCustomerDetail(customerId.value)
})

watch(() => props.customerId, (value) => {
  fetchCustomerDetail(value)
})

</script>

<style lang="scss" scoped>
.section-spliter {
  display: block;
  border-bottom: 1px solid #f0f0f0;
}

section.detail-section {
  padding: 24px 24px 24px 36px;
  border: 1px solid #fff;
  position: relative;

  &.editable {
    .detail-edit-action {
      display: none;
      position: absolute;
      right: 24px;
      top: 24px;
      color: #FF9111;
    }

    &:hover {
      border: 1px solid #FF9111;
      cursor: pointer;

      .detail-edit-action {
        display: block;
      }
    }
  }

  .pre-present {
    display: block;
    unicode-bidi: embed;
    white-space: pre-wrap;
  }

  p {
    margin-bottom: 0;
  }

  &:last-child {
    border-bottom: none;
  }

  .section-head {
    h4 {
      font-size: 20px;
      margin-bottom: 0px;
      position: relative;
      line-height: 24px;

      &::before {
        content: '';
        display: block;
        height: 30px;
        width: 4px;
        background-color: #FF9111;
        border-radius: 2px;
        position: absolute;
        left: -16px;
        top: 50%;
        margin-top: -15px;
      }
    }
  }

  .section-body {
    margin-top: 24px;
    line-height: 24px;
  }
}

.customer-detail {
  background-color: #fff;
  border-radius: 8px;

  .customer-basic-info {
    display: flex;

    &__logo {
      background-size: contain;
      width: 96px;
      height: 96px;
      background-position: center;
      background-repeat: no-repeat;
    }

    &__content {
      padding-left: 16px;
      line-height: 32px;
    }

    &__name {
      font-size: 20px;
      display: flex;
      align-items: center;
    }

    &__tag {
      margin: 0 12px;
    }
  }
}

.customer-positions {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;

  h4.title {
    font-size: 20px;
    position: relative;
    line-height: 24px;
    margin: 0;
    padding-left: 18px;
    margin-bottom: 24px;

    &::before {
      content: '';
      display: block;
      height: 20px;
      width: 4px;
      background-color: #FF9111;
      border-radius: 2px;
      position: absolute;
      left: 0;
      top: 2px;
    }
  }

  .job-list {
    .job-head {
      font-size: 16px;
      transition: all .3s;
      margin-bottom: 8px;

      em {
        font-size: 14px;
        font-style: normal;
        margin-left: 8px;
        color: #999;
      }
    }

    .job-info {
      color: #999;
      margin-bottom: 4px;
    }

    .job-item {
      cursor: pointer;

      &:hover {
        .job-head {
          color: #FF9111;
          transition: all .3s;
        }
      }
    }

    .job-user {
      color: #999;
    }
  }
}

.customer-contact {
  line-height: 32px;

  &__tag {
    margin-left: 12px;
  }
}

.recommand-position {
  background-color: #fff;
  border-radius: 8px;

  &__foot {
    padding: 16px;
    text-align: center;
  }

  &-item {
    padding: 24px 24px 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    transition: all .2s;

    &:hover {
      background-color: #fafafa;
      cursor: pointer;
      transition: all .2s;
    }

    &__position {
      strong {
        font-size: 16px;
      }
    }

    &__time {
      text-align: right;
      color: #999;
      font-size: 12px;
    }
  }
}

.company-info {
  .title {
    color: #999;
  }
}

.position-list {
  margin: -24px 0;

  .position-item {
    margin: 8px 0;
    padding: 16px 24px 16px 36px;

    &:hover {
      background-color: #f9f9f9;
      cursor: pointer;
    }

    &__position {
      strong {
        font-size: 18px;
        line-height: 36px;
      }
    }
  }
}

.position-title {
  line-height: 32px;

  .title {}
}
</style>