<template lang="pug">
mixin job-selector
  .job-selector
    a-select(
      v-model:value="currentProcessInstance"
      :options="jobSelectorOptions"
      style="width: 100%"
      @change="handleProcessInstanceChange"
    )

mixin job-progress
  template(v-if="currentStep !== null")
    .job-progress-container(ref="progressContainer")
      template(v-if="currentTask.taskDefinitionKey !== 'position_pm_talent_to_obsolete'")
        a-steps(progress-dot :current="currentStep" direction="horizontal" size="small")
          a-step(v-for="(item, index) in jobHeaderDefinition" :title="item.title")

    .job-actions-btns(v-if="currentTask")
      a-row(:gutter="[16, 16]")
        a-col(:span="6")
          a-button(v-if="currentTask.taskDefinitionKey != 'position_pm_talent_to_obsolete'" ghost block type="primary"  @click="() => {tackAction('weed_out')}") 淘汰
        a-col(:span="18")
          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_get'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_get')}") 推荐给客户

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_get_to_customer'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_get_to_customer')}") 约面试

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_interview'")
            a-row(:gutter="[16, 16]")
              a-col(:span="12")
                a-button(ghost block type="primary" @click="() => {showInterviewList()}") 面试结果反馈
              a-col(:span="12")
                a-button(type="primary" block @click="() => {tackAction('position_pm_talent_interview')}" v-if="currentTask.localVariables.isFinalInterview == '1' || currentTask.localVariables.finalInterviewResult") 谈薪

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_to_salary'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_to_salary')}") 已谈薪

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_to_offer'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_to_offer')}") 确认OFFER

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_to_inspect'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_to_inspect')}") 已背调

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_to_hired'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_to_hired')}") 已入职

          template(v-if="currentTask.taskDefinitionKey === 'position_pm_talent_in_keep'")
            a-button(type="primary" block @click="() => {tackAction('position_pm_talent_in_keep')}") 已过保

mixin job-logs
  JobLog(
    @update="handleUpdate" 
    :talentId="jobLogParams.talentId" 
    :processInstanceId="jobLogParams.processInstanceId" 
    :jobRequirementId="jobLogParams.jobRequirementId" 
    :showAction="true"
  )

.talent-job-progress(v-if="jobSelectorOptions.length")
  a-spin(:spinning="status.loading")
    +job-selector
    +job-progress
    TalentJobReportButton(
      :talentId="talentId"
      :processInstanceId="jobLogParams.processInstanceId"
      :jobRequirementId="jobLogParams.jobRequirementId"
    )
    +job-logs

  JobProgressActions(ref="jobActionsInstance", :jobRequirementId="currentJobId" @update="handleUpdate")
</template>

<script lang="ts" setup>
import { onMounted, watch, ref, toRef, nextTick, reactive } from 'vue'
import { getTalentJob } from '@/api/job'
import { getJobRequirementHeader, getProcessTaskList } from '@/api/position'
import JobProgressActions from '@/components/app/job-progress-actions.vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import JobLog from './job-log.vue'
import TalentJobReportButton from './talent-job-report-button.vue'

const jobActionsInstance = ref()
const props = defineProps<{ talentId: number, jobId?:number }>()
const talentId = toRef(props, 'talentId')
const jobId = toRef(props, 'jobId')
const jobSelectorOptions = ref<any[]>([])
const talentJobMap = new Map()

const currentProcessInstance = ref<any>()
const currentTask = ref<any>()
const currentStep = ref<number | null>(null)
const currentJobId = ref<number | null>(jobId.value || null)

const jobLogParams = reactive({
  talentId: null as null | number,
  processInstanceId: null as null | string,
  jobRequirementId: null as null | number,
})

const status = reactive({
  loading: false,
})

function refresh() {
  fetchTalentJob(props.talentId)
}

async function fetchTalentJob(talentId: number) {
  try {
    const talentJob = await getTalentJob(talentId)
    jobSelectorOptions.value = []

    if (talentJob.data.length) {
      jobSelectorOptions.value = talentJob.data.map((item: any, index: number) => {
        talentJobMap.set(item.task.processInstanceId, item)
        return { 
          label: `${item.jobRequirement.processName} - ${item.jobRequirement.customerName}`, 
          value: item.task.processInstanceId
        }
      })

      let findIndex = -1
      if (currentProcessInstance.value) {
        findIndex = talentJob.data.findIndex((item:any) => item.task.processInstanceId == currentProcessInstance.value)
      } else {
        findIndex = talentJob.data.findIndex((item: any) => item.jobRequirement.id == jobId.value)
      }
      // const findIndex = talentJob.data.findIndex((item: any) => item.jobRequirement.id == jobId.value)
      const dataIndex = findIndex == -1 ? 0 : findIndex

      currentTask.value = Object.assign({}, talentJob.data[dataIndex].task)
      const jobRequirementId = talentJob.data[dataIndex].jobRequirement.id
      const processInstanceId = talentJob.data[dataIndex].task.processInstanceId

      currentJobId.value = jobRequirementId
      currentProcessInstance.value = processInstanceId

      jobLogParams.talentId = talentId
      jobLogParams.processInstanceId = talentJob.data[dataIndex].task.processInstanceId
      jobLogParams.jobRequirementId = jobRequirementId

      fetchJobHeader(jobRequirementId)
    }
  } catch (err: any) {
    console.log(err.message)
    message.error(err.message)
  }
}

const jobHeaderDefinition = ref<any[]>([])
const progressContainer = ref()

async function fetchJobHeader(jobId: number) {
  try {
    currentStep.value = null
    const res = await getJobRequirementHeader(jobId)

    jobHeaderDefinition.value = []
    for (let key in res.data) {
      jobHeaderDefinition.value.push(
        Object.assign({}, res.data[key], { title: key })
      )
    }
    jobHeaderDefinition.value.sort((a, b) => a.sort - b.sort)

    jobHeaderDefinition.value.forEach((item: any, index: number) => {
      if (item.taskDefinitionKey === currentTask.value.taskDefinitionKey) {
        currentStep.value = index
        const scrollLeft = ((index - 1) * 140) + 30
        nextTick(() => {
          progressContainer.value.scrollLeft = scrollLeft
        })
      }
    })
  } catch (err: any) {
    message.error(err.message)
  }
}

async function handleProcessInstanceChange(processInstanceId: string) {
  status.loading = true
  try {
    const talentJob = talentJobMap.get(processInstanceId)    
    const jobRequirementId = talentJob.jobRequirement.id

    currentTask.value = Object.assign({}, talentJob.task)

    currentJobId.value = jobRequirementId
    jobLogParams.talentId = talentId.value
    jobLogParams.processInstanceId = processInstanceId
    jobLogParams.jobRequirementId = jobRequirementId

    fetchJobHeader(jobRequirementId)
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function tackAction(actionKey: string, taskId: string | undefined, isUpdate: boolean) {
  if (taskId === undefined) {
    taskId = currentTask.value.id
  }
  jobActionsInstance.value.trigger(actionKey, taskId, talentId.value, isUpdate)
}

async function showInterviewList() {
  jobActionsInstance.value.showInterviewList(currentTask.value.id, talentId.value)
}

async function handleUpdate() {
  fetchTalentJob(talentId.value)
}

onMounted(() => {
  const talentId = props.talentId
  if (talentId) fetchTalentJob(talentId)
})

watch(() => props.talentId, (value) => {
  if (value) fetchTalentJob(value)
})

defineExpose({ refresh })

</script>

<style lang="scss" scoped>
.talent-job-progress {
  padding: 24px;
  border-radius: 8px;
  background-color: #fff;
  margin-bottom: 16px;

  .job-selector {
    margin-bottom: 24px;
  }

  .job-progress-container {
    overflow-x: scroll;
    padding: 16px 0;
    margin-bottom: 24px;

    &::-webkit-scrollbar {
      display: none;
      // width: 3px;
      background-color: RGBA(0, 0, 0, .05);
      height: 4px;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: RGBA(0, 0, 0, .1);
      border-radius: 2px;
    }
  }
}
</style>