<template lang="pug">
mixin position-selector
  .job-selector
    .job-selector-left
      .job-selector-search
        a-input(@change="debouncedFetchPosition" v-model:value="searchKeyword")
          template(#prefix)
            SearchOutlined
      .job-selector-table
        a-table(
          :loading="status.loading"
          :row-selection="{ selectedRowKeys:selectedPositionKeys, onSelect:onSelectChange, hideSelectAll:!multi }"
          :columns="columnConfig",
          :data-source="positionList",
          :pagination="pagination",
          @change="handlePageChange"
          size="middle"
        )
          template(#bodyCell="{ column, record }")
            //- 头像部分
            template(v-if="column.dataIndex === 'position'")
              a-popover(@visibleChange="() => {fetchPositionDetail(record)}" :mouseEnterDelay=".5" title="职位详情" placement="right")
                .jobitem__title {{ record.parsedProperties.jobTitle }}
                a-space(:size="12")
                  span 公司: {{record.customerName}}
                  span 工作地: {{record.parsedProperties.locationId}}
                  //- span 年薪: {{getAnnualSalary(record)}}
                template(#content)
                  a-spin(:spinning="status.postionLoading")
                    template(v-if="positionDetail")
                      .jobdetail__summary
                        .jobitem__title {{ positionDetail.positionTitle }}
                        div 项目ID: {{ record.id }}
                        a-space(:size="12")
                          span 工作地: {{positionDetail.areaStr}}
                          span 年薪: {{formatSalary(record)}}
                      .jobdetail__tags
                        a-space(:size="12")
                          a-tag(v-for="(item, index) in positionDetail.tags") {{item.name}}
                      .jobdetail__company()
                        a-avatar.jobdetail__company-logo(:size="48")
                        .jobdetail__company-name  {{ customerDetail.customerFullName }}
                        .jobdetail__company-desc
                          a-space
                            span 规模: {{customerDetail.companyScaleStr}}
                            span {{customerDetail.companyTypeStr}}
                            span 行业: {{customerDetail.industryStr}}
                      .jobdetail__info
                        a-row(:gutter="[16,16]")
                          a-col(:span="12") HC数量: {{positionDetail.jobRequirementCount}}
                          a-col(:span="12") 工作地点: {{positionDetail.areaStr}}
                          a-col(:span="12") 职能: {{positionDetail.functionStr}}
                          a-col(:span="24") 对标公司: {{positionDetail.targetFirms}}
                    template(v-else)
                      a-empty

    .job-selector-right
      .jobselect-selected
        .jobitem(v-for="(position, index) in selectedPositions", :key="index")
          .jobdetail__summary
            .jobitem__title {{ position.parsedProperties.jobTitle }}
            a-space(:size="12")
              span 公司: {{position.customerName}}
              span 工作地: {{position.parsedProperties.locationId}}
          .jobitem__action
            a-button(type="link" @click="() => { removeStaff(position) }") 移除

a-modal(
  v-model:open="showModal",
  title="选择职位",
  :width="840",
  :body-style="{ padding: '12px' }",
  :ok-button-props="{ disabled: selectedPositions.length === 0, loading: loading }"
  :destroyOnClose="true",
  @ok="staffListConfirm"
)
  +position-selector
</template>
  
<script lang="ts" setup>
// 需要做一个单选还是多远的参数
import { onMounted, reactive, ref, toRef, toRefs, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getPositionDetail, getPositionList, getJobRequirementDetail } from '@/api/position'
import { debounce } from '@/utils/util'
import { getCustomerDetail } from '@/api/customer'
import { getJobRequireSearch } from '@/api/job'
import { formatSalary } from '@/utils/salary-helper'

interface Staff {
  id: number,
  name: string,
  department: string[],
  gender: string,
}

const props = defineProps<{
  visible: boolean, 
  multi: boolean, 
  selected?: any[], 
  loading: boolean, 
  jobId?: number
}>()
const emit = defineEmits(['update:visible', 'select'])

const visible = toRef(props, 'visible')
const loading = toRef(props, 'loading')
const multi = toRef(props, 'multi')
const showModal = ref(false)
const searchKeyword = ref('')
showModal.value = visible.value

const columnConfig = [
  { title: '职位', key: 'name', dataIndex: 'position' },
]

const pagination = reactive({
  pageSize: 10,
  total: 0,
  current: 1,
  showSizeChanger: false
})

const selectedPositionKeys = ref<any[]>([])
const selectedPositions = ref<any[]>([])

const status = reactive({
  loading: false,
  postionLoading: false
})

watch(visible, (value, oldValue) => {
  if (value !== oldValue) showModal.value = value
  // if (value === false) reset()
})

// 向外抛出当前modal的状态
watch(showModal, (value, oldValue) => {
  // if (value === false) reset()
  emit('update:visible', value)
})

const positionMap = new Map()

function reset() {
  selectedPositionKeys.value = []
  selectedPositions.value = []
  positionList.value = []
  pagination.current = 1
  pagination.total = 0
}

function onSelectChange(record:any, selected:any) {

  // console.log(rowKeys)
  // selectedPositionKeys.value = rowKeys
  // selectedPositions.value = rowKeys.map((item, index) => {
  //   return positionMap.get(item)
  // })

  if(multi.value) {
    if (selectedPositionKeys.value.includes(record.id)) {
      const index = selectedPositionKeys.value.indexOf(record.id)
      selectedPositionKeys.value.splice(index, 1)
      selectedPositions.value.splice(index, 1)
    } else {
      selectedPositionKeys.value.push(record.id)
      selectedPositions.value.push(record)
    }
  } else { 
    if (selectedPositionKeys.value.includes(record.id)) {
      selectedPositionKeys.value = []
      selectedPositions.value = []
    } else {
      selectedPositionKeys.value = [record.id]
      selectedPositions.value = [record]
    }
  }
}

function removeStaff(positionKey: any) {
  const index = selectedPositionKeys.value.indexOf(positionKey)
  selectedPositionKeys.value.splice(index, 1)
  selectedPositions.value.splice(index, 1)
}

async function fetchCustomerDetail(position: any) {
  try {
    const res = await getCustomerDetail(position.customerId)
    position.customer = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

const positionDetail = ref<any | null>({})
const customerDetail = ref<any>({})
async function fetchPositionDetail(job: any) {
  status.postionLoading = true
  try {
    customerDetail.value = {}
    positionDetail.value = null
    const customer = await getCustomerDetail(job.customerId)
    const position = await getPositionDetail(job.positionId)
    customerDetail.value = customer.data
    positionDetail.value = position.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.postionLoading = false
}

const positionList = ref<any[]>()

interface fetchJobListParams {
  keyword: string
  current: number
  size: number
}

async function fetchJobList() {
  const keyword = searchKeyword.value
  status.loading = true
  try {
    positionMap.clear()
    const res = await getJobRequireSearch({ name: keyword, status: 1, current: pagination.current, size: pagination.pageSize })
    positionList.value = res.data.jobRequirements.map((item: any) => {
      let parsedProperties: any = {}
      item.properties.forEach((item: any, index: number) => {
        parsedProperties[item.key] = item.valueName
      })
      const postionTemp = Object.assign({}, item, { key: item.id, customer: null, parsedProperties })
      positionMap.set(item.id, postionTemp)
      return postionTemp
    })
    pagination.total = res.data.total
  } catch (err: any) {

  }
  status.loading = false
}

const debouncedFetchPosition = debounce(handleKeywordSearch)

function handleKeywordSearch() {
  pagination.current = 1
  fetchJobList()
}

function handlePageChange(page: any) {
  pagination.current = page.current
  fetchJobList()
}

function staffListConfirm() {
  emit('select', selectedPositions.value)
}

const _getJobRequirementDetail = async () => {
  if (!props.jobId) return
  const res = await getJobRequirementDetail(Number(props.jobId))

  const data = res.data

  let parsedProperties: any = {}
  data.properties.forEach((item: any, index: number) => {
    parsedProperties[item.key] = item.valueName
  })
  const postionTemp = Object.assign({}, data, { key: data.id, customer: null, parsedProperties })

  const isHas = positionMap.get(postionTemp.key)
  
  if (isHas) {
    selectedPositionKeys.value.push(postionTemp.key)
    selectedPositions.value.push(postionTemp)
  } else {
    positionList.value?.unshift(postionTemp)
    positionMap.set(postionTemp.key, postionTemp)
    selectedPositionKeys.value.push(postionTemp.key)
    selectedPositions.value.push(postionTemp)
  }
}

onMounted(async () => {
  await fetchJobList()

  _getJobRequirementDetail()
})


</script>
  
<style lang="scss" scoped>
.job-selector {
  border-radius: 6px;
  display: flex;
  border: 1px solid #e8e8e8;
  min-height: 540px;

  &-left {
    flex: 1 1 auto;
    width: 50%;
  }

  &-right {
    flex: 1 1 auto;
    width: 50%;
    border-left: 1px solid #e8e8e8;
  }

  &-search {
    padding: 12px;
  }
}

.jobitem {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;

  &__title {
    font-weight: bold;
    word-break: break-all;
  }

  &__action {
    width: 40px;
    flex: 0 0 auto;
  }
}

.jobdetail {
  &__company {
    padding: 12px 0;
    padding-left: 60px;
    position: relative;
    padding-bottom: 12px;
    border: 1px solid #e8e8e8;
    border-left: none;
    border-right: none;
    margin-top: 12px;
  }

  &__company-logo {
    position: absolute;
    left: 0px;
  }

  &__company-name {
    // font-size: 16px;
    font-weight: bold;
  }

  &__detail {
    padding-top: 12px;
  }

  &__summary {
    flex: 1 1 auto;
  }

  &__tags {
    padding-top: 12px;
  }

  &__info {
    padding-top: 12px;
  }
}
</style> 
  