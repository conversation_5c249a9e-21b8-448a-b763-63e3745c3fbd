<template lang="pug">
.account-sider-component
  .account-sider-title 
    h2 账户设置
  a-menu(mode="inline")
    a-menu-item(key="1" @click="$router.push('/account/home')") 账户信息
      template(#icon)
        UserOutlined
    a-menu-item(key="2" @click="$router.push('/account/bind')") 账户绑定
      template(#icon)
        BlockOutlined
    a-menu-item(key="3" @click="$router.push('/account/password/change')") 更换密码
      template(#icon)
        LockOutlined
</template>

<script lang="ts" setup>
import { UserOutlined, LockOutlined  } from '@ant-design/icons-vue'
</script>

<style lang="scss" scoped>
.account-sider-component {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;

  .account-sider-title {
    padding: 12px 24px;
    border-bottom: 1px solid #f9f9f9;

    h2 {
      margin: 0;
    }
  }
}

.ant-menu-inline {
  border: none;
}
</style>