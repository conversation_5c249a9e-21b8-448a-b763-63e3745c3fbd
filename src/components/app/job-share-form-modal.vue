<template lang="pug">
a-modal(:open="open" title="项目分配" width="720px" @cancel="emit('update:open', false)" @ok="handleSubmit" destroyOnClose)
  a-form(:model="form" layout="vertical" ref="dataFormRef")
    a-spin(:spinning="status.loading")
      a-row(:gutter="[32,0]")
        a-col(:span="24")
          a-form-item(label="共同做单公司")
            a-select(v-model:value="form.companyIds" mode="multiple" show-search :options="dict.companies" @change="handleChangeCompanyIds")

        a-col(:span="24")
          a-form-item(label="职位托管公司(托管公司可以管理职位流程和职位信息)")
            a-select(v-model:value="form.controlCompanyIds" mode="multiple" show-search :options="dict.controlCompanies")

        a-col(:span="12")
          a-form-item(label="职位名称")
            a-input(v-model:value="form.coveredPositionName" placeholder="请输入职位名称")
        a-col(:span="12")
          a-form-item(label="客户名称")
            a-input(v-model:value="form.coveredCustomerName" placeholder="请输入客户名称")

        a-col(:span="12")
          a-form-item(label="职位介绍")
            a-textarea(v-model:value="form.coveredPositionDesc" placeholder="职位介绍" :rows="16")
        a-col(:span="12")
          a-form-item(label="客户介绍")
            a-textarea(v-model:value="form.coveredCustomerDesc" placeholder="客户介绍" :rows="16")

</template>

<script lang="ts" setup>
import { getCustomerDetail } from '@/api/customer'
import { getJobShareInfo, shareJobToOtherCompany } from '@/api/job'
import { getCompanyList } from '@/api/platform'
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import { useUserStore } from '@/store/user.store'
import { message } from 'ant-design-vue'
import { ref, toRef, reactive, onMounted, watch } from 'vue'

const props = defineProps<{ jobId: number, open: boolean }>()
const jobId = toRef(props, 'jobId')
const open = toRef(props, 'open')
const dataFormRef = ref<any>()
const emit = defineEmits(['update:open', 'update'])

const status = reactive({
  loading: false
})

const dict = reactive({
  companies: [] as any[],
  controlCompanies: [] as any[]
})

const form = reactive<{
  id?: number,
  companyIds: number[],
  controlCompanyIds: number[],
  allowPublicControl: number,
  coveredCustomerDesc: string,
  coveredCustomerName: string,
  coveredPositionName: string,
  coveredPositionDesc: string,
  jobRequirementId: number,
}>({
  companyIds: [] as number[],
  controlCompanyIds: [] as number[],
  allowPublicControl: 1,
  coveredCustomerDesc: '',
  coveredCustomerName: '',
  coveredPositionName: '',
  coveredPositionDesc: '',
  jobRequirementId: jobId.value,
})

const userStore = useUserStore()

let allCompanies: any[] = []

async function initDict() {
  try {
    const res = await getCompanyList()
    allCompanies = res.data

    dict.companies = allCompanies.map((item: any, index: number) => {
      return { label: item.companyName, value: item.id }
    }).filter((item: any, index: number) => {
      // 不能邀请自己的公司
      return item.value !== userStore.companyId
    })
  } catch (err: any) {
    message.error(err.message)
  }
}

async function handleChangeCompanyIds() {
  dict.controlCompanies = allCompanies.map((item: any, index: number) => {
    return { label: item.companyName, value: item.id }
  }).filter((item: { label: string, value: number }, index: number) => {
    return form.companyIds.includes(item.value) && item.value !== userStore.companyId
  })

  form.controlCompanyIds = form.controlCompanyIds.filter((item: number, index: number) => {
    return dict.controlCompanies.some((option: any) => {
      return option.value === item
    })
  })
}

const jobDetail = ref<any>()
async function fetchJobRequirementDetail(jobId: number) {
  try {
    const res = await getJobRequirementDetail(jobId)
    jobDetail.value = res.data

    await fetchPositionDetail(res.data.positionId)
    await fetchCustomerDetail(res.data.customerId)
  } catch (err: any) {
    message.error(err.message)
  }
}

// 职位详情部分
const positionDetail = ref<any>({})
async function fetchPositionDetail(positionId: number) {
  try {
    const res = await getPositionDetail(positionId)
    positionDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

// 客户详情部分
const customerDetail = ref<any>({})
async function fetchCustomerDetail(customerId: number) {
  try {
    const res = await getCustomerDetail(customerId)
    customerDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
}

async function handleSubmit() {
  try {
    form.jobRequirementId = Number(jobId.value)
    const res = await shareJobToOtherCompany(form)
    message.success('项目分配成功！')
    emit('update:open', false)
    emit('update')
  } catch (err: any) {
    message.error(err.message)
  }
}

async function initForm(jobId: number) {
  status.loading = true
  try {
    dataFormRef.value?.resetFields()
    const res = await getJobShareInfo(jobId)
    if (res.data) {
      form.id = res.data.id
      form.companyIds = res.data.companyIds
      form.controlCompanyIds = res.data.controlCompanyIds
      form.coveredCustomerName = res.data.coveredCustomerName
      form.coveredPositionName = res.data.coveredPositionName
      form.coveredCustomerDesc = res.data.coveredCustomerDesc
      form.coveredPositionDesc = res.data.coveredPositionDesc
      handleChangeCompanyIds()
    } else {
      await fetchJobRequirementDetail(jobId)
      form.coveredCustomerName = customerDetail.value.customerFullName
      form.coveredCustomerDesc = customerDetail.value.customerIntroduction
      form.coveredPositionName = positionDetail.value.positionTitle
      form.coveredPositionDesc = positionDetail.value.workDetail
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

onMounted(async () => {
  initDict()
})

watch(() => props.open, (val, oldVal) => {
  if (val) initForm(jobId.value)
})

</script>

<style lang="sass" scoped>
</style>