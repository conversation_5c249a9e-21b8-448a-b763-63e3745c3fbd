<template lang="pug">
InfoSection(title="人才要求" :editable="editable")
  a-descriptions(:column="2" :labelStyle="{color: '#999'}" )
    a-descriptions-item(label="性别要求")
      span(v-if="position?.requireGender == 1") 男
      span(v-else-if="position?.requireGender == 2") 女
      span(v-else) 不限
    a-descriptions-item(label="年龄要求")
      span(v-if="position?.requireAgeFrom && position?.requireAgeTo") {{ position?.requireAgeFrom }}岁 ~ {{ position?.requireAgeTo }}岁
      span(v-if="position?.requireAgeFrom && !position?.requireAgeTo") {{ position?.requireAgeFrom }}岁以上
      span(v-if="!position?.requireAgeFrom && position?.requireAgeTo") {{ position?.requireAgeTo }}岁以下
      span(v-if="!position?.requireAgeFrom && !position?.requireAgeTo") 不限
    a-descriptions-item(label="经验要求") {{ position?.requireWorkYearsStr }}
    a-descriptions-item(label="学历要求") {{ position?.requireDegreeStr }}
    a-descriptions-item(label="学校要求" :span="2") {{ position?.requireEliteSchoolStr }} 
    a-descriptions-item(label="对标公司" :span="2") {{ position?.targetFirms  }}
    a-descriptions-item(label="偏好标签" :span="2")
      a-space(:size="[0, 8]" wrap)
        a-tag(v-for="item in position?.tags" :key="item.id") {{ item.name }}
    a-descriptions-item(label="职位要求" :span="2") 
      pre {{ position.workRequirement }}
</template>

<script lang="ts" setup>
import InfoSection from '@/components/info-section/info-section.vue'
import { reactive, toRefs } from 'vue'
import { ref } from 'vue'

const props = defineProps<{ position: any, editable:boolean }>()
const { position, editable } = toRefs(props)

</script>

<style lang="sass" scoped>

.update-actions
  position: absolute
  bottom: 0
  background-color: #fff
  border-top: 1px solid #f0f0f0
  width: 100%
  padding: 12px 24px
  text-align: right
  box-sizing: border-box

</style>