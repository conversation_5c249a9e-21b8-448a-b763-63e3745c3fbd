<template lang="pug">
.talent-search-item(v-if="talent")
  .pipeline
    .pipeline-list-title
    .pipeline-item(v-for="(pipeline, i) in talentPipelines" :key="i")
      a-popover(placement="right")
        template(#content)
          .pipeline-detail
            .pipeline-detail-title 当前流程状态：{{ pipeline.completed === 1 ? '已完成' : '进行中' }}
            .pipeline-detail-interview
              .pipeline-detail-interview-title(v-if="pipeline.interviews.length !== 0") 面试信息
              .pipeline-detail-interview-item(v-for="(interview, j) in pipeline.interviews" :key="j")
                .interview-item
                  span.interview-serial 第 {{ j + 1 }} 轮面试
                  span.interview-time {{ interview.startDate }}
                  span.interview-result {{ interview.interviewResult }}
            .pipeline-detail-offer(v-if="pipeline.offer.position_pm_talent_to_offer_pass === '1'")
              .pipeline-detail-offer-title Offer 信息
              .popover-detail-label 薪资货币：
              .popover-detail-value {{ pipeline.offer.currencyType }}
              .popover-detail-label 薪资类型：
              .popover-detail-value {{ pipeline.offer.salaryUnit }}
              .popover-detail-label Offer 薪资：
              .popover-detail-value {{ formatAmount(pipeline.offer.salary) }}
            .pipeline-detail-onboarding(v-if="pipeline.offer.position_pm_talent_to_offer_pass === '1'")
              .pipeline-detail-onboarding-title 入职信息
              .popover-detail-label 预计入职日期：
              .popover-detail-value {{ pipeline.offer.expectOnboardingDate }}
              .popover-detail-label(v-if="pipeline.hire.position_pm_talent_to_hired_pass === '1'") 实际入职日期：
              .popover-detail-value {{ pipeline.hire.onboardingDate }}
            .pipeline-detail-overinsurance(v-if="pipeline.offer.position_pm_talent_to_offer_pass === '1'")
              .pipeline-detail-overinsurance-title 保证期信息
              .keep-expect_overinsurance-date 预计过保日期：{{ pipeline.offer.expectOverinsureDate }}
              .keep-actual-overinsurance-date(v-if="pipeline.keep.position_pm_talent_in_keep_pass === '1'" ) 实际过保日期：{{ formatDate(pipeline.keep.overInsuranceDate) }}
        .pipeline-info
          a-badge(status="success")
          a-tag(v-if="pipeline.keep.position_pm_talent_in_keep_pass === '1'" color="#FF9111" ) 已过保
          a-tag(v-else-if="Object.keys(pipeline.obsolete).length !== 0" color="#2665FC" ) 已中止
          a-tag(v-else color="#2db7f5" ) {{ pipeline.currentTaskName }}
          router-link(:to="`/job/${pipeline.jobRequirementId}/detail`") {{ pipeline.processName }} &nbsp;- &nbsp;
          router-link(:to="`/customer/${pipeline.jobRequirementId}/detail`") {{ pipeline.customerName }} &nbsp
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { computed, onMounted, ref, toRef, watch } from 'vue'
import { MailOutlined, MobileOutlined } from '@ant-design/icons-vue'
import degreeDict from '@/utils/degree-dict'

const props = defineProps<{talent: any}>()
const talent = toRef(props, 'talent')
const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(val).format('YYYY.MM')
}

const talentEducations = ref([])
const talentExperiences = ref([])
const talentPipelines = ref([])

function getTalentExp(talent:any) {
  talentEducations.value = talent.talentEducations.sort((a: any, b: any) => {
    const aTime = dayjs(a.fromDate).valueOf()
    const bTime = dayjs(b.fromDate).valueOf()
    return bTime - aTime
  }).slice(0, 2)

  const expCount = 4 - talentEducations.value.length

  talentExperiences.value = talent.talentExperiences.sort((a: any, b: any) => {
    const aTime = dayjs(a.fromDate).valueOf()
    const bTime = dayjs(b.fromDate).valueOf()
    return bTime - aTime
  }).slice(0, expCount)

  talentPipelines.value = talent.pipelineSnapshots;
}

onMounted(() => {
  getTalentExp(talent.value)
})

function formatDate (dateStr: string) {
  const date = new Date(dateStr)
  return dayjs(date).format("YYYY-MM-DD")
}

function formatAmount (amount: number) {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  return formatter.format(amount)
}

watch(()=> props.talent, (val) => {
  getTalentExp(val)
})

</script>

<style lang="sass" scoped>
.talent-search-item
  width: 380px
  .pipeline-info
    width: 380px
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis
    line-height: 30px
.pipeline-detail
  width: 300px
  .pipeline-detail-title
    line-height: 26px
    padding-bottom: 5px
  .pipeline-detail-interview
    border-top: 1px #EEE solid
  .pipeline-detail-interview-title
    font-weight: bold
    line-height: 28px
  .interview-serial, .interview-time, .interview-result
    line-height: 26px
  .interview-time, .interview-result
    margin-left: 10px
  .pipeline-detail-offer
    border-top: 1px #EEE solid
  .pipeline-detail-offer-title
    font-weight: bold
    line-height: 28px
  .pipeline-detail-onboarding
    border-top: 1px #EEE solid
  .pipeline-detail-onboarding-title
    font-weight: bold
    line-height: 28px
  .pipeline-detail-overinsurance
    border-top: 1px #EEE solid
  .pipeline-detail-overinsurance-title
    font-weight: bold
    line-height: 28px
  .popover-detail-label, .popover-detail-value
    display: inline-block
    line-height: 26px
  .popover-detail-label
    width: 100px
  .popover-detail-value
    width: 200px
</style>