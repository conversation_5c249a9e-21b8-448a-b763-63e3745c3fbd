<template lang="pug">

mixin performance-summary
  section.performance-summary
    a-row.summary-info(:gutter="[40, 0]")
      a-col(:span="8" v-for="(role, index) in summaryList")
        .title {{ role.role }}
        .summary-item(v-for="(user, index) in role.users")
          a-collapse()
            a-collapse-panel()
              template(#header)
                span(v-if="user.user?.realName") {{ user.user?.realName }}
                a-tag(v-else :color="'volcano'" :bordered="false" ) 未执行动作
              div(v-for="(action, index) in user.actions")
                span {{ action.performancePoint.actionName }} {{ action.pointRate }}% ({{ formateCommissionNumber(action.pointRate * totalCommission)}})
              template(#extra)
                span {{ getUserTotalRate(user.actions) }}% ({{ formateCommissionNumber(getUserTotalRate(user.actions) * totalCommission) }})

mixin performance-list
  section.section-performance-list
    .title 业绩分配

    .config-header
      a-row(type="flex" align="middle" )
        a-col(:span="2")
          span 角色

        a-col(:span="22")
          a-row()
            a-col(:span="10")
              span 动作
            a-col(:span="4")
              span 人员
            a-col(:span="6")
              span 比例
            a-col(:span="4")
              span 预期业绩

    template(v-for="(item, index) in configList")
      .role-group
        a-row(align="middle")
          a-col(:span="2")
            .role {{ item.role }}

          a-col(:span="22")
            template(v-for="(item, index) in item.list")
              .config-item
                a-row(align="middle")
                  a-col(:span="10") 
                    div {{ item.performancePoint.actionName  }}
                    div.duties {{ item.performancePoint.duties }}
                  a-col(:span="4") 
                    .user(v-if="item.companyUser?.realName") 
                      span {{ item.companyUser?.realName }}
                      a-button(v-if="item.performancePoint.userChangeable" type="link" @click="()=>handleCancelAllocation(item)")
                        template(#icon)
                          CloseCircleOutlined

                    .user(v-else)
                      a-tag(:color="'volcano'" :bordered="false" ) 未执行动作
                      a-button(v-if="item.performancePoint.userChangeable" type="link" @click="()=>handleAddAllocation(item)")
                        template(#icon)
                          PlusCircleOutlined

                  a-col(:span="6")
                    a-input-number(v-if="item.performancePoint.rateChangeable" v-model:value="item.pointRate" :formatter="value => `${value}%`" :min="1" :max="10")
                      template(#suffix)
                        span %
                    span(v-else) {{ item.pointRate }}%

                  a-col(:span="4")
                    span {{ formateCommissionNumber(totalCommission * item.pointRate) }}

    .config-footer
      a-row(type="flex" align="middle" )
        a-col(:span="2")
          span

        a-col(:span="22" )
          a-row(align="middle")
            a-col(:span="10")
              span 预期待分配业绩: ${{ parseFloat(totalCommission.toFixed(2)).toLocaleString() }}
            a-col(:span="10")
              span 分配总百分比：{{ totalRate }}%
            a-col(:span="4")
              a-button(@click="handleConfirmAllocation" type="primary" :loading="status.saving") 业绩确认

.offer-performance-config
  template(v-if="status.empty")
    a-empty(description="暂无业绩分配信息")
  .config-container(v-else)
    a-spin(:spinning="status.loading")
      +performance-summary
    a-spin(:spinning="status.loading")
      +performance-list

  StaffSelector(v-model:visible="status.showStaffSelector" :multi="false" @select="handleSelectStaff")

</template>

<script lang="ts" setup>

import { reactive, ref } from 'vue'
import StaffSelector from './staff-selector.vue'
import { MinusCircleOutlined, PlusOutlined, CloseCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import { getJobRequirementDetail } from '@/api/position'
import { Modal, message } from 'ant-design-vue'
import { onMounted } from 'vue'
import { getCompanyPerformanceConfig, getTaskContributors, initTaskAllocation, saveTaskPerformanceAllocate } from '@/api/performance'
import { toRef, watch } from 'vue'
import { showConfirmDialog } from 'vant'
import { computed } from 'vue'

const status = reactive({
  loading: false,
  saving: false,
  empty: false,
  showStaffSelector: false,
})

const props = defineProps<{ processInstanceId: string }>()
const processInstanceId = toRef(props, 'processInstanceId')

const configList = ref<any>([])
const summaryList = ref<any>([])

function formateCommissionNumber(value: number) {
  return `$${parseFloat((value / 100).toFixed(2)).toLocaleString()}`
}

async function getTaskContributeInfo(processInstanceId: string) {
  status.loading = true
  try {
    let res = await getTaskContributors(processInstanceId)
    if (res.data.length === 0) {
      await initTaskAllocation(processInstanceId)
      res = await getTaskContributors(processInstanceId)
    }

    configList.value = await processPerformanceData(res.data)
    summaryList.value = await getSummaryList(configList.value)
    totalCommission.value = await getTaskTotalCommission(res.data)

  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const totalCommission = ref(0)
async function getTaskTotalCommission(config: any[]) {
  let item = config.find((item: any) => item.pointRate === 1)
  if (!item) item = config.find((item: any) => item.pointRate === 2)
  return item.expectCommissionInUSD / item.pointRate
}

async function handleCancelAllocation(action: any) {
  Modal.confirm({
    title: '取消业绩分配',
    content: `确认要取消对 [${action.companyUser.realName}] 在 [${action.performancePoint.actionName}] 行为上的业绩分配？`,
    async onOk() {
      action.companyUserId = 0
      action.companyUser = null
      summaryList.value = await getSummaryList(configList.value)
    }
  })
}

const selectedAction = ref<any>(null)
async function handleAddAllocation(action: any) {
  selectedAction.value = action
  status.showStaffSelector = true
}

async function handleSelectStaff(staffs: any[]) {
  const staff = staffs[0]
  selectedAction.value.companyUser = staff
  selectedAction.value.companyUserId = staff.id
  status.showStaffSelector = false
  summaryList.value = await getSummaryList(configList.value)
}

/**
 * 处理数据。
 * 1. 按照角色分组数据，方便快速识别人和动作
 * @param data 
 */
async function processPerformanceData(data: any) {
  const roleMap = new Map()
  data.forEach((item: any) => {
    if (!roleMap.has(item.performancePoint.roleName)) {
      roleMap.set(item.performancePoint.roleName, [])
    }
    roleMap.get(item.performancePoint.roleName).push(item)
  })
  const configList = []
  for (const [key, value] of roleMap) {
    configList.push({ role: key, list: value })
  }
  return configList
}

async function getSummaryList(data: any) {
  const summary: any[] = []
  data.forEach((item: any) => {
    const userMap = new Map()
    item.list.forEach((record: any) => {
      if (!userMap.has(record.companyUser?.id)) {
        userMap.set(record.companyUser?.id, { user: record.companyUser, actions: [], totalRate: 0 })
      }
      userMap.get(record.companyUser?.id).actions.push(record)
    })

    const summaryItem = {
      role: item.role,
      users: [] as any[]
    }

    for (const [key, value] of userMap) {
      summaryItem.users.push(value)
    }

    summaryItem.users.sort((a: any, b: any) => {
      return a.user?.realName.localeCompare(b.user?.realName)
    })

    summary.push(summaryItem)
  })

  return summary
}

function getUserTotalRate(list: any[]) {
  let total = 0
  list.forEach((item: any) => {
    total += item.pointRate
  })
  return total
}

async function saveTaskPerformanceAllocation() {
  status.saving = true
  const form: any[] = []
  configList.value.forEach((role: any) => {
    role.list.forEach((item: any) => {
      form.push({
        id: item.id,
        pointRate: item.pointRate,
        companyUserId: item.companyUserId
      })
    })
  })

  try {
    const res = await saveTaskPerformanceAllocate(processInstanceId.value, form)
    message.success('业绩分配保存成功')
  } catch (err: any) {
    message.error(err.message)
  }
  status.saving = false
}

async function handleConfirmAllocation() {
  if (totalRate.value > 100) {
    message.error('业绩分配比例总和不能超过100%')
    return
  }

  Modal.confirm({
    title: '业绩确认',
    content: '确认业绩分配无误？',
    async onOk() {
      await saveTaskPerformanceAllocation()
    },
    onCancel() {
      // console.log('Cancel')
    }
  })
}

const totalRate = computed(() => {
  let total = 0
  configList.value.forEach((role: any) => {
    role.list.forEach((item: any) => {
      total += item.pointRate
    })
  })
  return total
})

onMounted(() => {
  status.empty = false
  if (processInstanceId.value) {
    getTaskContributeInfo(processInstanceId.value)
  }
})

watch(processInstanceId, (value) => {
  status.empty = false
  if (value) getTaskContributeInfo(value)
})

</script>

<style lang="sass" scoped>
.offer-performance-config
  .config-container
    box-sizing: border-box
    margin: -16px 0

    & > *
      margin: 16px 0

    .title
      font-size: 16px
      font-weight: bold
      margin-bottom: 12px
      color: #333
      position: relative
      padding-left: 12px

      &::before
        content: ""
        display: block
        width: 4px
        height: 16px
        top: 4px
        left: 0
        position: absolute
        border-radius: 2px
        background-color: #FF9111
.performance-summary
  padding: 20px
  background-color: #fff
  border-radius: 8px

.section-performance-list
  background-color: #fff
  padding: 20px
  border-radius: 8px

  .role-group
    border-bottom: 1px solid #f0f0f0
  .config-item, .config-header
    border-bottom: 1px solid #f0f0f0
    padding: 8px 0
    &:last-child
      border-bottom: none

    .duties
      color: #bbb

  .config-header
    font-weight: bold

  .config-footer
    padding: 16px 0
    position: sticky
    bottom: 0
    background-color: #fff

  .add-action
    text-align: right
    margin-top: 8px
  .action
    transition: all 0.3s
    &:hover
      color: #ff9111
      cursor: pointer
      transition: all 0.3s

  .footer-action
    padding-top: 20px
    width: 100%
    text-align: right

.summary-item
  margin: 4px 0

  // :deep(.ant-collapse-header)
  //   padding: 12px 0 

  // :deep(.ant-collapse-content-box)
  //   padding: 12px 0 !important


.summary-total
  display: flex
  padding: 8px 0
  justify-content: flex-end
  color: #ff9111
  font-weight: bold
  .name
    margin-right: 8px

  .rate
    color: #ff9111
</style>