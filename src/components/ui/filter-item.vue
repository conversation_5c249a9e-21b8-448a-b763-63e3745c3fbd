<template lang="pug">
FormItem.filter-item(:label="label")
  slot
</template>

<script lang="ts" setup>
import { FormItem } from 'ant-design-vue'
import { toRef } from 'vue'

const props = defineProps<{ label?: string }>()
const label = toRef(props, 'label')
</script>

<style lang="sass" scoped>
.filter-item
  white-space: nowrap
  margin-bottom: 0
  :deep(.ant-select-selector) 
    background-color: #f9f9f9
    border-color: transparent
  
  :deep(.ant-select-selection-item) 
    color: #FF9111

  :deep(.ant-picker)
    background-color: #f9f9f9 !important
  
  :deep(label) 
    background-color: #f9f9f9
    padding-left: 8px
</style>