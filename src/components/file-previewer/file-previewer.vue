<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2023-02-08 14:47:05
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-03-17 16:43:05
 * @FilePath: /itp-operation-web/src/components/file-previewer/file-previewer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
.file-previewer-component
  a-spin(:spinning="status.loading")
    template(v-if="['text/html'].includes(mime)")
      div(v-html="htmlContent" type="text/html" width="100%" ref="htmlContainer")

    //- template(v-if="['text/plain'].includes(mime)")
    //-   embed(:src="url" type="text/html" width="100%" :height="htmlContainerHeight" :onload="onHtmlLoaded" ref="htmlContainer")

    template(v-else-if="['application/x-pdf', 'application/pdf'].includes(mime)")
      VuePdfEmbed(:source="url" :disableTextLayer="true")

    template(v-else-if="['image/gif', 'image/jpeg', 'image/png'].includes(mime)")
      img(:src="url" style="width:100%;")

    template(v-else-if="['application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(mime)")
      div(v-html="htmlContent" ref="docxContainer")

    template(v-else-if="['application/msword'].includes(mime)")
      iframe(width="100%" height="800px" :src="getDocFile(url)")

    template(v-else)
      p 抱歉，暂不支持该文件的预览
      p 文件类型：{{mime}}
      p 文件URL：{{url}}

  //- template(v-if="['application/rtf', 'application/x-rtf', 'text/rtf'].includes(mime)")
  //-   embed
</template>

<script lang="ts" setup>
import axios from 'axios'
import VuePdfEmbed from 'vue-pdf-embed'
import { toRef, watch, ref, reactive, onMounted, toRefs, nextTick } from 'vue'
import * as docx from 'docx-preview'
import mhtml2html from 'mhtml2html'

const status = reactive({ loading: false })
const props = defineProps<{ url: string, mime: string }>()
const url = toRef(props, 'url')
const mime = toRef(props, 'mime')

const htmlContent = ref<string>('')

const htmlContainer = ref()
const htmlContainerHeight = ref(900)
async function onHtmlLoaded() {
  // htmlContainerHeight.value = htmlContainer.value.document.body.clientHeight
}

const docxContainer = ref()
async function showDocxFile(url: string) {
  const res = await axios.get(url, { responseType: 'blob' })

  await docx.renderAsync(res.data, docxContainer.value, undefined, {
    ignoreWidth: true,
    inWrapper: false
  })
}

const mhtContent = ref()
async function showMhtFile(url: string) {
  const res = await axios.get(url)
  mhtContent.value = mhtml2html.convert(res.data)
}

async function renderFile(url:string, mime: string) {
  if (['application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(mime)) {
    showDocxFile(url)
  } else if (['text/html'].includes(mime)) {
    htmlContent.value = await axios.get(url).then(res => res.data)
    // 注意，这里html可能需要二次处理，因为有些html文件会被包裹在一层引号里
    if (htmlContent.value.startsWith('"')) {
      htmlContent.value = htmlContent.value.slice(1, -1)
    }
  }
}

const getDocFile = (url: string) => {
  return 'https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(url);
}

watch(url, (value) => {
  renderFile(url.value, mime.value!)
})

onMounted(() => {
  renderFile(url.value, mime.value!)
})


</script>

<style lang="scss" scoped>

</style>