<script setup lang="ts">
import { ref, reactive } from 'vue'
import { PlusOutlined, LoadingOutlined, FileOutlined, FolderViewOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  }
})

const showF = ref(false)

const handleClickOpen = () => {
  window.open(props.src)
}

</script>
<template lang="pug">
.ui-image
  a-image(
    v-if="!showF"
    width="100%"
    :src="props.src"
    :preview="false"
    @error="() => showF = true"
  )
  .icon(v-else @click="handleClickOpen")
    FolderViewOutlined

</template>

<style scoped lang="scss">
.ui-image {
  width: 100%;

  .icon {
    font-size: 20px;
    cursor: pointer;
    width: 30px;
  }
}
</style>