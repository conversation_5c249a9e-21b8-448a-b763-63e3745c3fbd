<template lang="pug">
.department-component
  .department-item(v-for="(item, index) in listData" :key="index")
    .department-item__content(:class="{ active: selected?.includes(item.id) }")
      .department-item__title(@click="select(item.id)")
        a-checkbox.department-item__check-box(v-if="mode === 'check'" v-model:checked="item.checked" @change="checkChange")
        ApartmentOutlined.department-item__title-icon(v-else)
        span.title-text {{ item.title }}

      .department-item__action(v-if="mode !== 'check'")
        a-dropdown
          MoreOutlined.department-item__action-icon
          template(#overlay)
            a-menu
              a-menu-item
                span(@click="() => { update(item.id) }") 修改部门

              a-menu-divider

              a-menu-item
                a-popconfirm(
                  :title="`确认要删除部门 [${ item.deptName }] `",
                  ok-text="Yes",
                  cancel-text="No",
                  @confirm="() => { remove(item.id) }"
                )
                  span 删除部门

    .department-item__children
      itp-department(:data="item.children" :selected="selected" :mode="mode" @update="update" @remove="remove" @select="select" @check="check"  )

</template>

<script lang="ts" setup>

/**
 * --------------数据-------------
 * :data
 * 表示需要渲染的数据
 * 
 * :selected
 * 表示当前已经被选中的部门的ID
 * 
 * --------------事件-------------
 * @update
 * 表示点击变更按钮
 * 
 * @remove
 * 表示点击删除按钮
 * 
 * @select
 * 表示点击了某个部门
 * 
 * @check
 * 表示当前checked所有部门
 */

import { ApartmentOutlined, HomeOutlined, MoreOutlined } from '@ant-design/icons-vue'
import { watch, ref, onMounted } from 'vue'

const props = defineProps({ 'data': Object, 'selected': Array<any>, 'mode': String })
const emit = defineEmits(['update', 'remove', 'select', 'check'])
const listData = ref<any>([])
const selected = ref<number[] | undefined>()
const mode = ref<string>()

onMounted(() => {
  listData.value = props.data!.map((item:any, index:number) => {
    return {...item, checked: false}
  })
  if (Array.isArray(props.selected)) {
    check(props.selected)
  }
  selected.value = props.selected
  mode.value = props.mode
})

watch(() => props.data, (value) => {
  if (value) listData.value = value.map((item:any, index:number) => {
    return {...item, checked: false }
  })
})

watch(() => props.selected, (value) => {
  listData.value = listData.value.map((item:any, index:number) => {
    return {...item, checked: props.selected?.includes(item.id)}
  })
  selected.value = value
})


watch(() => props.mode, (value) => {
  mode.value = value
})

function update(departmentId: number) {
  emit('update', departmentId)
}

function remove(departmentId: number) {
  emit('remove', departmentId)
}

function select(departmentId: number) {
  emit('select', departmentId)
}


// 这里需要-------------
// 需要将自己的状态合并到主状态中。
function checkChange() {
  const selectedTemp = selected.value?.concat()

  listData.value.forEach((item:any, index:number) => {
    if(selected.value?.includes(item.id) && !item.checked) {
      selectedTemp?.splice(selectedTemp.indexOf(item.id), 1)
    } else if (!selected.value?.includes(item.id) && item.checked) {
      selectedTemp?.push(item.id)
    }
  })
  emit('check', selectedTemp)
}

function check(departmentIds: number[]) {
  const selectedTemp = departmentIds.concat()
  listData.value.forEach((item:any, index:number) => {
    if(selected.value?.includes(item.id) && !item.checked) {
      selectedTemp?.splice(selectedTemp.indexOf(item.id), 1)
    } else if (!selected.value?.includes(item.id) && item.checked) {
      selectedTemp?.push(item.id)
    }
  })
  emit('check', selectedTemp)
}

</script>

<style lang="scss" scoped>
.department-component {
  .department-item {

    &__content {
      position: relative;
      padding: 8px;
      border-radius: 4px;
      transition: all .2s;

      &:hover {
        transition: all .2s;
        background-color: #f9f9f9;
        cursor: pointer;
      }
    }

    .active {
      background-color: RGBA(255, 145, 17, .1);
      color: RGBA(255, 145, 17, 1);
      transition: all .2s;

      &:hover {
        background-color: RGBA(255, 145, 17, .2);
        transition: all .2s;
      }
    }

    &__title {
      width: 100%;
      flex: 1 1 auto;
      padding: 0 24px 0 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .title-text {
        padding-left: 12px;
      }
    }

    &__title-icon {
      position: absolute;
      left: 12px;
      top: 12px;
    }

    &__check-box {
      position: absolute;
      left: 12px;
      top: 8px;
    }

    &__action-icon {
      position: absolute;
      right: 12px;
      top: 12px;
    }

    &__action {
      width: 36px;
    }

    &__children {
      padding-left: 24px;
    }
  }
}
</style>
