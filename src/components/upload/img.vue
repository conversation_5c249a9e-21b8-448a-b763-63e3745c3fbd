<script setup lang="ts">
import { ref, reactive } from 'vue'
import { API_URL } from '@/api/customer'
import { useUserStore } from '@/store/user.store'
import { PlusOutlined, LoadingOutlined, FileOutlined, FolderViewOutlined } from '@ant-design/icons-vue'

const userStore = useUserStore()

const emit = defineEmits(['update:value'])
const props = defineProps({
  value: {
    type: String,
    default: ''
  }
})

const status = reactive({
  upLoading: false
})

const info = reactive({
  extName: '',
  fileUrl: '',
})

const handleUpload = (val: any) => {
  if (val.file.status === 'uploading') {
    status.upLoading = true
  } else if (val.file.status === 'done') {
    const fileResponse: any = val.file.response.data
    status.upLoading = false
    info.extName = fileResponse.extName
    info.fileUrl = fileResponse.fileAbsolutePath

    emit('update:value', fileResponse.id)
  } else {
    status.upLoading = false
  }
}

const handleClickView = () => {
  window.open(info.fileUrl)
}

const img = ['png', 'jpg', 'jpeg']

</script>
<template lang="pug">
a-upload.logo_uploader(
  accept="png,jpg,jpeg,pdf",
  :show-upload-list="false",
  :action="API_URL.UPLOAD_FILE",
  :headers="{'Authorization': userStore.token}",
  @change="handleUpload"
)
  .upload-area()
    template(v-if="info.fileUrl")
      a-avatar.avatar(
        v-if="img.includes(info.extName)"
        :src="info.fileUrl"
        alt="avatar"
        shape="square"
        :size="100"
      )
      template(v-else)
        FileOutlined
        .view(@click.stop="handleClickView")
          FolderViewOutlined
    
    template(v-else)
      LoadingOutlined(v-if="status.upLoading")
      PlusOutlined(v-else)
</template>

<style scoped lang="scss">
.upload-area {
  width: 100px;
  height: 100px;
  border: 1px dotted #999;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.view {
  width: 25px;
  height: 25px;
  position: absolute;
  right: 0;
  top: 0;
  background: rgb(0 0 0 / 30%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e8e6e6;
}
</style>