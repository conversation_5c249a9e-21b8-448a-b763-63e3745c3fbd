<script setup lang="ts">
import { ref, reactive } from 'vue'
import { API_URL } from '@/api/customer'
import { useUserStore } from '@/store/user.store'
import { UploadOutlined } from '@ant-design/icons-vue'

const userStore = useUserStore()

const emit = defineEmits(['update:value'])
const props = defineProps({
  value: {
    type: String,
    default: ''
  }
})

const status = reactive({
  upLoading: false
})

const info = reactive({
  extName: '',
  fileUrl: '',
})

const handleUpload = (val: any) => {
  if (val.file.status === 'uploading') {
    status.upLoading = true
  } else if (val.file.status === 'done') {
    const fileResponse: any = val.file.response.data
    status.upLoading = false
    info.extName = fileResponse.extName
    info.fileUrl = fileResponse.fileAbsolutePath

    emit('update:value', fileResponse.id)
  } else {
    status.upLoading = false
  }
}

const handleClickView = () => {
  window.open(info.fileUrl)
}

</script>
<template lang="pug">
.upload-file
  a-upload(
    name="file"
    :multiple="false"
    :show-upload-list="false",
    :max-count="1"
    :action="API_URL.UPLOAD_FILE"
    :headers="{'Authorization': userStore.token}",
    @change="handleUpload"
    :disabled="status.upLoading"
  )
    a-button(:loading="status.upLoading" style="width: 100%")
      template(#icon)
        UploadOutlined
      span 点击上传
  a.file-name(@click="handleClickView") {{ info.fileUrl }}
</template>

<style scoped lang="scss">
.upload-file {
  display: flex;
  align-items: center;
}

.file-name {
  margin-left: 15px;
  flex: 1;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap;  // 默认不换行；
  // cursor: pointer;
}
</style>