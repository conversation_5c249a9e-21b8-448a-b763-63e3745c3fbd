<template lang="pug">
.multi-file-upload
  .upload-area
    a-upload.upload-dragger(
      name="file"
      :multiple="true"
      :show-upload-list="false"
      :action="API_URL.UPLOAD_FILE"
      :headers="{'Authorization': userStore.token}"
      @change="handleUpload"
      :disabled="status.uploading"
      :before-upload="beforeUpload"
    )
      .upload-content
        .upload-icon
          InboxOutlined(v-if="!status.uploading")
          LoadingOutlined(v-else)
        .upload-text
          p.upload-hint(v-if="!status.uploading") 点击或拖拽文件到此区域上传
          p.upload-hint(v-else) 正在上传文件...
          p.upload-sub-hint 支持单个或批量上传

  .file-list(v-if="fileList.length > 0")
    .file-list-header
      h4 已上传文件 ({{ fileList.length }})
    .file-items
      .file-item(v-for="(file, index) in fileList" :key="file.id")
        .file-info
          .file-icon
            FileOutlined
          .file-details
            .file-name {{ file.originName }}
            //- .file-meta
            //-   span.file-size {{ formatFileSize(file.fileSize) }}
            //-   span.file-type {{ file.extName.toUpperCase() }}
        .file-actions
          a-button(
            type="link"
            size="small"
            @click="downloadFile(file)"
            :loading="file.downloading"
          ) 下载
          a-button(
            type="link"
            size="small"
            danger
            @click="removeFile(index)"
          ) 删除
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { API_URL } from '@/api/customer'
import { useUserStore } from '@/store/user.store'
import { InboxOutlined, LoadingOutlined, FileOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const userStore = useUserStore()

interface FileItem {
  id: string
  fileType: number
  originName: string
  extName: string
  mimeType: string
  fileSize: number
  fileAbsolutePath: string
  downloading?: boolean
}

const emit = defineEmits(['update:value', 'change'])
const props = defineProps({
  value: {
    type: Array as () => string[],
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 10
  },
  maxSize: {
    type: Number,
    default: 50 * 1024 * 1024 // 50MB
  },
  accept: {
    type: String,
    default: ''
  }
})

const status = reactive({
  uploading: false
})

const fileList = ref<FileItem[]>([])

// 监听props.value变化，用于回显已有文件
watch(() => props.value, (newValue) => {
  if (newValue && newValue.length > 0 && fileList.value.length === 0) {
    // 这里需要根据fileIds获取文件信息，暂时先处理空数组的情况
    // 在实际使用时，父组件应该传入完整的文件信息
  }
}, { immediate: true })

const beforeUpload = (file: File) => {
  // 检查文件数量限制
  if (fileList.value.length >= props.maxCount) {
    message.error(`最多只能上传 ${props.maxCount} 个文件`)
    return false
  }

  // 检查文件大小
  if (file.size > props.maxSize) {
    message.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
    return false
  }

  // 检查文件类型
  if (props.accept) {
    const acceptTypes = props.accept.split(',').map(type => type.trim().toLowerCase())
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    if (fileExt && !acceptTypes.includes(fileExt)) {
      message.error(`只支持上传 ${props.accept} 格式的文件`)
      return false
    }
  }

  return true
}

const handleUpload = (info: any) => {
  if (info.file.status === 'uploading') {
    status.uploading = true
  } else if (info.file.status === 'done') {
    status.uploading = false
    const response = info.file.response
    
    if (response.code === 0) {
      const fileData: FileItem = response.data
      fileList.value.push(fileData)
      
      // 更新文件ID列表
      const fileIds = fileList.value.map(file => file.id)
      emit('update:value', fileIds)
      emit('change', fileList.value)
      
      message.success(`${fileData.originName} 上传成功`)
    } else {
      message.error(response.message || '文件上传失败')
    }
  } else if (info.file.status === 'error') {
    status.uploading = false
    message.error(`${info.file.name} 上传失败`)
  }
}

const removeFile = (index: number) => {
  const removedFile = fileList.value[index]
  fileList.value.splice(index, 1)
  
  // 更新文件ID列表
  const fileIds = fileList.value.map(file => file.id)
  emit('update:value', fileIds)
  emit('change', fileList.value)
  
  message.success(`${removedFile.originName} 已删除`)
}

const downloadFile = (file: FileItem) => {
  if (file.fileAbsolutePath) {
    file.downloading = true
    // 创建一个临时的a标签来下载文件
    const link = document.createElement('a')
    link.href = file.fileAbsolutePath
    link.download = file.originName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    setTimeout(() => {
      file.downloading = false
    }, 1000)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  setFileList: (files: FileItem[]) => {
    fileList.value = files
  },
  getFileList: () => fileList.value,
  clearFiles: () => {
    fileList.value = []
    emit('update:value', [])
    emit('change', [])
  }
})
</script>

<style scoped lang="scss">
.multi-file-upload {
  .upload-area {
    margin-bottom: 16px;
    
    .upload-dragger {
      :deep(.ant-upload-drag) {
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s;
        
        &:hover {
          border-color: #1890ff;
          background: #f0f8ff;
        }
      }
    }
    
    .upload-content {
      padding: 32px 16px;
      text-align: center;
      
      .upload-icon {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }
      
      .upload-hint {
        font-size: 16px;
        color: #666;
        margin: 0 0 8px 0;
      }
      
      .upload-sub-hint {
        font-size: 14px;
        color: #999;
        margin: 0;
      }
    }
  }
  
  .file-list {
    .file-list-header {
      margin-bottom: 12px;
      
      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .file-items {
      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        background: #fafafa;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .file-info {
          display: flex;
          align-items: center;
          flex: 1;
          
          .file-icon {
            font-size: 20px;
            color: #1890ff;
            margin-right: 12px;
          }
          
          .file-details {
            .file-name {
              font-size: 14px;
              color: #333;
              margin-bottom: 4px;
              word-break: break-all;
            }
            
            .file-meta {
              font-size: 12px;
              color: #999;
              
              span {
                margin-right: 12px;
              }
            }
          }
        }
        
        .file-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
}
</style>
