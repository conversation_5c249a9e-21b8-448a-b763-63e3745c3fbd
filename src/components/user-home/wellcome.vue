<template lang="pug">
.wellcome
  span  hi {{ userStore.name }}, {{ welcomeText }}
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user.store'
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';

const userStore = useUserStore()
const welcomeText = ref('')

function getWelcomeText() {
  const now = dayjs()
  const hour = now.hour()
  if (hour >= 0 && hour < 6) {
    return '凌晨好, 哎呦？你也是个夜猫子啊！'
  } else if (hour >= 6 && hour < 12) {
    return '上午好!'
  } else if (hour >= 12 && hour < 14) {
    return '中午好!'
  } else if (hour >= 14 && hour < 19) {
    return '下午好呀。'
  } else if (hour >= 19 && hour < 24) {
    return '晚上好！'
  } else {
    return '你好'
  }
}

onMounted(()=>{
  welcomeText.value = getWelcomeText()
})
</script>

<style lang="sass" scoped>
.wellcome
  padding: 16px
  border-radius: 8px
  background-color: #fff
</style>