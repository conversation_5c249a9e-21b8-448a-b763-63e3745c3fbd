<template lang="pug">
.preformance-statistic

  a-row.header(:gutter="8")
    a-col(:span="24")
      h3.title {{ getTitle() }}项目统计
  a-spin(:spinning="status.loading")
    a-row.statistics(:gutter="[8, 24]")
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('onGoing')"
          symbol=""
          title="进展中项目数"
          @click="emit('numberClick', '')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('p0')"
          symbol=""
          title="P0高优项目数"
          @click="emit('numberClick', '?priority=10')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('p1')"
          symbol=""
          title="P1常规项目数"
          @click="emit('numberClick', '?priority=5')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('abroadP0')"
          symbol=""
          title="海外P0项目数"
          @click="emit('numberClick', '?priority=10&baseAreaType=2')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('remoteP0')"
          symbol=""
          title="远程P0项目数"
          @click="emit('numberClick', '?priority=10&remoteWork=1')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('nativeP0')"
          symbol=""
          title="国内P0项目数"
          @click="emit('numberClick', '?priority=10&baseAreaType=1')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('noRecommend30Days')"
          symbol=""
          title="近30天无推荐高优"
          @click="emit('numberClick', '?priority=10&toPM30Day=0')"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('noInterview30Days')"
          symbol=""
          title="近30天无面试高优"
          @click="emit('numberClick', '?priority=10&interviewed30Day=0')"
        )

</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import  StatisticItem from '@/components/user-home/pipeline-statistic-item.vue'
import { getJobRequirementStats } from '@/api/stat'
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/user.store'
import router from '@/router'

const userStore = useUserStore();
const companyId = userStore.companyId
const isCompanyAdmin = userStore.isAdmin
const isPlatformAdmin = userStore.userType === 2

const emit = defineEmits(['numberClick'])

const status = reactive({
  loading: false,
})

const statsParams = reactive({
  onGoing: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      }
    ]
  },
  p0: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      }
    ]
  },
  p1: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '5',
        action: 'eq'
      }
    ]
  },
  abroadP0: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      },
      {
        key: 'position.workAbroad',
        value: "2",
        action: 'eq'
      }
    ]
  },
  remoteP0: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      },
      {
        key: 'position.remoteWork',
        value: '1',
        action: 'eq'
      }
    ]
  },
  nativeP0: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      },
      {
        key: 'position.workAbroad',
        value: '1',
        action: 'eq'
      }
    ]
  },
  noRecommend30Days: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      },
      {
        key: 'invertedIndexSearchMap.invertedIndex_toPM30Day',
        value: '0',
        action: 'eq'
      }
    ]
  },
  noInterview30Days: {
    queries: [
      {
        key: 'status',
        value: '1',
        action: 'eq'
      },
      {
        key: 'priority',
        value: '10',
        action: 'eq'
      },
      {
        key: 'invertedIndexSearchMap.invertedIndex_interviewed30Day',
        value: '0',
        action: 'eq'
      }
    ]
  }
})
const jobRequirementStatistic = ref<any>([])

async function getJobRequirementStatistic() {
  status.loading = true
  try {
    const res = await getJobRequirementStats(companyId, statsParams)
    jobRequirementStatistic.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

function getResultCount(param: string) {
  for (let i in jobRequirementStatistic.value) {
    if (i === param) {
      return jobRequirementStatistic.value[i]
    }
  }
  return 0
}

function getTitle () {
  if (isPlatformAdmin) {
    return '平台'
  } else if (isCompanyAdmin) {
    return '公司'
  } else {
    return '我的'
  }
}

onMounted(()=>{
  getJobRequirementStatistic()
})

</script>

<style lang="sass" scoped>
.preformance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px
  margin-bottom: 20px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)

  .time-range
    margin-bottom: 24px

  .statistics
    padding: 16px 0
    cursor: pointer


</style>