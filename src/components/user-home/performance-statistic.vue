<template lang="pug">

.preformance-statistic

  a-row.header(:gutter="8")
    a-col(:span="8")
      h3.title 业绩统计
    a-col(:span="16" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="timeRange" button-style="solid")
          a-radio-button(value="day" @click="handleTimeChange('day')") 今天
          a-radio-button(value="week" @click="handleTimeChange('week')") 本周
          a-radio-button(value="month" @click="handleTimeChange('month')") 本月
          a-radio-button(value="year" @click="handleTimeChange('year')") 本年度
    
        a-button(@click="handlePreformanceDetailClick") 详情

  a-spin(:spinning="status.loading")
    a-row.statistics(:gutter="[8, 24]")
      a-col(:span="6")
        StatisticItem(
          :value="performanceStastic.offerPerformance" 
          title="Offer业绩" 
          :compareValue="performanceStastic.offerPerformanceLastPeriod" 
          :compareTitle="performanceStastic.lastPeriodName" 
          @click="emit('numberClick')"
        )
      a-col(:span="6")
        StatisticItem(
          :value="performanceStastic.onboardPerformance" 
          title="入职业绩" 
          :compareValue="performanceStastic.onboardPerformanceLastPeriod" 
          :compareTitle="performanceStastic.lastPeriodName"
          @click="emit('numberClick')"
        )
      a-col(:span="6")
        StatisticItem(
          :value="performanceStastic.invoicePerformance" 
          title="开票业绩" 
          :compareValue="performanceStastic.invoicePerformanceLastPeriod" 
          :compareTitle="performanceStastic.lastPeriodName"
          @click="emit('numberClick')"
        )
      a-col(:span="6")
        StatisticItem(
          :value="performanceStastic.paidPerformance" 
          title="回款业绩" 
          :compareValue="performanceStastic.paidPerformanceLastPeriod" 
          :compareTitle="performanceStastic.lastPeriodName"
          @click="emit('numberClick')"
        )

</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import  StatisticItem from '@/components/user-home/performance-statistic-item.vue'
import { getStaffPerformanceStatistics } from '@/api/performance'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import router from '@/router';

type TimeRangeStr = 'week' | 'year' | 'month' | 'day'

const emit = defineEmits(['numberClick'])

const status = reactive({
  loading: false,
})

const performanceStastic = ref<any>({
  offerPerformance: 0,
  offerPerformanceLastPeriod: 0,
  onboardPerformance: 0,
  onboardPerformanceLastPeriod: 0,
  invoicePerformance: 0,
  invoicePerformanceLastPeriod: 0,
  paidPerformance: 0,
  paidPerformanceLastPeriod: 0
})

async function getStaffPerformanceStastic(start: string, end: string) {
  status.loading = true
  try {
    const res = await getStaffPerformanceStatistics(start, end)
    performanceStastic.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const timeRange = ref<TimeRangeStr>('month')

function getTimeRange(timeRangeKey: TimeRangeStr) {
  if (['week', 'month', 'year', 'day'].includes(timeRangeKey)) {
    const now = dayjs()
    const type = timeRangeKey as any
    const start = dayjs().startOf(type).format('YYYY-MM-DD')
    const end = dayjs().endOf(type).format('YYYY-MM-DD')
    return { start, end }
  }

  return {
    start: '',
    end: ''
  }
}

const filter = reactive({
  start: '',
  end: '',
  type: 1
})

function handlePreformanceDetailClick() {
  router.push(`/performance/me?timerange=${timeRange.value}`)
}

async function handleTimeChange(timeRangeKey: any) {
  const { start, end } = getTimeRange(timeRangeKey)
  filter.start = start
  filter.end = end
  getStaffPerformanceStastic(start, end)
}

onMounted(()=>{
  const { start, end } = getTimeRange(timeRange.value)
  filter.start = start
  filter.end = end

  // 1 表示offer业绩
  filter.type = 1
  getStaffPerformanceStastic(start, end)
})

</script>

<style lang="sass" scoped>
.preformance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)

  .time-range
    margin-bottom: 24px
    
  .statistics
    padding: 16px 0
    cursor: pointer
  

</style>