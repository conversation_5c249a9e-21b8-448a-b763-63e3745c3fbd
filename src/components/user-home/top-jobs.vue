<template lang="pug">

mixin job-item
  .adviser
    .avatar
      a-avatar(:size="32") {{ item.user.name }}
    .advise-text 
      .name {{ item.user.name }}
      .text {{ item.text }}
  .job-info-wrapper(@click="() => handleJobInfoClick(item)")
    .prioriy
      JobPriority(:priority="item.job.priority")
    .job-summary
      .job-title {{ item.job.jobTitle }}
      .job-infos {{ item.job.companyName }} · {{ item.job.salary }} · {{ item.job.city }}

.top-jobs-comp
  .section-head
    .title
      h3 重点项目
    .actions
      a-button(shape="circle" @click="status.showList = true")
        template(#icon)
          UnorderedListOutlined
  a-carousel(dot-position="right" autoplay :dots="false")
    .carousel-item(v-for="item in JOB_CONFIG" :key="item.id")
      .top-job-item()
        +job-item


  a-drawer(v-model:open="status.showList" title="重点项目")
    .top-job-item.with-spliter(v-for="item in JOB_CONFIG" :key="item.id")
      +job-item
      a-divider
  
</template>

<script lang="ts" setup>
import { UnorderedListOutlined } from '@ant-design/icons-vue'
import { reactive } from 'vue'
import JobPriority from '@/components/app/job-priority.vue'
import router from '@/router';

const JOB_CONFIG = [
  {
    id: 1,
    user: {
      name: 'Marian'
    },
    text: '项目方着急，且佣金高！项目方着急，且佣金高！项目速度很快，这里填充是为了能够出现两行数据，请不要在意这些文字，这里是推荐该职位的理由。',
    job: {
      jobTitle: 'Java开发工程师',
      companyName: '北京百度科技有限公司',
      salary: '20-30k',
      city: '北京',
      priority: 10
    }
  },
  {
    id: 2,
    user: {
      name: 'ada'
    },
    text: '项目方着急，且佣金高！',
    job: {
      jobTitle: 'Java开发工程师',
      companyName: '北京百度科技有限公司',
      salary: '20-30k',
      city: '北京',
      priority: 10
    }
  },
]

const status = reactive({
  showList: false
})

function handleJobInfoClick(item:any) {
  router.push('/job/670/detail')
}

</script>

<style lang="sass" scoped>
.top-jobs-comp
  border-radius: 8px
  background-color: #fff
  padding: 16px
  width: 100%

  .section-head
    display: flex
    justify-content: space-between
    align-items: center
    margin-bottom: 12px

    .title
      h3
        padding-left: 12px
        position: relative
        &::before
          position: absolute
          content: ''
          display: block
          width: 4px
          height: 16px
          border-radius: 2px
          background-color: #ff9111
          left: 0
          top: 50%
          transform: translateY(-50%)

.carousel-item
  height: 156px

.top-job-item
  .adviser
    margin-bottom: 12px
    padding-left: 40px
    position: relative

    .avatar
      margin-right: 12px
      position: absolute
      left: 0
      top: 0

    .advise-text
      .name
        line-height: 1
        margin-bottom: 4px
      .text
        margin: 0
        padding: 8px
        font-size: 14px
        color: #666
        background-color: #eee
        border-radius: 6px
        overflow: hidden
        text-overflow: ellipsis
        white-space: nowrap
        -webkit-box-orient: vertical
        -webkit-line-clamp: 2

  .job-info-wrapper
    padding: 16px
    border: 1px solid #eee
    border-radius: 8px
    display: flex
    align-items: center
    transition: all .2s
    &:hover
      cursor: pointer
      background-color: RGBA(0,0,0,0.012)
      transition: all .2s


    .job-summary
      .job-infos
        color: #999


</style>