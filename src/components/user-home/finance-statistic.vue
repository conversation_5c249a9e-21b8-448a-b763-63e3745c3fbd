<template lang="pug">
.performance-statistic
  a-row.header(:gutter="8")
    a-col(:span="12")
      h3.title {{ getTitle() }}业绩对比
    a-col(:span="9" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="abroad" button-style="solid" @change="handleAbroad")
          a-radio-button(value="abroad") 海外 + 远程
          a-radio-button(value="native") 国内
    a-col(:span="3" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="employeeType" button-style="solid" @change="handleEmployeeType")
          a-radio-button(value="full_time") 内部
          a-radio-button(value="part_time") RCN
  .performance-detail
    .chart-panel.offer-count(ref="chartOfferCount" style="width: 45%; height:200px")
    .chart-panel.offer-amount(ref="chartOfferAmount" style="width: 45%; height:200px")
    .chart-panel.invoice(ref="chartInvoiceAmount" style="width: 45%; height:200px")
    .chart-panel.statement(ref="chartStatementAmount" style="width: 45%; height:200px")

</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import dayjs from 'dayjs';
import { useUserStore } from '@/store/user.store'
import * as echarts from 'echarts'

const userStore = useUserStore();
const companyId = userStore.companyId
const isCompanyAdmin = userStore.isAdmin
const isPlatformAdmin = userStore.userType === 2

type TimeRangeStr = 'week' | 'year' | 'month' | 'day'

const emit = defineEmits(['numberClick'])

const status = reactive({
  loading: false,
})

const abroad = ref<string>('')
const employeeType = ref<string>('')
const fromDate = ref<string>()
const toDate = ref<string>()
const timeRange = ref<TimeRangeStr>('month')
const chartOfferCount = ref<HTMLDivElement | null>(null)
const chartOfferAmount = ref<HTMLDivElement | null>(null)
const chartInvoiceAmount = ref<HTMLDivElement | null>(null)
const chartStatementAmount = ref<HTMLDivElement | null>(null)


function getTimeRange(timeRangeKey: TimeRangeStr) {
  if (['week', 'month', 'year', 'day', 'last_month'].includes(timeRangeKey)) {
    const now = dayjs()
    const type = timeRangeKey as any
    let start = dayjs().startOf(type).format('YYYY-MM-DD')
    let end = dayjs().endOf(type).format('YYYY-MM-DD')
    if (type === 'last_month') {
      start = dayjs().startOf(type).subtract(1, 'month').format('YYYY-MM-DD')
      end = dayjs().endOf(type).subtract(1, 'month').format('YYYY-MM-DD')
    }
    fromDate.value = start
    toDate.value = end
  }
}

function handleAbroad() {
  employeeType.value = ""
}

function handleEmployeeType() {
  abroad.value = ""
}

async function handleTimeChange(timeRangeKey: any) {
  getTimeRange(timeRangeKey)
}

function getTitle () {
  if (isPlatformAdmin) {
    return '平台'
  } else if (isCompanyAdmin) {
    return '公司'
  } else {
    return '我的'
  }
}

async function initOfferCountChart() {
  const myChart = echarts.init(chartOfferCount.value);
  const option = {
    tooltip: {},
    title: {
      text: 'Offer 金额月同比',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 14,
        opacity: 0.7,
        color: '#000'
      }
    },
    xAxis: {
      data: ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']
    },
    yAxis: {},
    grid: {
      left: 60,
      top: 10,
      bottom: 20,
      right: 0
    },
    series: [
      {
        type: 'bar',
        data: [304328.18, 411201.78, 390065.06, 452362.19, 256254.02, 445792.72, 439222.79, 572081.53, 564392.37, 782172.47, 693403.37, 276861.98]
      }
    ]
  };
  myChart.setOption(option);
}

async function initOfferAmountChart() {
  const myChart = echarts.init(chartOfferAmount.value);
  const option = {
    tooltip: {},
    title: {
      text: 'Offer 金额月同比',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 14,
        opacity: 0.7,
        color: '#000'
      }
    },
    xAxis: {
      data: ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']
    },
    yAxis: {},
    grid: {
      left: 60,
      top: 10,
      bottom: 20,
      right: 0
    },
    series: [
      {
        type: 'bar',
        data: [304328.18, 411201.78, 390065.06, 452362.19, 256254.02, 445792.72, 439222.79, 572081.53, 564392.37, 782172.47, 693403.37, 276861.98]
      }
    ]
  };
  myChart.setOption(option);
}

async function initInvoiceChart() {
  const myChart = echarts.init(chartInvoiceAmount.value);
  const option = {
    tooltip: {},
    title: {
      text: 'Offer 金额月同比',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 14,
        opacity: 0.7,
        color: '#000'
      }
    },
    xAxis: {
      data: ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']
    },
    yAxis: {},
    grid: {
      left: 60,
      top: 10,
      bottom: 20,
      right: 0
    },
    series: [
      {
        type: 'bar',
        data: [304328.18, 411201.78, 390065.06, 452362.19, 256254.02, 445792.72, 439222.79, 572081.53, 564392.37, 782172.47, 693403.37, 276861.98]
      }
    ]
  };
  myChart.setOption(option);
}

async function initStatementChart() {
  const myChart = echarts.init(chartStatementAmount.value);
  const option = {
    tooltip: {},
    title: {
      text: 'Offer 金额月同比',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 14,
        opacity: 0.7,
        color: '#000'
      }
    },
    xAxis: {
      data: ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']
    },
    yAxis: {},
    grid: {
      left: 60,
      top: 10,
      bottom: 20,
      right: 0
    },
    series: [
      {
        type: 'bar',
        data: [304328.18, 411201.78, 390065.06, 452362.19, 256254.02, 445792.72, 439222.79, 572081.53, 564392.37, 782172.47, 693403.37, 276861.98]
      }
    ]
  };
  myChart.setOption(option);
}

onMounted(async ()=>{
  getTimeRange(timeRange.value)
  await initOfferCountChart()
  await initOfferAmountChart()
  await initInvoiceChart()
  await initStatementChart()
})

</script>

<style lang="sass" scoped>
.performance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px
  margin-bottom: 20px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)

  .time-range
    margin-bottom: 24px

  .statistics
    padding: 16px 0
    cursor: pointer

.performance-detail
  display: flex
  flex-wrap: wrap

  .chart-panel
    margin-bottom: 50px
</style>