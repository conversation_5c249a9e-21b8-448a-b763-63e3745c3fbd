<template lang="pug">
.preformance-statistic

  a-row.header(:gutter="8")
    a-col(:span="8")
      h3.title {{ getTitle() }}项目统计（推荐人）
    a-col(:span="9" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="abroad" button-style="solid" @change="handleAbroad")
          a-radio-button(value="abroad") 海外
          a-radio-button(value="native") 国内
    a-col(:span="3" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="employeeType" button-style="solid" @change="handleEmployeeType")
          a-radio-button(value="full_time") 内部
          a-radio-button(value="part_time") RCN
    a-col(:span="4" align="right")
      a-space(:size="8")
        a-radio-group(v-model:value="timeRange" button-style="solid")
          a-radio-button(value="week" @click="handleTimeChange('week')") 本周
          a-radio-button(value="month" @click="handleTimeChange('month')") 本月
          a-radio-button(value="year" @click="handleTimeChange('year')") 本年

  a-spin(:spinning="status.loading")
    a-row.statistics(:gutter="[8, 24]")
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('position_pm_talent_get')"
          symbol=""
          title="推荐人才"
          @click="emit('numberClick', 'position_pm_talent_get', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('position_pm_talent_get_to_customer')"
          symbol=""
          title="推荐给客户"
          @click="emit('numberClick', 'position_pm_talent_get_to_customer', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('position_pm_talent_interview')"
          symbol=""
          title="进入面试"
          @click="emit('numberClick', 'position_pm_talent_interview', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="3")
        StatisticItem(
          :value="getResultCount('position_interview_task')"
          symbol=""
          title="面试场次"
          @click="emit('numberClick', 'position_interview_task', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="2")
        StatisticItem(
          :value="getResultCount('position_pm_talent_to_inspect')"
          symbol=""
          title="Offer"
          @click="emit('numberClick', 'position_pm_talent_to_inspect', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="4")
        StatisticItem(
          :value="getResultValue('position_pm_talent_to_inspect')"
          symbol="$"
          title="Offer 金额 (美金）"
          @click="emit('numberClick', 'position_pm_talent_to_inspect', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="2")
        StatisticItem(
          :value="getResultCount('position_pm_talent_in_keep')"
          symbol=""
          title="入职"
          @click="emit('numberClick', 'position_pm_talent_in_keep', abroad, employeeType, fromDate, toDate)"
        )
      a-col(:span="4")
        StatisticItem(
          :value="getResultValue('position_pm_talent_in_keep')"
          symbol="$"
          title="入职金额（美金）"
          @click="emit('numberClick', 'position_pm_talent_in_keep', abroad, employeeType, fromDate, toDate)"
        )

</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import  StatisticItem from '@/components/user-home/pipeline-statistic-item.vue'
import { getPipelineStats } from '@/api/stat'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import router from '@/router';
import { useUserStore } from '@/store/user.store'

const userStore = useUserStore();
const companyId = userStore.companyId
const isCompanyAdmin = userStore.isAdmin
const isPlatformAdmin = userStore.userType === 2

type TimeRangeStr = 'week' | 'year' | 'month' | 'day'

const emit = defineEmits(['numberClick'])

const status = reactive({
  loading: false,
})

const pipelineStatistic = ref<any>([])

async function getPipelineStatistic() {
  status.loading = true
  try {
    const res = await getPipelineStats({
      fromDate: fromDate.value,
      toDate: toDate.value,
      abroad: abroad.value,
      employeeType: employeeType.value
    }, companyId)
    pipelineStatistic.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const abroad = ref<string>('')
const employeeType = ref<string>('')
const fromDate = ref<string>()
const toDate = ref<string>()
const timeRange = ref<TimeRangeStr>('month')


function getTimeRange(timeRangeKey: TimeRangeStr) {
  if (['week', 'month', 'year', 'day', 'last_month'].includes(timeRangeKey)) {
    const now = dayjs()
    const type = timeRangeKey as any
    let start = dayjs().startOf(type).format('YYYY-MM-DD')
    let end = dayjs().endOf(type).format('YYYY-MM-DD')
    if (type === 'last_month') {
      start = dayjs().startOf(type).subtract(1, 'month').format('YYYY-MM-DD')
      end = dayjs().endOf(type).subtract(1, 'month').format('YYYY-MM-DD')
    }
    fromDate.value = start
    toDate.value = end
  }
}

function handleAbroad() {
  employeeType.value = ""
  getPipelineStatistic()
}

function handleEmployeeType() {
  abroad.value = ""
  getPipelineStatistic()
}

async function handleTimeChange(timeRangeKey: any) {
  getTimeRange(timeRangeKey)
  await getPipelineStatistic()
}

function getResultCount(param: string) {
  for (let i = 0; i < pipelineStatistic.value.length; i++) {
    if (pipelineStatistic.value[i].taskDefinitionKey === param) {
      return pipelineStatistic.value[i].statsCount
    }
  }
  return 0
}

function getResultValue(param: string) {
  for (let i = 0; i < pipelineStatistic.value.length; i++) {
    if (pipelineStatistic.value[i].taskDefinitionKey === param) {
      return pipelineStatistic.value[i].valueInUSD
    }
  }
  return 0
}

function getTitle () {
  if (isPlatformAdmin) {
    return '平台'
  } else if (isCompanyAdmin) {
    return '公司'
  } else {
    return '我的'
  }
}

onMounted(()=>{
  getTimeRange(timeRange.value)
  getPipelineStatistic()
})

</script>

<style lang="sass" scoped>
.preformance-statistic
  background-color: #fff
  border-radius: 8px
  padding: 20px
  margin-bottom: 20px

  .header
    margin-bottom: 16px

    h3.title
      padding-left: 12px
      position: relative
      &::before
        position: absolute
        content: ''
        display: block
        width: 4px
        height: 16px
        border-radius: 2px
        background-color: #ff9111
        left: 0
        top: 50%
        transform: translateY(-50%)

  .time-range
    margin-bottom: 24px

  .statistics
    padding: 16px 0
    cursor: pointer


</style>