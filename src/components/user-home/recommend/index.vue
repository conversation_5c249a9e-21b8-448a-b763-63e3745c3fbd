<template>
  <div class="preformance-statistic bg-white mb-4" v-if="!isClosed">
    <div class="p-4">
      <div class="ant-row flex justify-between header">
        <div class="w-full sm:w-1/3">
          <h3 class="title">小推每日推荐</h3>
        </div>
        <a-button type="text" class="close-btn" @click="closeList">关闭小推</a-button>
      </div>
      <div class="px-4">
        <!-- Tabs -->
        <Tabs 
          :jobTabs="jobTabs" 
          @change="handleTabChange"
        />
        <!-- Cards -->
        <List
          :loading="loading"
          :jobRequirements="jobRequirements"
          :total="total"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :degreeMap="degreeMap"
          @pageChange="handlePageChange"
          @sizeChange="handleSizeChange"
          @removeItem="handleRemoveItem"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { getRecommendationsTabs, getRecommendationsList } from '@/api/notice'
import List from './list.vue'
import Tabs from './tabs.vue'

const isClosed = ref(localStorage.getItem('closeXiaotui') ? true : false)
const degreeMap = reactive({
  '1': '高中',
  '2': '大专',
  '3': '本科',
  '4': '硕士',
  '5': '博士'
})

const closeList = () => {
  localStorage.setItem('closeXiaotui', String(new Date().setHours(23,59,59,999)))
  isClosed.value = true
}

interface JobRequirement {
  id: number
  processName: string
  recommendTalentCount: number
  customerName: string
}

const loading = ref(false)
const jobTabs = ref<JobRequirement[]>([])
const jobRequirements = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const activeJobId = ref<number>()

const handleTabChange = async (jobId: number) => {
  activeJobId.value = jobId
  currentPage.value = 1
  await fetchJobRequirements()
}

const handlePageChange = async (page: number) => {
  currentPage.value = page
  await fetchJobRequirements()
}

const handleSizeChange = async (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  await fetchJobRequirements()
}

const handleRemoveItem = (parentId: number) => {
  jobRequirements.value = jobRequirements.value.filter((item: any) => item.parentId !== parentId)
}

const fetchJobRequirements = async () => {
  if (!activeJobId.value) return
  
  loading.value = true
  try {
    const res = await getRecommendationsList(
      activeJobId.value,
      currentPage.value,
      pageSize.value
    )
    jobRequirements.value = res.data.records.map((record: any) => {
      const talent = JSON.parse(record.json)
      if (record.scoreTotal) {
        talent.score = {
          final: record.scoreTotal,
          ...JSON.parse(record.scoreDetail)
        }
      }
      talent.parentId = record.id
      talent.parentContactStatus = record.contactStatus
      talent.processStatus = record.processStatus
      talent.recommendDetail = record.recommendReason
      return talent
    })
    total.value = res.data.total
  } catch (err) {
    console.error('Failed to fetch job requirements:', err)
  } finally {
    loading.value = false
  }
}

const fetchTabs = async () => {
  try {
    const res = await getRecommendationsTabs(20)
    if (res?.data?.jobRequirements?.length > 0) {
      jobTabs.value = res.data.jobRequirements
      // Set initial active tab
      if (jobTabs.value.length > 0) {
        activeJobId.value = jobTabs.value[0].id
        await fetchJobRequirements()
      }
    }
  } catch (err) {
    console.error('Failed to fetch tabs:', err)
  }
}

onMounted(() => {
  fetchTabs()

  const expire = Number(localStorage.getItem('closeXiaotui'))
  // 判断 expire 是否存在且与当前时间相差小于等于1天（86400000ms）
  if (expire && (expire - Date.now() > 0) && (expire - Date.now() <= 86400000)) {
    isClosed.value = true
  } else {
    localStorage.removeItem('closeXiaotui')
    isClosed.value = false
  }
})
</script>

<style lang="sass" scoped>
.header
  margin-bottom: 16px

  h3.title
    padding-left: 12px
    position: relative
    &::before
      position: absolute
      content: ''
      display: block
      width: 4px
      height: 16px
      border-radius: 2px
      background-color: #ff9111
      left: 0
      top: 50%
      transform: translateY(-50%)
</style>

export default {}