<template>
  <div class="mb-4">
    <a-tabs v-model:activeKey="activeKey" @change="handleTabChange">
      <a-tab-pane
        v-for="item in props.jobTabs"
        :key="item.id"
        :tab="item.processName + ' (' + item.recommendTalentCount + ')'"
      >
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue'

interface JobRequirement {
  id: number
  processName: string
  recommendTalentCount: number
}

const props = defineProps({
  jobTabs: {
    type: Array as PropType<JobRequirement[]>,
    required: true
  }
})

const emit = defineEmits(['change'])

const activeKey = ref<number>()

const handleTabChange = (key: number) => {
  emit('change', key)
}
</script>

<style lang="sass" scoped>
</style>