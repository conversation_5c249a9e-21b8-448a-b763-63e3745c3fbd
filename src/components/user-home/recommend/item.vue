<template>
  <div class="flex border-b border-gray-200 py-4">
    <div class="w-[100px] flex flex-col items-center mr-6">
      <div class="w-[100px] h-[88px] flex justify-center items-center mb-4 relative">
        <a-avatar :size="88" :src="item?.avatar" />
        <div class="absolute bottom-0 left-0 w-full h-5 bg-black/20  text-white text-center leading-[2.3rem] text-xs ">
          {{ getSite(item?.site) }}
        </div>
      </div>
      <div class="flex flex-col gap-2 w-full items-center">
        <a-popover trigger="hover" placement="right">
          <template #content>
            <div class="p-2 min-w-[200px]">
              <div class="flex font-bold border-b border-gray-100 pb-1 mb-2">
                <div class="w-1/2">评分项</div>
                <div class="w-1/2">分数</div>
              </div>
              <div v-for="(value, key) in score" :key="key" class="flex text-sm mb-1 last:mb-0">
                <div class="w-1/2 text-gray-500">{{ key }}:</div>
                <div class="w-1/2 break-all">{{ Array.isArray(value) ? value.join('、') : value }}</div>
              </div>
            </div>
          </template>
          <a-tag :color="scoreColor">匹配 {{ score?.final }} ></a-tag>
        </a-popover>
        <a-tag :color="jobColor">{{ jobStatus }}</a-tag>
        <a-tag color="red">{{ contactStatus }}</a-tag>
      </div>
    </div>
    <div class="flex-1 mx-6">
      <div class="flex justify-between items-center mb-4">
        <div class="flex flex-wrap items-center gap-x-2 text-lg font-semibold text-gray-900">
          <span>{{ item?.name }}</span>
          <span v-if="item?.age" class="text-gray-500 text-base">· {{ item?.age }}岁</span>
          <span v-if="item?.workingExperience" class="text-gray-500 text-base">· {{ item?.workingExperience }} 年工作经验</span>
          <span v-if="item?.highestDegree" class="text-gray-500 text-base">· {{ degreeMap[item?.highestDegree] }}</span>
          <span v-if="item?.nowLocation" class="text-gray-500 text-base">· {{ item?.nowLocation }}</span>
          <span v-if="item?.site" class="text-gray-500 text-base"> · 
            <a-tag>
              {{ getSite(item?.site) }}
            </a-tag>
          </span>
        </div>
        <span class="text-sm text-green-600 font-medium">{{ item?.online === 'true' ? '在线' : item?.activeStatus }}</span>
      </div>
      <div class="mb-4">
        <a-popover trigger="hover" placement="top">
          <template #content>
            <div class="max-w-[300px] text-gray-700">{{ item?.recommendDetail }}</div>
          </template>
          <div class="max-w-[300px] text-gray-600 cursor-pointer">
            {{ item?.recommendDetail.length > 200 ? item?.recommendDetail.slice(0, 200) + '...' : item?.recommendDetail }}
          </div>
        </a-popover>
      </div>
      <!-- 工作经历 & 教育经历 start -->
      <div class="flex gap-4">

        <div>
          <div v-if="item?.exps?.length > 0" class="flex flex-col gap-2">
            <div v-for="(exp, i) in item?.exps" :key="i" class="flex items-center text-sm text-gray-700">
              <div class=" text-gray-400">{{ formatTime(exp.startTime) }} - {{ formatTime(exp.endTime) }}</div>
              <div class="ml-2">{{ exp.company }}</div>
              <div v-if="exp.position" class="ml-2 text-gray-500">· {{ exp.position }}</div>
            </div>
          </div>
          <div v-if="item?.experiences?.length > 0" class="flex flex-col gap-2">
            <div v-for="(exp, i) in item?.experiences" :key="i" class="flex items-center text-sm text-gray-700">
              <div class=" text-gray-400">{{ exp?.startDate }} - {{ exp?.endDate }}</div>
              <div class="ml-2">{{ exp?.company }}</div>
              <div v-if="exp?.title" class="ml-2 text-gray-500">· {{ exp.title }}</div>
            </div>
          </div>
        </div>

        <div class="border-l border-gray-200 h-auto mx-4"></div>

        <div>
          <div v-if="item?.educations?.length > 0" class="flex flex-col gap-2">
            <div v-for="(exp, i) in item?.educations" :key="i" class="flex items-center text-sm text-gray-700" :class="i === 0 ? 'font-bold text-black' : ''">
              <div class=" text-gray-400">{{ exp.startDate }} - {{ exp.endDate }}</div>
              <div class="ml-2">{{ exp.school }} · {{ exp.speciality }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 工作经历 & 教育经历 end -->
    </div>
    <div class="flex flex-col items-center justify-center gap-4 min-w-[100px]">
      <a-button :type="hasContacted ? 'default' : 'primary'" :disabled="hasContacted" @click.prevent="handleChat">{{ hasContacted ? '已沟通' : '立即沟通' }}</a-button>
      <a-dropdown>
        <a-button :type="isUnfit ? 'danger' : 'default'">{{ isUnfit ? '不合适' : '不合适' }}</a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item @click.prevent="handleUnfit(item, opt)" v-for="opt in unfitOptions" :key="opt">{{ opt }}</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import dayjs from "dayjs"
import { PropType } from 'vue';

const emit = defineEmits(['chat', 'unfit', 'remove'])

const props = defineProps({
  key: String,
  item: Object,
  score: Object,
  scoreColor: String,
  jobStatus: String,
  jobColor: String,
  contactStatus: String,
  contactColor: String,
  unfitOptions: {
    type: Array as PropType<string[]>,
    default: () => ['工作经验不符', '城市不符', '学历不符', '其他条件不符']
  },
  degreeMap: {
    type: Object as PropType<Record<string, string>>,
    required: true
  },
});
const hasContacted = ref(props.item?.parentContactStatus === 1);
const isUnfit = ref(props.item?.processStatus === 2);
// console.log(`props.item?.parentContactStatus: `, props.item?.parentContactStatus)
const handleChat = ({ key }: { key: string }) => {
  hasContacted.value = true;
  // 跳转沟通页面
  emit('chat', {
    ...props.item
  })
};
const handleUnfit = (item: any, reason: string) => {
  isUnfit.value = true;
  emit('unfit', {
    ...props.item
  }, reason)
  emit('remove', props.item)
};

const formatTime = (val: string) => {
  if (!val) return ''
  if (val === '0') return ''

  return dayjs(Number(val) * 1000).format('YYYY.MM')
}

const getSite = (val: string) => {
  switch (Number(val)) {
    case 1:
    case 7:
      return 'ITP'
    case 2:
      return '猎聘'
    case 3:
      return '脉脉'
    case 4:
      return '领英'
    case 5:
      return 'Boss直聘'
    case 6:
      return 'SmartDeer'
    default:
      break;
  }
}
</script>

<style lang="scss" scoped>
.talent-card {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 16px 0;
}
.talent-info {
  flex: 1;
  margin-left: 16px;
}
.talent-header {
  display: flex;
  align-items: center;
  gap: 8px;
}
.talent-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}
.talent-score-detail {
  margin-top: 4px;
}
.site {
  background: RGBA(0, 0, 0, .2);
  backdrop-filter: blur(10px);
  color: #fff;
  height: 23px;
  line-height: 23px;
  text-align: center;
}

.score-detail {
  padding: 8px;
  min-width: 300px;
  
  .score-header {
    display: flex;
    margin-bottom: 8px;
    font-weight: bold;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 4px;
  }

  .score-item {
    display: flex;
    margin-bottom: 4px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .score-label {
    width: 120px;
    flex-shrink: 0;
  }

  .score-value {
    flex: 1;
    word-break: break-all;
  }
}
</style>