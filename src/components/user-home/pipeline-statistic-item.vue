<template lang="pug">
.statistic-item
  .left
    .number
      span.performance-number {{ symbol }} {{ displayValue }}
    .title {{ title }}

</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { onMounted, ref, toRefs } from 'vue'
import { FallOutlined, RiseOutlined } from '@ant-design/icons-vue'

const props = defineProps<{ value: number, title: string, symbol: string }>()
const { value, title, symbol } = toRefs(props)

const displayValue = computed(() => {
  if (!isNaN(value.value)) {
    if (symbol.value !== '') {
      return parseFloat((value.value/100).toFixed(2)).toLocaleString()
    } else {
      return parseFloat((value.value).toFixed(2)).toLocaleString()
    }
  }
  return 0
})
</script>

<style lang="sass" scoped>
.statistic-item
  display: flex
  align-items: flex-end
  justify-content: flex-start
  line-height: 1

  .left
    margin-right: 16px
    .number
      font-family: 'Bebas'
      // font-weight: bold
      font-size: 28px
      margin-bottom: 6px


    .title
      color: RGBA(0,0,0,.4)
  .right

    .compare
      margin-bottom: 8px
      font-size: 18px

      &.rise
        color: #52c41a

      &.fall
        color: #ff4d4f

      span
        margin-right: 4px

    .title
      color: RGBA(0,0,0,.2)

      .performance-number
        margin-left: 4px

</style>