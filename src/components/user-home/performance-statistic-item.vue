<template lang="pug">
.statistic-item
  .left
    .number
      span.performance-number ${{ displayValue }}
    .title {{ title }}

  .right(v-if="compareTitle")
    .compare
      span {{ changeRatio }}
      FallOutlined(v-if="isRise === false" style="font-size: 18px")
      RiseOutlined(v-if="isRise === true" style="font-size: 18px")
    .title 
      span {{ compareTitle }} 
      span.performance-number ${{ displayCompareValue }}

</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { onMounted, ref, toRefs } from 'vue'
import { FallOutlined, RiseOutlined } from '@ant-design/icons-vue'

const props = defineProps<{ value: number, compareTitle?: string, title: string, compareValue: number }>()
const { value, title, compareTitle, compareValue } = toRefs(props)

const displayValue = computed(() => {
  if (!isNaN(value.value)) {
    return parseFloat((value.value/100).toFixed(2)).toLocaleString()
  }
  return 0
})

const displayCompareValue = computed(() => {
  if (!isNaN(compareValue.value)) {
    return parseFloat((compareValue.value/100).toFixed(2)).toLocaleString()
  }
  return 0
})

const changeRatio = computed(() => {
  if (isNaN(value.value)) return ''
  if (compareValue.value === 0) return ''
  if (value.value == compareValue.value) return '持平'
  if (value.value > compareValue.value) return `${((value.value - compareValue.value) / compareValue.value * 100).toFixed(2)}%`
  if (value.value < compareValue.value) return `${((compareValue.value - value.value) / compareValue.value * 100).toFixed(2)}%`
})

const isRise = computed(() => {
  if (isNaN(value.value)) return null
  if (compareValue.value === 0 || !compareValue.value) return null
  if (value.value == compareValue.value) return null
  return value.value > compareValue.value
})
</script>

<style lang="sass" scoped>
.statistic-item
  display: flex
  align-items: flex-end
  justify-content: flex-start
  line-height: 1

  .left
    margin-right: 16px
    .number
      font-family: 'Bebas'
      // font-weight: bold
      font-size: 28px
      margin-bottom: 6px


    .title
      color: RGBA(0,0,0,.4)
  .right

    .compare
      margin-bottom: 8px
      font-size: 18px

      &.rise
        color: #52c41a

      &.fall
        color: #ff4d4f

      span
        margin-right: 4px

    .title
      color: RGBA(0,0,0,.2)

      .performance-number
        margin-left: 4px

</style>