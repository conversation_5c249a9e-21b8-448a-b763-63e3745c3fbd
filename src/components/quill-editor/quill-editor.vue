<script setup lang="ts">
import { ref, reactive, onMounted, watch, toRef } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { API_URL } from '@/api/customer'
import { useUserStore } from '@/store/user.store'

const block = Quill.import('blots/block')
block.tagName = 'div'
Quill.register(block, true)

const userStore = useUserStore()

const emit = defineEmits(['update:value', 'cllback'])
const props = defineProps<{value: string}>()

const value = toRef(props, 'value')
const content = ref('')
const editor = ref()

const options = {
  container: [
    'bold', 'italic', 'underline',
    { 'header': 1 },
    { 'header': 2 },
    {
      'align': ''
    },
    {
      'align': 'center'
    },
    {
      'align': 'right'
    },

    {
      'list': 'ordered'
    }, {
      'list': 'bullet'
    },
    { 'indent': '+1' },
    { 'indent': '-1' },
    { 'size': ['small', false, 'large', 'huge'] },
    'image',
    'clean'
  ],
  handlers: {
    'image': function (value: boolean) {
      if (value) {
        const don : any = document.querySelector('#quill_upload input')
        don.click()
      } else {
        editor.value.format('image', false)
      }
    }
  },
} 

let curInnerHTML = ''


onMounted(() => {
  curInnerHTML = ''
})

onMounted(() => {
  handleQuill()
})

// 可以直接侦听一个 props
watch(() => props.value, async (newVal, oldVal) => {
  if (newVal == editor.value.root.innerHTML) return
  curInnerHTML = newVal
  editor.value.root.innerHTML = newVal
})

// 初始化
const handleQuill = () => {
  editor.value = new Quill('#editor', {
    modules: {
      toolbar: options
    },
    theme: 'snow'
  })

  editor.value.on('text-change', () => {
    emit('update:value', editor.value.root.innerHTML)
  })

  editor.value.clipboard.addMatcher(Node.ELEMENT_NODE, (node: any, delta: any) => {
    delta.ops = delta.ops.map((op:any) => {
      return {
        insert: op.insert
      }
    })
    return delta
  })
}

// 图片上传
const handleUpload = (info: any) => {
  let newRange = editor.value.selection.savedRange.index

  if (info.file.status === 'done') {
    const fileResponse: any = info.file.response.data
    const url = `https://image.itp.smartdeer.work/images/${fileResponse.id}`

    editor.value.insertEmbed(newRange, 'image', url, {
      'max-width': '100%'
    })
    editor.value.setSelection(1 + newRange)
  }
}

// watch(()=>props.value, (newVal, oldVal) => {
//   value.value
// })

</script>
<template lang="pug">
.quill-editor
  #editor {{ content }}
  #quill_upload
    a-upload.logo_uploader(
      name="image",
      accept="png,jpg,jpeg",
      :show-upload-list="false",
      :action="API_URL.LOGO_UPLOAD",
      :headers="{'Authorization': userStore.token}",
      @change="handleUpload"
  )
</template>

<style scoped lang="scss">
#editor {
  min-height: 300px;
}

::deep svg {
  width: 16px;
  margin-top: 3px;
}

::deep .ql-bold {
  border: none;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

::deep .ql-italic {
  border: none;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

::deep .ql-underline {
  border: none;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}
::deep .ql-align {
  border: none;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

::deep .ql-list {
  border: none;
  background: #fff;
	border-right: 1px solid #f0f0f0;
}

::deep .ql-toolbar {
	padding: 0;
	height: 30px;
}

::deep .ql-formats {
	height: 20px;
	line-height: 20px;
	margin-top: -18px;
}

::deep #editor {
	padding: 5px;
}

::deep #editor p {
	line-height: normal;
	white-space: normal;
	font-size: 10pt;
	font-family: KaiTi;
}
::deep .ql-picker.ql-size .ql-picker-label{
  color: #f0f0f0 !important;
  &::before{
      content: none;
  }
}

::deep .ql-clean {
  border: none;
  background: #fff;
  border-right: 1px solid #f0f0f0;
}
</style>