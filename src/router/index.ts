import {
  createRouter,
  createWeb<PERSON><PERSON><PERSON>,
  RouteRecordRaw,
  createWebHashHistory,
} from "vue-router"
import { Modal } from "ant-design-vue"

import BasicLayout from "@/layouts/basic-layout.vue"
import AccountLayout from "@/layouts/account-layout.vue"
import { useUserStore } from "@/store/user.store"
import { useAppStore } from "@/store/app.store"
import { PERMISSIONS, hasPermission } from "@/utils/permission"
import tracker from "@/utils/tracker"

const routes: RouteRecordRaw[] = [
  {
    path: "/talent",
    redirect: "/talent/list/me",
    component: BasicLayout,
    children: [
      {
        path: "list",
        name: "company-talent-list",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.company.code] },
        component: () => import("@/views/talent/company-talent-list.vue"),
      },
      {
        path: "list/all",
        name: "platform-talent-list",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.all.code] },
        component: () => import("@/views/talent/platform-talent-list.vue"),
      },
      {
        path: "list/me",
        name: "talent-list-me",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.mine.code] },
        component: () => import("@/views/talent/my-talent-list-interview.vue"),
      },
      {
        path: "list/me/interview",
        name: "talent-list-me-interview",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.mine.code] },
        component: () => import("@/views/talent/my-talent-list-interview.vue"),
      },
      {
        path: "list/me/offered",
        name: "talent-list-me-offered",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.mine.code] },
        component: () => import("@/views/talent/my-talent-list-offered.vue"),
      },
      {
        path: "resume/upload",
        name: "talent-upload",
        meta: { auth: true },
        component: () => import("@/views/talent/resume-upload.vue"),
      },

      {
        path: "create",
        name: "talent-create",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.add.code] },
        component: () => import("@/views/talent/talent-edit.vue"),
      },

      {
        path: "batch/create",
        name: "talent-batch-create",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.add.code] },
        component: () => import("@/views/talent/talent-multi-upload.vue"),
      },

      {
        path: ":id/edit",
        name: "talent-edit",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.add.code] },
        component: () => import("@/views/talent/talent-edit.vue"),
      },

      // {
      //   path: ":id/resume/preview",
      //   name: "talent-resume-preview",
      //   meta: { auth: true },
      //   component: () => import("@/views/talent/talent-resume-preview.vue"),
      // },

      {
        path: ":id/detail",
        name: "talent-detail",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.unite.code] },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/talent/talent-detail.vue"),
      },
      {
        path: "web/search",
        name: "talent-web-search",
        meta: { auth: true, permissions: [PERMISSIONS.talent.children.unite.code] },
        component: () => import("@/views/talent/talent-global-search.vue"),
      },
      {
        path: ":talentId/job/:jobId/process/:processId/report",
        name: "talent-recommend-report-create",
        meta: { auth: true },
        props: (route) => ({
          talentId: Number(route.params.talentId),
          processInstanceId: route.params.processId,
          jobRequirementId: Number(route.params.jobId),
        }),
        component: () => import("@/views/talent/talent-report.vue"),
      },
    ],
  },

  // 职位
  {
    path: "/customer",
    redirect: "/customer/list",
    component: BasicLayout,
    children: [
      {
        path: "list",
        name: "customer-list",
        meta: { auth: true, permissions: [PERMISSIONS.customer.children.company.code] },
        component: () => import("@/views/customer/customer-list.vue"),
      },
      {
        path: "list/me",
        name: "my-customer-list",
        meta: { auth: true, permissions: [PERMISSIONS.customer.children.mine.code] },
        component: () => import("@/views/customer/my-customer-list.vue"),
      },
      {
        path: ":id/detail",
        name: "customer-detail",
        meta: { auth: true },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/customer/customer-detail.vue"),
      },
      {
        path: ":id/save/success",
        name: "customer-save-success",
        meta: { auth: true },
        component: () => import("@/views/customer/customer-save-success.vue"),
      },
      {
        path: "create",
        name: "customer-create",
        meta: { auth: true, permissions: [PERMISSIONS.customer.children.add.code] },
        component: () => import("@/views/customer/customer-create.vue"),
      },
    ],
  },

  // 职位
  // {
  //   path: "/position",
  //   component: BasicLayout,
  //   children: [
  //     {
  //       path: ":id/detail",
  //       name: "position-detail",
  //       meta: { auth: true },
  //       component: () => import("@/views/position/position-detail.vue"),
  //     },
  //     {
  //       path: "list",
  //       name: "position-list",
  //       meta: { auth: true },
  //       component: () => import("@/views/position/position-list.vue"),
  //     },
  //   ],
  // },

  // 职位
  {
    path: "/job",
    redirect: "/job/list/me",
    component: BasicLayout,
    children: [
      {
        path: "list",
        name: "job-list",
        meta: { auth: true, permissions: [PERMISSIONS.project.children.company.code] },
        component: () => import("@/views/job/job-requirement-list.vue"),
      },
      {
        path: "list/me",
        name: "my-job-list",
        meta: { auth: true, permissions: [PERMISSIONS.project.children.mine.code] },
        component: () => import("@/views/job/my-job-requirement-list.vue"),
      },
      {
        path: ":id/detail",
        name: "job-detail",
        meta: { auth: true },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/job/job-requiremnet-detail.vue"),
      },
      {
        path: ":id/save/success",
        name: "job-save-success",
        meta: { auth: true },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/job/job-save-success.vue"),
      },
      {
        path: "create",
        name: "job-create",
        meta: { auth: true, permissions: [PERMISSIONS.project.children.add.code] },
        component: () => import("@/views/job/job-create.vue"),
      },
    ],
  },

  {
    path: "/marketing",
    component: BasicLayout,
    children: [
      {
        path: "activity/create",
        name: "activity-create",
        meta: { auth: true, permissions: [PERMISSIONS.operation.children.promotion_edit.code] },
        component: () =>
          import("@/views/marketing/activity/activity-create.vue"),
      },
      {
        path: "activity/list",
        name: "activity-list",
        meta: { auth: true, permissions: [PERMISSIONS.operation.children.promotion.code] },
        component: () => import("@/views/marketing/activity/activity-list.vue"),
      },

      {
        path: "activity/:id/detail",
        name: "activity-detail",
        meta: { auth: true },
        component: () =>
          import("@/views/marketing/activity/activity-detail.vue"),
      },

      {
        path: "activity/staff/:id/list",
        name: "activity-staff-list",
        meta: { auth: true },
        component: () =>
          import("@/views/marketing/activity/activity-staff-list.vue"),
      },

      {
        path: "candidate/list",
        name: "candidate-list",
        meta: { auth: true, permissions: [PERMISSIONS.operation.children.leads.code] },
        component: () =>
          import("@/views/marketing/candidate/candidate-list.vue"),
      },

      {
        path: "candidate/:id/detail",
        name: "candidate-detail",
        meta: { auth: true },
        component: () =>
          import("@/views/marketing/candidate/candidate-detail.vue"),
      },
    ],
  },

  {
    path: "/system",
    component: BasicLayout,
    redirect: "/system/staff/list",
    children: [
      {
        path: "staff/list",
        name: "system-staff-list",
        meta: { auth: true, permissions: [PERMISSIONS.company.children.staff.code] },
        component: () => import("@/views/system/staff.vue"),
      },
      {
        path: "roles/list",
        name: "system-roles-list",
        meta: { auth: true, permissions: [PERMISSIONS.company.children.role.code] },
        component: () => import("@/views/system/roles.vue"),
      },
      {
        path: "department/list",
        name: "system-department-list",
        meta: { auth: true, permissions: [PERMISSIONS.company.children.department.code] },
        component: () => import("@/views/system/department.vue"),
      },
      {
        path: "performance/edit",
        name: "system-performance-edit",
        meta: { auth: true, permissions: [PERMISSIONS.company.children.performance.code] },
        component: () => import("@/views/system/performance-edit.vue"),
      },
      {
        path: "roles/:id/permission",
        name: "system-roles-permission",
        meta: { auth: true, permissions: [PERMISSIONS.company.children.permission.code] },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/system/permission.vue"),
      }
    ],
  },

  // 灵鹿聘小程序平台
  {
    path: "/wechat",
    component: BasicLayout,
    redirect: "/wechat/industry/knowledge/list",
    children: [
      {
        path: "industry/knowledge/list",
        name: "knowledge-list",
        meta: { auth: true },
        component: () => import("@/views/wechat/industry/knowledge-list.vue"),
      },
      {
        path: "industry/knowledge/:id/detail",
        name: "knowledge-detail",
        meta: { auth: true },
        component: () => import("@/views/wechat/industry/knowledge-details.vue"),
      },
      {
        path: "industry/knowledge/create",
        name: "knowledge-create",
        meta: { auth: true },
        component: () => import("@/views/wechat/industry/knowledge-details.vue"),
      },
    ],
  },

  // user
  {
    path: "/user",
    component: BasicLayout,
    redirect: "/user/home",
    children: [
      {
        path: "home",
        name: "user-home",
        meta: { auth: true },
        component: () => import("@/views/user/home.vue"),
      }
    ],
  },

  {
    path: "/account/login",
    name: "account-login",
    component: () => import("@/views/account/login.vue"),
  },

  // 账户管理
  {
    path: "/account",
    // component: () => import('@/views/account/login.vue'),
    // name: 'account-login'
    component: AccountLayout,
    children: [
      {
        path: "home",
        name: "account-home",
        meta: { auth: true },
        component: () => import("@/views/account/home.vue"),
      },
      {
        path: "bind",
        name: "account-bind",
        meta: { auth: true },
        component: () => import("@/views/account/bind.vue"),
      },
      {
        path: "password/change",
        name: "account-password-change",
        meta: { auth: true },
        component: () => import("@/views/account/changepassword.vue"),
      },
    ],
  },

  // 统计
  {
    path: "/stat",
    component: BasicLayout,
    children: [
      {
        path: "ca",
        name: "ca-stat",
        meta: { auth: true, permissions: [PERMISSIONS.statistic.children.ca.code] },
        component: () => import("@/views/stat/ca-stat.vue"),
      },
      {
        path: "pm",
        name: "pm-stat",
        meta: { auth: true, permissions: [PERMISSIONS.statistic.children.pm.code] },
        component: () => import("@/views/stat/pm-stat.vue"),
      },
      {
        path: "bd",
        name: "bd-stat",
        meta: { auth: true, permissions: [PERMISSIONS.statistic.children.bd.code] },
        component: () => import("@/views/stat/bd-stat.vue"),
      },
      {
        path: "offer",
        name: "offer-stat",
        meta: { auth: true, permissions: [PERMISSIONS.statistic.children.offer.code] },
        component: () => import("@/views/stat/offer-stat.vue"),
      },
      {
        path: "offer/receivable",
        name: "offer-receivable-stat",
        meta: { auth: true, permissions: [PERMISSIONS.statistic.children.offer.code] },
        component: () => import("@/views/stat/offer-receivable-stat.vue"),
      }
      // {
      //   path: "finance",
      //   name: "finance-stat",
      //   meta: { auth: true, permissions: [PERMISSIONS.statistic.children.finance.code] },
      //   component: () => import("@/views/stat/finance-stat.vue"),
      // },
    ],
  },
  {
    path: "/performance",
    component: BasicLayout,
    children: [
      {
        path: "data",
        name: "performance-data",
        meta: { auth: true },
        props: (route) => ({ timeRange: route.query.timerange }),
        component: () => import("@/views/account/home.vue"),
      },

      {
        path: "me",
        name: "performance-me",
        meta: { auth: true, permissions: [PERMISSIONS.performance.children.mine.code] },
        props: (route) => ({ timeRange: route.query.timerange }),
        component: () => import("@/views/performance/performance-me.vue"),
      },
      {
        path: "job/:jobId/task/:processInstanceId/commission/config",
        name: "offer-commission-config",
        meta: { auth: true, permissions: [PERMISSIONS.performance.children.project.code] },
        props: true,
        component: () => import("@/views/performance/commission-config.vue"),
      },
      {
        path: 'job/:jobId/process/:processInstanceId/detail',
        name: 'job-performance-detail',
        meta: { auth: true },
        props: true,
        component: () => import("@/views/performance/job-performance-detail.vue")
      },
      {
        path: "offer/list",
        name: "performance-offer-list",
        meta: { auth: true, permissions: [PERMISSIONS.performance.children.project.code] },
        component: () => import("@/views/performance/performance-offer-list.vue"),
      },
    ],
  },

  // 财务
  {
    path: "/finance",
    component: BasicLayout,
    children: [
      {
        path: "invoice/list/my",
        name: "invoice-list-my",
        meta: { auth: true, permissions: [PERMISSIONS.finance.children.mine.code] },
        component: () => import("@/views/finance/invoice-list-my.vue"),
      },
      {
        path: "invoice/list",
        name: "invoice-list",
        meta: { auth: true, permissions: [PERMISSIONS.finance.children.invoice.code] },
        component: () => import("@/views/finance/invoice-list.vue"),
      },
      {
        path: "payment/list",
        name: "payment-claim-list",
        meta: { auth: true, permissions: [PERMISSIONS.finance.children.claim.code] },
        component: () => import("@/views/finance/payment-claim-list.vue"),
      },
      {
        path: "payment/claim/:id/claim",
        name: "claim-payment",
        meta: { auth: true, permissions: [PERMISSIONS.finance.children.claim.code] },
        props: (route) => ({ id: Number(route.params.id) }),
        component: () => import("@/views/finance/claim-payment.vue"),
      },
      {
        path: "statement/list",
        name: "statement-list",
        meta: { auth: true, permissions: [PERMISSIONS.finance.children.revenue.code] },
        component: () => import("@/views/finance/statement-list.vue"),
      }
    ],
  },
  {
    path: "/rcn",
    component: BasicLayout,
    children: [
      {
        path: "stat",
        name: "rcn-stat",
        meta: { auth: false },
        component: () => import("@/views/stat/rcn-stat.vue"),
      }
    ]
  },

  {
    path: "/chat",
    component: BasicLayout,
    children: [
      {
        path: "unread/list",
        name: "unread-list",
        meta: { auth: true },
        component: () => import("@/views/chat/unread-list.vue"),
      },
    ],
  },

  {
    path: '/error',
    component: BasicLayout,
    children: [
      {
        path: '403',
        name: 'error-403',
        component: () => import('@/views/exception/403.vue'),
      },
    ],
  },

  {
    path: "/:pathMatch(.*)*",
    name: "not-found",
    component: () => import("@/views/exception/404.vue"),
  },

  {
    path: "/",
    name: "root",
    redirect: { name: "user-home" },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

function permissionCheck(permissions: string[] | any): boolean {
  // 默认没有权限检查需要，即放行
  if (!permissions) return true

  let result = false
  permissions.forEach((code: string) => {
    result = result || hasPermission(code)
  })
  return result
}

router.beforeEach((to, from, next) => {

  const result = permissionCheck(to.meta.permissions)
  const userStore = useUserStore()

  if (!result) {
    next({ name: 'error-403' })
  } else if (to.meta.auth && !userStore.token) {
    next({ name: "account-login" })
  } else if (to.meta.admin && !userStore.isAdmin) {
    next({ name: "account-login" })
  } else {
    next()
  }
})

router.afterEach((to, from, next) => {
  window.scrollTo(0, 0)
  tracker.pageview(to.name?.toString()!, { referer: from.name, to: to.name })
})

router.onError((error, to, from) => {
  const pattern = /imported module/g
  const isChunkLoadFailed = pattern.test(error.message)

  if (isChunkLoadFailed && to.path) {
    Modal.info({
      title: "版本更新通知：全新功能与优化！",
      content:
        "亲爱的用户，我们为您带来了全新的版本更新！本次更新包含了一系列全新的功能和优化，旨在提升您的使用体验。我们修复了一些已知问题，并增加了更多的稳定性和性能改进。请尽快更新以享受最新的功能和改进。感谢您的支持和反馈，我们将继续努力为您提供更好的服务！",
      onOk() {
        window.location.href = `${window.location.origin}${to.path}`
      },
    })
  } else {
    console.log("router.onError", error)
  }
})

export default router
