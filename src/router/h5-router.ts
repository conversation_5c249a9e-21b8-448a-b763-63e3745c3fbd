import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
  RouteRecordRaw,
} from "vue-router"

import PositionDetailPage from "@/views/h5/position-detail.vue"
import TalentTouchPage from '@/views/h5/talent-touch.vue'

const routes: RouteRecordRaw[] = [
  {
    path: "/h5/position/:id",
    name: 'h5-position-detail',
    component: PositionDetailPage,
    props: (route) => ({ id: route.params.id })
  },
  {
    path: "/h5/talent/:talentId/position/:jobId/touch-me",
    name: 'talent-touch-me',
    component: TalentTouchPage,
    props: (route) => ({ talentId: Number(route.params.talentId), jobId: Number(route.params.jobId), uuid: route.query.uuid })
  }
]

// 开发环境使用hash模式，真正对外暴露的是history模式
// 举例：开发环境：http://localhost:3000/h5/#/h5/position/1
// 举例：线上环境：http://localhost:3000/h5/position/1
const history = ['test', 'production'].includes(import.meta.env.VITE_VUE_APP_BUILD_ENV)
  ? createWebHistory()
  : createWebHashHistory()

const router = createRouter({
  history: history,
  routes,
})

export default router