interface UnionSearchParams {
  // 年龄
  ageFrom: number,
  ageTo: number,

  // 工作年限
  workAgeFrom: number | null,
  workAgeTo: number | null,

  // 所在城市ID
  areaIds: number[],
  // 期望城市ID
  demandAreaIds: number[],
  // 公司名称
  companies: string[],
  companyJoined: string,
  educationDegree: number,
  gender: number,

  is211: boolean,
  is985: boolean,
  major: string,

  // 统招本科， 0-不限，1-统招本科
  // TODO，猎聘会有更多的参数。
  nationalUnified: number,

  // 0-不限，1-当前，2-过往
  companyPeriod: number,

  // 0-不限，1-当前，2-过往
  positionPeriod: number,

  // 海外工作经验
  overseasWork: boolean,

  keywords: string[],
  positionKeywordsAny: boolean,

  // 类型是字符串，使用逗号分隔
  positionName: string[],

  schools: string[],

  // 当前薪资和期望薪资
  currentSalaryFrom: number,
  currentSalaryTo: number,
  demandSalaryFrom: number,
  demandSalaryTo: number,

  industryIds: number[],

  searchCount: number,

  // 暂时先不用
  lastRank?: number,
  // 暂时先不用。
  cities?: string[],
  // 暂时不用。
  searchId?: string,

  // 该参数只是给灵鹿聘使用
  fields?: string[],
  phone?: string,
  email?: string,
  name?: string,

  [key?: string]: any
}

namespace ICB {
  type TrackEvent = {
    clientTime: number
    app: string
    version: string
    // 这个用于客户端的唯一标
    uuid: string
    // 操作系统
    os: string
    // 操作系统版本
    osVersion: string
    // 网络类型，这个留
    networkType: string
    // 事件id，这个自己定
    eventId: string
    // 事件类
    eventType: string
    // 事件内容，必须以JSON
    eventContent: string

    userId?: string
  }

}


