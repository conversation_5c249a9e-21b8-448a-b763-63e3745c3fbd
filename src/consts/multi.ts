interface Other {
  [propName: string]: any;
}

export const WORK_YEAR = [
  {
    label: "不限",
    value: "0,100",
  },
  {
    label: "1年以内",
    value: "0,1",
  },
  {
    label: "1-3年",
    value: "1,3",
  },
  {
    label: "3-5年",
    value: "3,5",
  },
  {
    label: "5-10年",
    value: "5,10",
  },
  {
    label: "10年以上",
    value: "10,100",
  },
];

export const EDUCATION_LIST = [
  {
    label: "不限",
    value: "0",
  },
  {
    label: "本科及以上",
    value: "1",
  },
  {
    label: "硕士及以上",
    value: "2",
  },
  {
    label: "博士及以上",
    value: "3",
  },
  {
    label: "高中及以下",
    value: "4",
  },
  {
    label: "专科及以下",
    value: "5",
  },
  {
    label: "专科及以上",
    value: "6",
  },
];

export const DIC_DEGREE = {
  "0": null,
  "1": {
    min: 7,
    value: "本科及以上",
  },
  "2": {
    min: 8,
    value: "硕士及以上",
  },
  "3": {
    min: 9,
    value: "博士及以上",
  },
  "4": {
    min: 4,
    value: "高中及以下",
  },
  "5": {
    min: 6,
    value: "专科及以下",
  },
  "6": {
    min: 5,
    value: "专科及以上",
  },
};

export const UPDATE_TIME = [
  {
    label: "不限",
    value: "不限",
  },
  {
    label: "一周内",
    value: "一周内",
  },
  {
    label: "二周内",
    value: "二周内",
  },
  {
    label: "一月内",
    value: "一月内",
  },
  {
    label: "三月内",
    value: "三月内",
  },
  {
    label: "半年内",
    value: "半年内",
  },
  {
    label: "一年内",
    value: "一年内",
  },
];

export const CURRENT_STATUS = [
  {
    label: "不限",
    value: "不限",
  },
  {
    label: "在职，看看新机会",
    value: "在职，看看新机会",
  },
  {
    label: "在职，急寻新工作",
    value: "在职，急寻新工作",
  },
  {
    label: "在职，暂无跳槽打算",
    value: "在职，暂无跳槽打算",
  },
  {
    label: "离职，正在找工作",
    value: "离职，正在找工作",
  },
];

export const INDUSTRY_LIST = [
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__IT互联网",
    schema_defs: ["EntityItem"],
    name: "IT/互联网",
    css_style: "plain",
    order_index: 11,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__游戏",
        schema_defs: ["EntityItem"],
        name: "游戏",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__游戏",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "游戏",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__区块链",
        schema_defs: ["EntityItem"],
        name: "区块链",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__区块链",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "区块链",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__云计算/大数据",
        schema_defs: ["EntityItem"],
        name: "云计算/大数据",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__云计算/大数据",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "云计算/大数据",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他IT互联网",
        schema_defs: ["EntityItem"],
        name: "其他IT/互联网",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他IT/互联网",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__新闻资讯",
        schema_defs: ["EntityItem"],
        name: "新闻资讯",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "新闻资讯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__搜索",
        schema_defs: ["EntityItem"],
        name: "搜索",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "搜索",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__信息聚合",
        schema_defs: ["EntityItem"],
        name: "信息聚合",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "信息聚合",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__即时通讯",
        schema_defs: ["EntityItem"],
        name: "即时通讯",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "即时通讯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__影视音频",
        schema_defs: ["EntityItem"],
        name: "影视音频",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "影视音频",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__视频/直播",
        schema_defs: ["EntityItem"],
        name: "视频/直播",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "视频/直播",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__信息安全",
        schema_defs: ["EntityItem"],
        name: "信息安全",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "信息安全",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__人工智能",
        schema_defs: ["EntityItem"],
        name: "人工智能",
        css_style: "plain",
        order_index: 8,
        keywords: ["ai"],
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "人工智能",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__游戏",
        schema_defs: ["EntityItem"],
        name: "游戏",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "游戏",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__社交网络",
        schema_defs: ["EntityItem"],
        name: "社交网络",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "社交网络",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__工具软件",
        schema_defs: ["EntityItem"],
        name: "工具软件",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__IT互联网",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "工具软件",
        leaf: false,
      },
    ],
    has_children: true,
    code: "IT/互联网",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__电信/通信技术",
    schema_defs: ["EntityItem"],
    name: "电信/通信技术",
    css_style: "plain",
    order_index: 12,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__运营商",
        schema_defs: ["EntityItem"],
        name: "运营商",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "运营商",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__设备制造",
        schema_defs: ["EntityItem"],
        name: "设备制造",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "设备制造",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__电信ICT",
        schema_defs: ["EntityItem"],
        name: "电信ICT",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "电信ICT",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__终端制造",
        schema_defs: ["EntityItem"],
        name: "终端制造",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "终端制造",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__芯片制造",
        schema_defs: ["EntityItem"],
        name: "芯片制造",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "芯片制造",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__通信测试",
        schema_defs: ["EntityItem"],
        name: "通信测试",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "通信测试",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__设备代理",
        schema_defs: ["EntityItem"],
        name: "设备代理",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "设备代理",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__视频通信",
        schema_defs: ["EntityItem"],
        name: "视频通信",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "视频通信",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他电信/通信技术",
        schema_defs: ["EntityItem"],
        name: "其他电信/通信技术",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__电信/通信技术",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他电信/通信技术",
        leaf: false,
      },
    ],
    has_children: true,
    code: "电信/通信技术",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__能源矿产",
    schema_defs: ["EntityItem"],
    name: "能源矿产",
    css_style: "plain",
    order_index: 13,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__新能源新材料",
        schema_defs: ["EntityItem"],
        name: "新能源新材料",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__能源矿产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "新能源新材料",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__金属",
        schema_defs: ["EntityItem"],
        name: "金属",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__能源矿产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "金属",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__非金属",
        schema_defs: ["EntityItem"],
        name: "非金属",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__能源矿产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "非金属",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__石油天然气",
        schema_defs: ["EntityItem"],
        name: "石油天然气",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__能源矿产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "石油天然气",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他能源矿产",
        schema_defs: ["EntityItem"],
        name: "其他能源矿产",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__能源矿产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他能源矿产",
        leaf: false,
      },
    ],
    has_children: true,
    code: "能源矿产",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__航空/航天",
    schema_defs: ["EntityItem"],
    name: "航空/航天",
    css_style: "plain",
    order_index: 14,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__军用航天",
        schema_defs: ["EntityItem"],
        name: "军用航天",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__航空/航天",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "军用航天",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__民用航天",
        schema_defs: ["EntityItem"],
        name: "民用航天",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__航空/航天",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "民用航天",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他航空/航天",
        schema_defs: ["EntityItem"],
        name: "其他航空/航天",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__航空/航天",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他航空/航天",
        leaf: false,
      },
    ],
    has_children: true,
    code: "航空/航天",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__生产制造",
    schema_defs: ["EntityItem"],
    name: "生产制造",
    css_style: "plain",
    order_index: 15,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__船舶制造",
        schema_defs: ["EntityItem"],
        name: "船舶制造",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "船舶制造",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__食品加工制造业",
        schema_defs: ["EntityItem"],
        name: "食品加工制造业",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "食品加工制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__服装制造业",
        schema_defs: ["EntityItem"],
        name: "服装制造业",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "服装制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__家具制造业",
        schema_defs: ["EntityItem"],
        name: "家具制造业",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "家具制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__汽车制造业",
        schema_defs: ["EntityItem"],
        name: "汽车制造业",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "汽车制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__汽车零部件",
        schema_defs: ["EntityItem"],
        name: "汽车零部件",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "汽车零部件",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__金属制品制造业",
        schema_defs: ["EntityItem"],
        name: "金属制品制造业",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "金属制品制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__化学制品制造业",
        schema_defs: ["EntityItem"],
        name: "化学制品制造业",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "化学制品制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__通用设备制造业",
        schema_defs: ["EntityItem"],
        name: "通用设备制造业",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "通用设备制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__材料工程",
        schema_defs: ["EntityItem"],
        name: "材料工程",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "材料工程",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__工程机械制造业",
        schema_defs: ["EntityItem"],
        name: "工程机械制造业",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "工程机械制造业",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__智能家居",
        schema_defs: ["EntityItem"],
        name: "智能家居",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "智能家居",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__消费电子",
        schema_defs: ["EntityItem"],
        name: "消费电子",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "消费电子",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__芯片半导体",
        schema_defs: ["EntityItem"],
        name: "芯片半导体",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "芯片半导体",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__机器人",
        schema_defs: ["EntityItem"],
        name: "机器人",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "机器人",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__3D打印",
        schema_defs: ["EntityItem"],
        name: "3D打印",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "3D打印",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__传感器及中间件",
        schema_defs: ["EntityItem"],
        name: "传感器及中间件",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "传感器及中间件",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__飞行器",
        schema_defs: ["EntityItem"],
        name: "飞行器",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "飞行器",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__硬件",
        schema_defs: ["EntityItem"],
        name: "硬件",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "硬件",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__可穿戴设备",
        schema_defs: ["EntityItem"],
        name: "可穿戴设备",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "可穿戴设备",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他生产制造",
        schema_defs: ["EntityItem"],
        name: "其他生产制造",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "classification_of_industry_code_list__生产制造",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他生产制造",
        leaf: false,
      },
    ],
    has_children: true,
    code: "生产制造",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__房地产",
    schema_defs: ["EntityItem"],
    name: "房地产",
    css_style: "plain",
    order_index: 16,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__租房",
        schema_defs: ["EntityItem"],
        name: "租房",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__房地产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "租房",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__装修装潢",
        schema_defs: ["EntityItem"],
        name: "装修装潢",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__房地产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "装修装潢",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__商业房产",
        schema_defs: ["EntityItem"],
        name: "商业房产",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__房地产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "商业房产",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他房地产",
        schema_defs: ["EntityItem"],
        name: "其他房地产",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__房地产",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他房地产",
        leaf: false,
      },
    ],
    has_children: true,
    code: "房地产",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__教育",
    schema_defs: ["EntityItem"],
    name: "教育",
    css_style: "plain",
    order_index: 1,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__儿童早教",
        schema_defs: ["EntityItem"],
        name: "儿童早教",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "儿童早教",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__K12",
        schema_defs: ["EntityItem"],
        name: "K12",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "K12",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__高等教育",
        schema_defs: ["EntityItem"],
        name: "高等教育",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "高等教育",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__职业培训",
        schema_defs: ["EntityItem"],
        name: "职业培训",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "职业培训",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__出国留学",
        schema_defs: ["EntityItem"],
        name: "出国留学",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "出国留学",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__语言学习",
        schema_defs: ["EntityItem"],
        name: "语言学习",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "语言学习",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__兴趣教育",
        schema_defs: ["EntityItem"],
        name: "兴趣教育",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "兴趣教育",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__教育服务",
        schema_defs: ["EntityItem"],
        name: "教育服务",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "教育服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__教辅设备",
        schema_defs: ["EntityItem"],
        name: "教辅设备",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "教辅设备",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他教育",
        schema_defs: ["EntityItem"],
        name: "其他教育",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__教育",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他教育",
        leaf: false,
      },
    ],
    has_children: true,
    code: "教育",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__金融",
    schema_defs: ["EntityItem"],
    name: "金融",
    css_style: "plain",
    order_index: 2,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__股票",
        schema_defs: ["EntityItem"],
        name: "股票",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "股票",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__借贷",
        schema_defs: ["EntityItem"],
        name: "借贷",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "借贷",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__风控",
        schema_defs: ["EntityItem"],
        name: "风控",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "风控",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__支付",
        schema_defs: ["EntityItem"],
        name: "支付",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "支付",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__保险",
        schema_defs: ["EntityItem"],
        name: "保险",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "保险",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__理财",
        schema_defs: ["EntityItem"],
        name: "理财",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "理财",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__虚拟货币",
        schema_defs: ["EntityItem"],
        name: "虚拟货币",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "虚拟货币",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__信用及征信",
        schema_defs: ["EntityItem"],
        name: "信用及征信",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "信用及征信",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__投融资",
        schema_defs: ["EntityItem"],
        name: "投融资",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "投融资",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__外汇期货贵金属",
        schema_defs: ["EntityItem"],
        name: "外汇期货贵金属",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "外汇期货贵金属",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__金融服务",
        schema_defs: ["EntityItem"],
        name: "金融服务",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "金融服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__彩票",
        schema_defs: ["EntityItem"],
        name: "彩票",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "彩票",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__房产金融",
        schema_defs: ["EntityItem"],
        name: "房产金融",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "房产金融",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__三农金融",
        schema_defs: ["EntityItem"],
        name: "三农金融",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "三农金融",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__汽车金融",
        schema_defs: ["EntityItem"],
        name: "汽车金融",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "汽车金融",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他金融",
        schema_defs: ["EntityItem"],
        name: "其他金融",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "classification_of_industry_code_list__金融",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他金融",
        leaf: false,
      },
    ],
    has_children: true,
    code: "金融",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__交通运输",
    schema_defs: ["EntityItem"],
    name: "交通运输",
    css_style: "plain",
    order_index: 3,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__交通出行",
        schema_defs: ["EntityItem"],
        name: "交通出行",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "交通出行",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__汽车服务",
        schema_defs: ["EntityItem"],
        name: "汽车服务",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "汽车服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__车联网及硬件",
        schema_defs: ["EntityItem"],
        name: "车联网及硬件",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "车联网及硬件",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__船运",
        schema_defs: ["EntityItem"],
        name: "船运",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "船运",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__物流",
        schema_defs: ["EntityItem"],
        name: "物流",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "物流",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他交通运输",
        schema_defs: ["EntityItem"],
        name: "其他交通运输",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__交通运输",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他交通运输",
        leaf: false,
      },
    ],
    has_children: true,
    code: "交通运输",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__医疗健康",
    schema_defs: ["EntityItem"],
    name: "医疗健康",
    css_style: "plain",
    order_index: 4,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__寻医诊疗",
        schema_defs: ["EntityItem"],
        name: "寻医诊疗",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "寻医诊疗",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__健康保健",
        schema_defs: ["EntityItem"],
        name: "健康保健",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "健康保健",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__医疗信息化",
        schema_defs: ["EntityItem"],
        name: "医疗信息化",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "医疗信息化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__医生服务",
        schema_defs: ["EntityItem"],
        name: "医生服务",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "医生服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__医疗器械及硬件",
        schema_defs: ["EntityItem"],
        name: "医疗器械及硬件",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "医疗器械及硬件",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__专科服务",
        schema_defs: ["EntityItem"],
        name: "专科服务",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "专科服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__医疗服务",
        schema_defs: ["EntityItem"],
        name: "医疗服务",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "医疗服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__生物技术和制药",
        schema_defs: ["EntityItem"],
        name: "生物技术和制药",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "生物技术和制药",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他医疗健康",
        schema_defs: ["EntityItem"],
        name: "其他医疗健康",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__医疗健康",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他医疗健康",
        leaf: false,
      },
    ],
    has_children: true,
    code: "医疗健康",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__旅游",
    schema_defs: ["EntityItem"],
    name: "旅游",
    css_style: "plain",
    order_index: 5,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__国内游",
        schema_defs: ["EntityItem"],
        name: "国内游",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "国内游",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__跨境游",
        schema_defs: ["EntityItem"],
        name: "跨境游",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "跨境游",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__旅游信息化",
        schema_defs: ["EntityItem"],
        name: "旅游信息化",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "旅游信息化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__景点门票",
        schema_defs: ["EntityItem"],
        name: "景点门票",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "景点门票",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__主题特色游",
        schema_defs: ["EntityItem"],
        name: "主题特色游",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "主题特色游",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__旅游服务",
        schema_defs: ["EntityItem"],
        name: "旅游服务",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "旅游服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__旅游工具及社区",
        schema_defs: ["EntityItem"],
        name: "旅游工具及社区",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "旅游工具及社区",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他旅游",
        schema_defs: ["EntityItem"],
        name: "其他旅游",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__旅游",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他旅游",
        leaf: false,
      },
    ],
    has_children: true,
    code: "旅游",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__本地生活",
    schema_defs: ["EntityItem"],
    name: "本地生活",
    css_style: "plain",
    order_index: 6,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__美食餐饮",
        schema_defs: ["EntityItem"],
        name: "美食餐饮",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "美食餐饮",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__家政服务",
        schema_defs: ["EntityItem"],
        name: "家政服务",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "家政服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__小区服务",
        schema_defs: ["EntityItem"],
        name: "小区服务",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "小区服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__美业服务",
        schema_defs: ["EntityItem"],
        name: "美业服务",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "美业服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__婚礼婚庆",
        schema_defs: ["EntityItem"],
        name: "婚礼婚庆",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "婚礼婚庆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__宠物服务",
        schema_defs: ["EntityItem"],
        name: "宠物服务",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "宠物服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__休闲娱乐",
        schema_defs: ["EntityItem"],
        name: "休闲娱乐",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "休闲娱乐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__维修服务",
        schema_defs: ["EntityItem"],
        name: "维修服务",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "维修服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__酒店",
        schema_defs: ["EntityItem"],
        name: "酒店",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "酒店",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__超市",
        schema_defs: ["EntityItem"],
        name: "超市",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "超市",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__生活服务",
        schema_defs: ["EntityItem"],
        name: "生活服务",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "生活服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__商户服务及信息化",
        schema_defs: ["EntityItem"],
        name: "商户服务及信息化",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "商户服务及信息化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__校园服务",
        schema_defs: ["EntityItem"],
        name: "校园服务",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "校园服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__体育运动",
        schema_defs: ["EntityItem"],
        name: "体育运动",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "体育运动",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他本地生活",
        schema_defs: ["EntityItem"],
        name: "其他本地生活",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "classification_of_industry_code_list__本地生活",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他本地生活",
        leaf: false,
      },
    ],
    has_children: true,
    code: "本地生活",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__营销",
    schema_defs: ["EntityItem"],
    name: "营销",
    css_style: "plain",
    order_index: 7,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__广告营销",
        schema_defs: ["EntityItem"],
        name: "广告营销",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "广告营销",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__移动及网络广告",
        schema_defs: ["EntityItem"],
        name: "移动及网络广告",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "移动及网络广告",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__传统广告",
        schema_defs: ["EntityItem"],
        name: "传统广告",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "传统广告",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__整合营销传播",
        schema_defs: ["EntityItem"],
        name: "整合营销传播",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "整合营销传播",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__广告平台",
        schema_defs: ["EntityItem"],
        name: "广告平台",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "广告平台",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他营销",
        schema_defs: ["EntityItem"],
        name: "其他营销",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__营销",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他营销",
        leaf: false,
      },
    ],
    has_children: true,
    code: "营销",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__文化娱乐",
    schema_defs: ["EntityItem"],
    name: "文化娱乐",
    css_style: "plain",
    order_index: 8,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__影视",
        schema_defs: ["EntityItem"],
        name: "影视",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "影视",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__动漫",
        schema_defs: ["EntityItem"],
        name: "动漫",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "动漫",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__艺术",
        schema_defs: ["EntityItem"],
        name: "艺术",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "艺术",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__设计及创意",
        schema_defs: ["EntityItem"],
        name: "设计及创意",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "设计及创意",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__媒体",
        schema_defs: ["EntityItem"],
        name: "媒体",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "媒体",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__音乐",
        schema_defs: ["EntityItem"],
        name: "音乐",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "音乐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__IP知识产权",
        schema_defs: ["EntityItem"],
        name: "IP知识产权",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "IP知识产权",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__视频",
        schema_defs: ["EntityItem"],
        name: "视频",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "视频",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__动漫及游戏周边服务",
        schema_defs: ["EntityItem"],
        name: "动漫及游戏周边服务",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "动漫及游戏周边服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__演艺",
        schema_defs: ["EntityItem"],
        name: "演艺",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "演艺",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__图片照片",
        schema_defs: ["EntityItem"],
        name: "图片照片",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "图片照片",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他文化娱乐",
        schema_defs: ["EntityItem"],
        name: "其他文化娱乐",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__文化娱乐",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他文化娱乐",
        leaf: false,
      },
    ],
    has_children: true,
    code: "文化娱乐",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__商业服务",
    schema_defs: ["EntityItem"],
    name: "商业服务",
    css_style: "plain",
    order_index: 9,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__企业服务",
        schema_defs: ["EntityItem"],
        name: "企业服务",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "企业服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__办公OA",
        schema_defs: ["EntityItem"],
        name: "办公OA",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "办公OA",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__人力资源",
        schema_defs: ["EntityItem"],
        name: "人力资源",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "人力资源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__法律服务",
        schema_defs: ["EntityItem"],
        name: "法律服务",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "法律服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__IT基础设施",
        schema_defs: ["EntityItem"],
        name: "IT基础设施",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "IT基础设施",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__行业信息化及解决方案",
        schema_defs: ["EntityItem"],
        name: "行业信息化及解决方案",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "行业信息化及解决方案",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__财务税务",
        schema_defs: ["EntityItem"],
        name: "财务税务",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "财务税务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__保洁服务",
        schema_defs: ["EntityItem"],
        name: "保洁服务",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "保洁服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__企业安全",
        schema_defs: ["EntityItem"],
        name: "企业安全",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "企业安全",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__数据服务",
        schema_defs: ["EntityItem"],
        name: "数据服务",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "数据服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__客户服务",
        schema_defs: ["EntityItem"],
        name: "客户服务",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "客户服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__电商解决方案",
        schema_defs: ["EntityItem"],
        name: "电商解决方案",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "电商解决方案",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__B2D开发者服务",
        schema_defs: ["EntityItem"],
        name: "B2D开发者服务",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "B2D开发者服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__外包",
        schema_defs: ["EntityItem"],
        name: "外包",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "外包",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__租赁",
        schema_defs: ["EntityItem"],
        name: "租赁",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "租赁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__黑科技及前沿技术",
        schema_defs: ["EntityItem"],
        name: "黑科技及前沿技术",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "黑科技及前沿技术",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他商业服务",
        schema_defs: ["EntityItem"],
        name: "其他商业服务",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "classification_of_industry_code_list__商业服务",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他商业服务",
        leaf: false,
      },
    ],
    has_children: true,
    code: "商业服务",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__农业",
    schema_defs: ["EntityItem"],
    name: "农业",
    css_style: "plain",
    order_index: 10,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__农业生物技术",
        schema_defs: ["EntityItem"],
        name: "农业生物技术",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "农业生物技术",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__新型农业系统",
        schema_defs: ["EntityItem"],
        name: "新型农业系统",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "新型农业系统",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__农机农资及装备",
        schema_defs: ["EntityItem"],
        name: "农机农资及装备",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "农机农资及装备",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__种植养殖",
        schema_defs: ["EntityItem"],
        name: "种植养殖",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "种植养殖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__农技服务",
        schema_defs: ["EntityItem"],
        name: "农技服务",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "农技服务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他农业",
        schema_defs: ["EntityItem"],
        name: "其他农业",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__农业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他农业",
        leaf: false,
      },
    ],
    has_children: true,
    code: "农业",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__公共事业",
    schema_defs: ["EntityItem"],
    name: "公共事业",
    css_style: "plain",
    order_index: 17,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__水务",
        schema_defs: ["EntityItem"],
        name: "水务",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "水务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__环保工程",
        schema_defs: ["EntityItem"],
        name: "环保工程",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "环保工程",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__热力",
        schema_defs: ["EntityItem"],
        name: "热力",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "热力",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__电力",
        schema_defs: ["EntityItem"],
        name: "电力",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "电力",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__燃气",
        schema_defs: ["EntityItem"],
        name: "燃气",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "燃气",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__污水处理",
        schema_defs: ["EntityItem"],
        name: "污水处理",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "污水处理",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__园林景观",
        schema_defs: ["EntityItem"],
        name: "园林景观",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "园林景观",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__动物保护",
        schema_defs: ["EntityItem"],
        name: "动物保护",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "动物保护",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他公共事业",
        schema_defs: ["EntityItem"],
        name: "其他公共事业",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "classification_of_industry_code_list__公共事业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他公共事业",
        leaf: false,
      },
    ],
    has_children: true,
    code: "公共事业",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__建筑业",
    schema_defs: ["EntityItem"],
    name: "建筑业",
    css_style: "plain",
    order_index: 18,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__基础建设",
        schema_defs: ["EntityItem"],
        name: "基础建设",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__建筑业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "基础建设",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__建筑工程",
        schema_defs: ["EntityItem"],
        name: "建筑工程",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__建筑业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "建筑工程",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他建筑业",
        schema_defs: ["EntityItem"],
        name: "其他建筑业",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__建筑业",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他建筑业",
        leaf: false,
      },
    ],
    has_children: true,
    code: "建筑业",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__商贸",
    schema_defs: ["EntityItem"],
    name: "商贸",
    css_style: "plain",
    order_index: 19,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__外贸",
        schema_defs: ["EntityItem"],
        name: "外贸",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "外贸",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__新零售",
        schema_defs: ["EntityItem"],
        name: "新零售",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "新零售",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__消费品",
        schema_defs: ["EntityItem"],
        name: "消费品",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "消费品",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__电子商务",
        schema_defs: ["EntityItem"],
        name: "电子商务",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "电子商务",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__批发/零售",
        schema_defs: ["EntityItem"],
        name: "批发/零售",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "批发/零售",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__其他商贸",
        schema_defs: ["EntityItem"],
        name: "其他商贸",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "classification_of_industry_code_list__商贸",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "其他商贸",
        leaf: false,
      },
    ],
    has_children: true,
    code: "商贸",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "classification_of_industry_code_list__不限",
    schema_defs: ["EntityItem"],
    name: "不限",
    css_style: "plain",
    order_index: 20,
    entity: {
      id: "classification_of_industry_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "classification_of_industry_code_list__不限C",
        schema_defs: ["EntityItem"],
        name: "不限",
        css_style: "plain",
        order_index: 0,
        parent: {
          id: "classification_of_industry_code_list__不限",
        },
        entity: {
          id: "classification_of_industry_code_list",
        },
        has_children: false,
        code: "不限",
        leaf: false,
      },
    ],
    has_children: true,
    code: "不限",
    leaf: true,
  },
];

export const LOCATION_LIST = [
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__热门城市",
    schema_defs: ["EntityItem"],
    name: "热门城市",
    css_style: "plain",
    order_index: 1,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北京",
        schema_defs: ["EntityItem"],
        name: "北京",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北京",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__上海",
        schema_defs: ["EntityItem"],
        name: "上海",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "上海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广州热门",
        schema_defs: ["EntityItem"],
        name: "广州",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__深圳热门",
        schema_defs: ["EntityItem"],
        name: "深圳",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "深圳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__杭州热门",
        schema_defs: ["EntityItem"],
        name: "杭州",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "杭州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__天津",
        schema_defs: ["EntityItem"],
        name: "天津",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "天津",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__西安热门",
        schema_defs: ["EntityItem"],
        name: "西安",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "西安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__苏州热门",
        schema_defs: ["EntityItem"],
        name: "苏州",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "苏州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武汉热门",
        schema_defs: ["EntityItem"],
        name: "武汉",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武汉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__厦门热门",
        schema_defs: ["EntityItem"],
        name: "厦门",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "厦门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长沙热门",
        schema_defs: ["EntityItem"],
        name: "长沙",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长沙",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__成都热门",
        schema_defs: ["EntityItem"],
        name: "成都",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "成都",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__重庆",
        schema_defs: ["EntityItem"],
        name: "重庆",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__热门城市",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "重庆",
        leaf: false,
      },
    ],
    has_children: true,
    code: "热门城市",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__河北省",
    schema_defs: ["EntityItem"],
    name: "河北省",
    css_style: "plain",
    order_index: 2,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__石家庄",
        schema_defs: ["EntityItem"],
        name: "石家庄",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "石家庄",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邯郸",
        schema_defs: ["EntityItem"],
        name: "邯郸",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邯郸",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__唐山",
        schema_defs: ["EntityItem"],
        name: "唐山",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "唐山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__保定",
        schema_defs: ["EntityItem"],
        name: "保定",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "保定",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__秦皇岛",
        schema_defs: ["EntityItem"],
        name: "秦皇岛",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "秦皇岛",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邢台",
        schema_defs: ["EntityItem"],
        name: "邢台",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邢台",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__张家口",
        schema_defs: ["EntityItem"],
        name: "张家口",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "张家口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__承德",
        schema_defs: ["EntityItem"],
        name: "承德",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "承德",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__沧州",
        schema_defs: ["EntityItem"],
        name: "沧州",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "沧州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__廊坊",
        schema_defs: ["EntityItem"],
        name: "廊坊",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "廊坊",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__衡水",
        schema_defs: ["EntityItem"],
        name: "衡水",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "衡水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__辛集",
        schema_defs: ["EntityItem"],
        name: "辛集",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "辛集",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__晋州",
        schema_defs: ["EntityItem"],
        name: "晋州",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "晋州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新乐",
        schema_defs: ["EntityItem"],
        name: "新乐",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新乐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__遵化",
        schema_defs: ["EntityItem"],
        name: "遵化",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "遵化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__迁安",
        schema_defs: ["EntityItem"],
        name: "迁安",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "迁安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__霸州",
        schema_defs: ["EntityItem"],
        name: "霸州",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "霸州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__三河",
        schema_defs: ["EntityItem"],
        name: "三河",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "三河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__定州",
        schema_defs: ["EntityItem"],
        name: "定州",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "定州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__涿州",
        schema_defs: ["EntityItem"],
        name: "涿州",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "涿州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安国",
        schema_defs: ["EntityItem"],
        name: "安国",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高碑店",
        schema_defs: ["EntityItem"],
        name: "高碑店",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高碑店",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泊头",
        schema_defs: ["EntityItem"],
        name: "泊头",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泊头",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__任丘",
        schema_defs: ["EntityItem"],
        name: "任丘",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "任丘",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黄骅",
        schema_defs: ["EntityItem"],
        name: "黄骅",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黄骅",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__河间",
        schema_defs: ["EntityItem"],
        name: "河间",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "河间",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__冀州",
        schema_defs: ["EntityItem"],
        name: "冀州",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "冀州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__深州",
        schema_defs: ["EntityItem"],
        name: "深州",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "深州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南宫",
        schema_defs: ["EntityItem"],
        name: "南宫",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南宫",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__沙河",
        schema_defs: ["EntityItem"],
        name: "沙河",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "沙河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武安",
        schema_defs: ["EntityItem"],
        name: "武安",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__河北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武安",
        leaf: false,
      },
    ],
    has_children: true,
    code: "河北省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__山西省",
    schema_defs: ["EntityItem"],
    name: "山西省",
    css_style: "plain",
    order_index: 3,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__太原",
        schema_defs: ["EntityItem"],
        name: "太原",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "太原",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大同",
        schema_defs: ["EntityItem"],
        name: "大同",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大同",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__朔州",
        schema_defs: ["EntityItem"],
        name: "朔州",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "朔州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阳泉",
        schema_defs: ["EntityItem"],
        name: "阳泉",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阳泉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长治",
        schema_defs: ["EntityItem"],
        name: "长治",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长治",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__晋城",
        schema_defs: ["EntityItem"],
        name: "晋城",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "晋城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__忻州",
        schema_defs: ["EntityItem"],
        name: "忻州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "忻州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吕梁",
        schema_defs: ["EntityItem"],
        name: "吕梁",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吕梁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__晋中",
        schema_defs: ["EntityItem"],
        name: "晋中",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "晋中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临汾",
        schema_defs: ["EntityItem"],
        name: "临汾",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临汾",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__运城",
        schema_defs: ["EntityItem"],
        name: "运城",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "运城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__古交",
        schema_defs: ["EntityItem"],
        name: "古交",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "古交",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__潞城",
        schema_defs: ["EntityItem"],
        name: "潞城",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "潞城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高平",
        schema_defs: ["EntityItem"],
        name: "高平",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__原平",
        schema_defs: ["EntityItem"],
        name: "原平",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "原平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__孝义",
        schema_defs: ["EntityItem"],
        name: "孝义",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "孝义",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汾阳",
        schema_defs: ["EntityItem"],
        name: "汾阳",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汾阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__介休",
        schema_defs: ["EntityItem"],
        name: "介休",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "介休",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__侯马",
        schema_defs: ["EntityItem"],
        name: "侯马",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "侯马",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__霍州",
        schema_defs: ["EntityItem"],
        name: "霍州",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "霍州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__永济",
        schema_defs: ["EntityItem"],
        name: "永济",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "永济",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__河津",
        schema_defs: ["EntityItem"],
        name: "河津",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__山西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "河津",
        leaf: false,
      },
    ],
    has_children: true,
    code: "山西省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__内蒙古自治区",
    schema_defs: ["EntityItem"],
    name: "内蒙古自治区",
    css_style: "plain",
    order_index: 4,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__呼和浩特",
        schema_defs: ["EntityItem"],
        name: "呼和浩特",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "呼和浩特",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__包头",
        schema_defs: ["EntityItem"],
        name: "包头",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "包头",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乌海",
        schema_defs: ["EntityItem"],
        name: "乌海",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乌海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__赤峰",
        schema_defs: ["EntityItem"],
        name: "赤峰",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "赤峰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__呼伦贝尔",
        schema_defs: ["EntityItem"],
        name: "呼伦贝尔",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "呼伦贝尔",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__通辽",
        schema_defs: ["EntityItem"],
        name: "通辽",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "通辽",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乌兰察布",
        schema_defs: ["EntityItem"],
        name: "乌兰察布",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乌兰察布",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鄂尔多斯",
        schema_defs: ["EntityItem"],
        name: "鄂尔多斯",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鄂尔多斯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__巴彦淖尔",
        schema_defs: ["EntityItem"],
        name: "巴彦淖尔",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "巴彦淖尔",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__满洲里",
        schema_defs: ["EntityItem"],
        name: "满洲里",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "满洲里",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__扎兰屯",
        schema_defs: ["EntityItem"],
        name: "扎兰屯",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "扎兰屯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__牙克石",
        schema_defs: ["EntityItem"],
        name: "牙克石",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "牙克石",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__根河",
        schema_defs: ["EntityItem"],
        name: "根河",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "根河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__额尔古纳",
        schema_defs: ["EntityItem"],
        name: "额尔古纳",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "额尔古纳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乌兰浩特",
        schema_defs: ["EntityItem"],
        name: "乌兰浩特",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乌兰浩特",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿尔山",
        schema_defs: ["EntityItem"],
        name: "阿尔山",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿尔山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__霍林郭勒",
        schema_defs: ["EntityItem"],
        name: "霍林郭勒",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "霍林郭勒",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__锡林浩特",
        schema_defs: ["EntityItem"],
        name: "锡林浩特",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "锡林浩特",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__二连浩特",
        schema_defs: ["EntityItem"],
        name: "二连浩特",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "二连浩特",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丰镇",
        schema_defs: ["EntityItem"],
        name: "丰镇",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__内蒙古自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丰镇",
        leaf: false,
      },
    ],
    has_children: true,
    code: "内蒙古自治区",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__辽宁省",
    schema_defs: ["EntityItem"],
    name: "辽宁省",
    css_style: "plain",
    order_index: 5,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__沈阳",
        schema_defs: ["EntityItem"],
        name: "沈阳",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "沈阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大连",
        schema_defs: ["EntityItem"],
        name: "大连",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大连",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__朝阳",
        schema_defs: ["EntityItem"],
        name: "朝阳",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "朝阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阜新",
        schema_defs: ["EntityItem"],
        name: "阜新",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阜新",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铁岭",
        schema_defs: ["EntityItem"],
        name: "铁岭",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铁岭",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__抚顺",
        schema_defs: ["EntityItem"],
        name: "抚顺",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "抚顺",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__本溪",
        schema_defs: ["EntityItem"],
        name: "本溪",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "本溪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__辽阳",
        schema_defs: ["EntityItem"],
        name: "辽阳",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "辽阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鞍山",
        schema_defs: ["EntityItem"],
        name: "鞍山",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鞍山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丹东",
        schema_defs: ["EntityItem"],
        name: "丹东",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丹东",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__营口",
        schema_defs: ["EntityItem"],
        name: "营口",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "营口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__盘锦",
        schema_defs: ["EntityItem"],
        name: "盘锦",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "盘锦",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__锦州",
        schema_defs: ["EntityItem"],
        name: "锦州",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "锦州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__葫芦岛",
        schema_defs: ["EntityItem"],
        name: "葫芦岛",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "葫芦岛",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新民",
        schema_defs: ["EntityItem"],
        name: "新民",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新民",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__瓦房店",
        schema_defs: ["EntityItem"],
        name: "瓦房店",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "瓦房店",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__庄河",
        schema_defs: ["EntityItem"],
        name: "庄河",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "庄河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北票",
        schema_defs: ["EntityItem"],
        name: "北票",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北票",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__凌源",
        schema_defs: ["EntityItem"],
        name: "凌源",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "凌源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__调兵山",
        schema_defs: ["EntityItem"],
        name: "调兵山",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "调兵山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__开原",
        schema_defs: ["EntityItem"],
        name: "开原",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "开原",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__灯塔",
        schema_defs: ["EntityItem"],
        name: "灯塔",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "灯塔",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海城",
        schema_defs: ["EntityItem"],
        name: "海城",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__凤城",
        schema_defs: ["EntityItem"],
        name: "凤城",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "凤城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东港",
        schema_defs: ["EntityItem"],
        name: "东港",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东港",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大石桥",
        schema_defs: ["EntityItem"],
        name: "大石桥",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大石桥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__盖州",
        schema_defs: ["EntityItem"],
        name: "盖州",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "盖州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__凌海",
        schema_defs: ["EntityItem"],
        name: "凌海",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "凌海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北镇",
        schema_defs: ["EntityItem"],
        name: "北镇",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北镇",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兴城",
        schema_defs: ["EntityItem"],
        name: "兴城",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__辽宁省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兴城",
        leaf: false,
      },
    ],
    has_children: true,
    code: "辽宁省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__吉林省",
    schema_defs: ["EntityItem"],
    name: "吉林省",
    css_style: "plain",
    order_index: 6,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长春",
        schema_defs: ["EntityItem"],
        name: "长春",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长春",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吉林",
        schema_defs: ["EntityItem"],
        name: "吉林",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吉林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__白城",
        schema_defs: ["EntityItem"],
        name: "白城",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "白城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__松原",
        schema_defs: ["EntityItem"],
        name: "松原",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "松原",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__四平",
        schema_defs: ["EntityItem"],
        name: "四平",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "四平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__辽源",
        schema_defs: ["EntityItem"],
        name: "辽源",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "辽源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__通化",
        schema_defs: ["EntityItem"],
        name: "通化",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "通化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__白山",
        schema_defs: ["EntityItem"],
        name: "白山",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "白山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德惠",
        schema_defs: ["EntityItem"],
        name: "德惠",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德惠",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__榆树",
        schema_defs: ["EntityItem"],
        name: "榆树",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "榆树",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__磐石",
        schema_defs: ["EntityItem"],
        name: "磐石",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "磐石",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__蛟河",
        schema_defs: ["EntityItem"],
        name: "蛟河",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "蛟河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桦甸",
        schema_defs: ["EntityItem"],
        name: "桦甸",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桦甸",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__舒兰",
        schema_defs: ["EntityItem"],
        name: "舒兰",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "舒兰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__洮南",
        schema_defs: ["EntityItem"],
        name: "洮南",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "洮南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大安",
        schema_defs: ["EntityItem"],
        name: "大安",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__双辽",
        schema_defs: ["EntityItem"],
        name: "双辽",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "双辽",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__公主岭",
        schema_defs: ["EntityItem"],
        name: "公主岭",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "公主岭",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__梅河口",
        schema_defs: ["EntityItem"],
        name: "梅河口",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "梅河口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__集安",
        schema_defs: ["EntityItem"],
        name: "集安",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "集安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临江",
        schema_defs: ["EntityItem"],
        name: "临江",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__延吉",
        schema_defs: ["EntityItem"],
        name: "延吉",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "延吉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__图们",
        schema_defs: ["EntityItem"],
        name: "图们",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "图们",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__敦化",
        schema_defs: ["EntityItem"],
        name: "敦化",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "敦化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__珲春",
        schema_defs: ["EntityItem"],
        name: "珲春",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "珲春",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__龙井",
        schema_defs: ["EntityItem"],
        name: "龙井",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "龙井",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__和龙",
        schema_defs: ["EntityItem"],
        name: "和龙",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "和龙",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__扶余",
        schema_defs: ["EntityItem"],
        name: "扶余",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__吉林省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "扶余",
        leaf: false,
      },
    ],
    has_children: true,
    code: "吉林省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__黑龙江省",
    schema_defs: ["EntityItem"],
    name: "黑龙江省",
    css_style: "plain",
    order_index: 7,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__哈尔滨",
        schema_defs: ["EntityItem"],
        name: "哈尔滨",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "哈尔滨",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__齐齐哈尔",
        schema_defs: ["EntityItem"],
        name: "齐齐哈尔",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "齐齐哈尔",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黑河",
        schema_defs: ["EntityItem"],
        name: "黑河",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黑河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大庆",
        schema_defs: ["EntityItem"],
        name: "大庆",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大庆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__伊春",
        schema_defs: ["EntityItem"],
        name: "伊春",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "伊春",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鹤岗",
        schema_defs: ["EntityItem"],
        name: "鹤岗",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鹤岗",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__佳木斯",
        schema_defs: ["EntityItem"],
        name: "佳木斯",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "佳木斯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__双鸭山",
        schema_defs: ["EntityItem"],
        name: "双鸭山",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "双鸭山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__七台河",
        schema_defs: ["EntityItem"],
        name: "七台河",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "七台河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鸡西",
        schema_defs: ["EntityItem"],
        name: "鸡西",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鸡西",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__牡丹江",
        schema_defs: ["EntityItem"],
        name: "牡丹江",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "牡丹江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__绥化",
        schema_defs: ["EntityItem"],
        name: "绥化",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "绥化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__尚志",
        schema_defs: ["EntityItem"],
        name: "尚志",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "尚志",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__五常",
        schema_defs: ["EntityItem"],
        name: "五常",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "五常",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__讷河",
        schema_defs: ["EntityItem"],
        name: "讷河",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "讷河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北安",
        schema_defs: ["EntityItem"],
        name: "北安",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__五大连池",
        schema_defs: ["EntityItem"],
        name: "五大连池",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "五大连池",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铁力",
        schema_defs: ["EntityItem"],
        name: "铁力",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铁力",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__同江",
        schema_defs: ["EntityItem"],
        name: "同江",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "同江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__富锦",
        schema_defs: ["EntityItem"],
        name: "富锦",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "富锦",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__虎林",
        schema_defs: ["EntityItem"],
        name: "虎林",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "虎林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__密山",
        schema_defs: ["EntityItem"],
        name: "密山",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "密山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__绥芬河",
        schema_defs: ["EntityItem"],
        name: "绥芬河",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "绥芬河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海林",
        schema_defs: ["EntityItem"],
        name: "海林",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宁安",
        schema_defs: ["EntityItem"],
        name: "宁安",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宁安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安达",
        schema_defs: ["EntityItem"],
        name: "安达",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安达",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__肇东",
        schema_defs: ["EntityItem"],
        name: "肇东",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "肇东",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海伦",
        schema_defs: ["EntityItem"],
        name: "海伦",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海伦",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__穆棱",
        schema_defs: ["EntityItem"],
        name: "穆棱",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "穆棱",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东宁",
        schema_defs: ["EntityItem"],
        name: "东宁",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__抚远",
        schema_defs: ["EntityItem"],
        name: "抚远",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__黑龙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "抚远",
        leaf: false,
      },
    ],
    has_children: true,
    code: "黑龙江省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__江苏省",
    schema_defs: ["EntityItem"],
    name: "江苏省",
    css_style: "plain",
    order_index: 8,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南京",
        schema_defs: ["EntityItem"],
        name: "南京",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南京",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__徐州",
        schema_defs: ["EntityItem"],
        name: "徐州",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "徐州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__连云港",
        schema_defs: ["EntityItem"],
        name: "连云港",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "连云港",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宿迁",
        schema_defs: ["EntityItem"],
        name: "宿迁",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宿迁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__淮安",
        schema_defs: ["EntityItem"],
        name: "淮安",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "淮安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__盐城",
        schema_defs: ["EntityItem"],
        name: "盐城",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "盐城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__扬州",
        schema_defs: ["EntityItem"],
        name: "扬州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "扬州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泰州",
        schema_defs: ["EntityItem"],
        name: "泰州",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泰州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南通",
        schema_defs: ["EntityItem"],
        name: "南通",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南通",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__镇江",
        schema_defs: ["EntityItem"],
        name: "镇江",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "镇江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__常州",
        schema_defs: ["EntityItem"],
        name: "常州",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "常州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__无锡",
        schema_defs: ["EntityItem"],
        name: "无锡",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "无锡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__苏州",
        schema_defs: ["EntityItem"],
        name: "苏州",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "苏州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__常熟",
        schema_defs: ["EntityItem"],
        name: "常熟",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "常熟",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__张家港",
        schema_defs: ["EntityItem"],
        name: "张家港",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "张家港",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__太仓",
        schema_defs: ["EntityItem"],
        name: "太仓",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "太仓",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昆山",
        schema_defs: ["EntityItem"],
        name: "昆山",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昆山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__江阴",
        schema_defs: ["EntityItem"],
        name: "江阴",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "江阴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜兴",
        schema_defs: ["EntityItem"],
        name: "宜兴",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__溧阳",
        schema_defs: ["EntityItem"],
        name: "溧阳",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "溧阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__扬中",
        schema_defs: ["EntityItem"],
        name: "扬中",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "扬中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__句容",
        schema_defs: ["EntityItem"],
        name: "句容",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "句容",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丹阳",
        schema_defs: ["EntityItem"],
        name: "丹阳",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丹阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__如皋",
        schema_defs: ["EntityItem"],
        name: "如皋",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "如皋",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海门",
        schema_defs: ["EntityItem"],
        name: "海门",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__启东",
        schema_defs: ["EntityItem"],
        name: "启东",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "启东",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高邮",
        schema_defs: ["EntityItem"],
        name: "高邮",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高邮",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__仪征",
        schema_defs: ["EntityItem"],
        name: "仪征",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "仪征",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兴化",
        schema_defs: ["EntityItem"],
        name: "兴化",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兴化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泰兴",
        schema_defs: ["EntityItem"],
        name: "泰兴",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泰兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__靖江",
        schema_defs: ["EntityItem"],
        name: "靖江",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "靖江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东台",
        schema_defs: ["EntityItem"],
        name: "东台",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东台",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邳州",
        schema_defs: ["EntityItem"],
        name: "邳州",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邳州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新沂",
        schema_defs: ["EntityItem"],
        name: "新沂",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__江苏省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新沂",
        leaf: false,
      },
    ],
    has_children: true,
    code: "江苏省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__浙江省",
    schema_defs: ["EntityItem"],
    name: "浙江省",
    css_style: "plain",
    order_index: 9,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__杭州",
        schema_defs: ["EntityItem"],
        name: "杭州",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "杭州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宁波",
        schema_defs: ["EntityItem"],
        name: "宁波",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宁波",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__湖州",
        schema_defs: ["EntityItem"],
        name: "湖州",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "湖州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__嘉兴",
        schema_defs: ["EntityItem"],
        name: "嘉兴",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "嘉兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__舟山",
        schema_defs: ["EntityItem"],
        name: "舟山",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "舟山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__绍兴",
        schema_defs: ["EntityItem"],
        name: "绍兴",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "绍兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__衢州",
        schema_defs: ["EntityItem"],
        name: "衢州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "衢州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__金华",
        schema_defs: ["EntityItem"],
        name: "金华",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "金华",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__台州",
        schema_defs: ["EntityItem"],
        name: "台州",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "台州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__温州",
        schema_defs: ["EntityItem"],
        name: "温州",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "温州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丽水",
        schema_defs: ["EntityItem"],
        name: "丽水",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丽水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临安",
        schema_defs: ["EntityItem"],
        name: "临安",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__建德",
        schema_defs: ["EntityItem"],
        name: "建德",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "建德",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__慈溪",
        schema_defs: ["EntityItem"],
        name: "慈溪",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "慈溪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__余姚",
        schema_defs: ["EntityItem"],
        name: "余姚",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "余姚",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__平湖",
        schema_defs: ["EntityItem"],
        name: "平湖",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "平湖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海宁",
        schema_defs: ["EntityItem"],
        name: "海宁",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桐乡",
        schema_defs: ["EntityItem"],
        name: "桐乡",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桐乡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__诸暨",
        schema_defs: ["EntityItem"],
        name: "诸暨",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "诸暨",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__嵊州",
        schema_defs: ["EntityItem"],
        name: "嵊州",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "嵊州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__江山",
        schema_defs: ["EntityItem"],
        name: "江山",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "江山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兰溪",
        schema_defs: ["EntityItem"],
        name: "兰溪",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兰溪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__永康",
        schema_defs: ["EntityItem"],
        name: "永康",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "永康",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__义乌",
        schema_defs: ["EntityItem"],
        name: "义乌",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "义乌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东阳",
        schema_defs: ["EntityItem"],
        name: "东阳",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临海",
        schema_defs: ["EntityItem"],
        name: "临海",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__温岭",
        schema_defs: ["EntityItem"],
        name: "温岭",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "温岭",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__瑞安",
        schema_defs: ["EntityItem"],
        name: "瑞安",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "瑞安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乐清",
        schema_defs: ["EntityItem"],
        name: "乐清",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乐清",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__龙泉",
        schema_defs: ["EntityItem"],
        name: "龙泉",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__浙江省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "龙泉",
        leaf: false,
      },
    ],
    has_children: true,
    code: "浙江省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__安徽省",
    schema_defs: ["EntityItem"],
    name: "安徽省",
    css_style: "plain",
    order_index: 10,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__合肥",
        schema_defs: ["EntityItem"],
        name: "合肥",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "合肥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__芜湖",
        schema_defs: ["EntityItem"],
        name: "芜湖",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "芜湖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__蚌埠",
        schema_defs: ["EntityItem"],
        name: "蚌埠",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "蚌埠",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__淮南",
        schema_defs: ["EntityItem"],
        name: "淮南",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "淮南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__马鞍山",
        schema_defs: ["EntityItem"],
        name: "马鞍山",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "马鞍山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__淮北",
        schema_defs: ["EntityItem"],
        name: "淮北",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "淮北",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铜陵",
        schema_defs: ["EntityItem"],
        name: "铜陵",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铜陵",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安庆",
        schema_defs: ["EntityItem"],
        name: "安庆",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安庆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黄山",
        schema_defs: ["EntityItem"],
        name: "黄山",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黄山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__滁州",
        schema_defs: ["EntityItem"],
        name: "滁州",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "滁州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阜阳",
        schema_defs: ["EntityItem"],
        name: "阜阳",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阜阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宿州",
        schema_defs: ["EntityItem"],
        name: "宿州",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宿州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__六安",
        schema_defs: ["EntityItem"],
        name: "六安",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "六安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__亳州",
        schema_defs: ["EntityItem"],
        name: "亳州",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "亳州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__池州",
        schema_defs: ["EntityItem"],
        name: "池州",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "池州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宣城",
        schema_defs: ["EntityItem"],
        name: "宣城",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宣城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__巢湖",
        schema_defs: ["EntityItem"],
        name: "巢湖",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "巢湖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桐城",
        schema_defs: ["EntityItem"],
        name: "桐城",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桐城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__天长",
        schema_defs: ["EntityItem"],
        name: "天长",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "天长",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__明光",
        schema_defs: ["EntityItem"],
        name: "明光",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "明光",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__界首",
        schema_defs: ["EntityItem"],
        name: "界首",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "界首",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宁国",
        schema_defs: ["EntityItem"],
        name: "宁国",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__安徽省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宁国",
        leaf: false,
      },
    ],
    has_children: true,
    code: "安徽省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__福建省",
    schema_defs: ["EntityItem"],
    name: "福建省",
    css_style: "plain",
    order_index: 11,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__厦门",
        schema_defs: ["EntityItem"],
        name: "厦门",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "厦门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__福州",
        schema_defs: ["EntityItem"],
        name: "福州",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "福州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南平",
        schema_defs: ["EntityItem"],
        name: "南平",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__三明",
        schema_defs: ["EntityItem"],
        name: "三明",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "三明",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__莆田",
        schema_defs: ["EntityItem"],
        name: "莆田",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "莆田",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泉州",
        schema_defs: ["EntityItem"],
        name: "泉州",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泉州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__漳州",
        schema_defs: ["EntityItem"],
        name: "漳州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "漳州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__龙岩",
        schema_defs: ["EntityItem"],
        name: "龙岩",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "龙岩",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宁德",
        schema_defs: ["EntityItem"],
        name: "宁德",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宁德",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__福清",
        schema_defs: ["EntityItem"],
        name: "福清",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "福清",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长乐",
        schema_defs: ["EntityItem"],
        name: "长乐",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长乐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邵武",
        schema_defs: ["EntityItem"],
        name: "邵武",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邵武",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武夷山",
        schema_defs: ["EntityItem"],
        name: "武夷山",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武夷山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__建瓯",
        schema_defs: ["EntityItem"],
        name: "建瓯",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "建瓯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__永安",
        schema_defs: ["EntityItem"],
        name: "永安",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "永安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__石狮",
        schema_defs: ["EntityItem"],
        name: "石狮",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "石狮",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__晋江",
        schema_defs: ["EntityItem"],
        name: "晋江",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "晋江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南安",
        schema_defs: ["EntityItem"],
        name: "南安",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__龙海",
        schema_defs: ["EntityItem"],
        name: "龙海",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "龙海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__漳平",
        schema_defs: ["EntityItem"],
        name: "漳平",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "漳平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__福安",
        schema_defs: ["EntityItem"],
        name: "福安",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "福安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__福鼎",
        schema_defs: ["EntityItem"],
        name: "福鼎",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__福建省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "福鼎",
        leaf: false,
      },
    ],
    has_children: true,
    code: "福建省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__江西省",
    schema_defs: ["EntityItem"],
    name: "江西省",
    css_style: "plain",
    order_index: 12,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南昌",
        schema_defs: ["EntityItem"],
        name: "南昌",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__九江",
        schema_defs: ["EntityItem"],
        name: "九江",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "九江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__景德镇",
        schema_defs: ["EntityItem"],
        name: "景德镇",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "景德镇",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鹰潭",
        schema_defs: ["EntityItem"],
        name: "鹰潭",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鹰潭",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新余",
        schema_defs: ["EntityItem"],
        name: "新余",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新余",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__萍乡",
        schema_defs: ["EntityItem"],
        name: "萍乡",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "萍乡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__赣州",
        schema_defs: ["EntityItem"],
        name: "赣州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "赣州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__上饶",
        schema_defs: ["EntityItem"],
        name: "上饶",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "上饶",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__抚州",
        schema_defs: ["EntityItem"],
        name: "抚州",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "抚州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜春",
        schema_defs: ["EntityItem"],
        name: "宜春",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜春",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吉安",
        schema_defs: ["EntityItem"],
        name: "吉安",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吉安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__瑞昌",
        schema_defs: ["EntityItem"],
        name: "瑞昌",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "瑞昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__共青城",
        schema_defs: ["EntityItem"],
        name: "共青城",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "共青城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乐平",
        schema_defs: ["EntityItem"],
        name: "乐平",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乐平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__瑞金",
        schema_defs: ["EntityItem"],
        name: "瑞金",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "瑞金",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德兴",
        schema_defs: ["EntityItem"],
        name: "德兴",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丰城",
        schema_defs: ["EntityItem"],
        name: "丰城",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丰城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__樟树",
        schema_defs: ["EntityItem"],
        name: "樟树",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "樟树",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高安",
        schema_defs: ["EntityItem"],
        name: "高安",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__井冈山",
        schema_defs: ["EntityItem"],
        name: "井冈山",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "井冈山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__贵溪",
        schema_defs: ["EntityItem"],
        name: "贵溪",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__江西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "贵溪",
        leaf: false,
      },
    ],
    has_children: true,
    code: "江西省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__山东省",
    schema_defs: ["EntityItem"],
    name: "山东省",
    css_style: "plain",
    order_index: 13,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__济南",
        schema_defs: ["EntityItem"],
        name: "济南",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "济南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__青岛",
        schema_defs: ["EntityItem"],
        name: "青岛",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "青岛",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__聊城",
        schema_defs: ["EntityItem"],
        name: "聊城",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "聊城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德州",
        schema_defs: ["EntityItem"],
        name: "德州",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东营",
        schema_defs: ["EntityItem"],
        name: "东营",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东营",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__淄博",
        schema_defs: ["EntityItem"],
        name: "淄博",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "淄博",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__潍坊",
        schema_defs: ["EntityItem"],
        name: "潍坊",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "潍坊",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__烟台",
        schema_defs: ["EntityItem"],
        name: "烟台",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "烟台",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__威海",
        schema_defs: ["EntityItem"],
        name: "威海",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "威海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__日照",
        schema_defs: ["EntityItem"],
        name: "日照",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "日照",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临沂",
        schema_defs: ["EntityItem"],
        name: "临沂",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临沂",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__枣庄",
        schema_defs: ["EntityItem"],
        name: "枣庄",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "枣庄",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__济宁",
        schema_defs: ["EntityItem"],
        name: "济宁",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "济宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泰安",
        schema_defs: ["EntityItem"],
        name: "泰安",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泰安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__莱芜",
        schema_defs: ["EntityItem"],
        name: "莱芜",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "莱芜",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__滨州",
        schema_defs: ["EntityItem"],
        name: "滨州",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "滨州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__菏泽",
        schema_defs: ["EntityItem"],
        name: "菏泽",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "菏泽",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__胶州",
        schema_defs: ["EntityItem"],
        name: "胶州",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "胶州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__即墨",
        schema_defs: ["EntityItem"],
        name: "即墨",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "即墨",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__平度",
        schema_defs: ["EntityItem"],
        name: "平度",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "平度",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__莱西",
        schema_defs: ["EntityItem"],
        name: "莱西",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "莱西",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临清",
        schema_defs: ["EntityItem"],
        name: "临清",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临清",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乐陵",
        schema_defs: ["EntityItem"],
        name: "乐陵",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乐陵",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__禹城",
        schema_defs: ["EntityItem"],
        name: "禹城",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "禹城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安丘",
        schema_defs: ["EntityItem"],
        name: "安丘",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安丘",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昌邑",
        schema_defs: ["EntityItem"],
        name: "昌邑",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昌邑",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高密",
        schema_defs: ["EntityItem"],
        name: "高密",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高密",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__青州",
        schema_defs: ["EntityItem"],
        name: "青州",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "青州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__诸城",
        schema_defs: ["EntityItem"],
        name: "诸城",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "诸城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__寿光",
        schema_defs: ["EntityItem"],
        name: "寿光",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "寿光",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__栖霞",
        schema_defs: ["EntityItem"],
        name: "栖霞",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "栖霞",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海阳",
        schema_defs: ["EntityItem"],
        name: "海阳",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__龙口",
        schema_defs: ["EntityItem"],
        name: "龙口",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "龙口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__莱阳",
        schema_defs: ["EntityItem"],
        name: "莱阳",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "莱阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__莱州",
        schema_defs: ["EntityItem"],
        name: "莱州",
        css_style: "plain",
        order_index: 35,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "莱州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__蓬莱",
        schema_defs: ["EntityItem"],
        name: "蓬莱",
        css_style: "plain",
        order_index: 36,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "蓬莱",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__招远",
        schema_defs: ["EntityItem"],
        name: "招远",
        css_style: "plain",
        order_index: 37,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "招远",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__荣成",
        schema_defs: ["EntityItem"],
        name: "荣成",
        css_style: "plain",
        order_index: 38,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "荣成",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乳山",
        schema_defs: ["EntityItem"],
        name: "乳山",
        css_style: "plain",
        order_index: 39,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乳山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__滕州",
        schema_defs: ["EntityItem"],
        name: "滕州",
        css_style: "plain",
        order_index: 40,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "滕州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__曲阜",
        schema_defs: ["EntityItem"],
        name: "曲阜",
        css_style: "plain",
        order_index: 41,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "曲阜",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邹城",
        schema_defs: ["EntityItem"],
        name: "邹城",
        css_style: "plain",
        order_index: 42,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邹城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新泰",
        schema_defs: ["EntityItem"],
        name: "新泰",
        css_style: "plain",
        order_index: 43,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新泰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__肥城",
        schema_defs: ["EntityItem"],
        name: "肥城",
        css_style: "plain",
        order_index: 44,
        parent: {
          id: "location_code_list__山东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "肥城",
        leaf: false,
      },
    ],
    has_children: true,
    code: "山东省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__河南省",
    schema_defs: ["EntityItem"],
    name: "河南省",
    css_style: "plain",
    order_index: 14,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__郑州",
        schema_defs: ["EntityItem"],
        name: "郑州",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "郑州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__开封",
        schema_defs: ["EntityItem"],
        name: "开封",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "开封",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__洛阳",
        schema_defs: ["EntityItem"],
        name: "洛阳",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "洛阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__平顶山",
        schema_defs: ["EntityItem"],
        name: "平顶山",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "平顶山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安阳",
        schema_defs: ["EntityItem"],
        name: "安阳",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鹤壁",
        schema_defs: ["EntityItem"],
        name: "鹤壁",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鹤壁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新乡",
        schema_defs: ["EntityItem"],
        name: "新乡",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新乡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__焦作",
        schema_defs: ["EntityItem"],
        name: "焦作",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "焦作",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__濮阳",
        schema_defs: ["EntityItem"],
        name: "濮阳",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "濮阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__许昌",
        schema_defs: ["EntityItem"],
        name: "许昌",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "许昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__漯河",
        schema_defs: ["EntityItem"],
        name: "漯河",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "漯河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__三门峡",
        schema_defs: ["EntityItem"],
        name: "三门峡",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "三门峡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南阳",
        schema_defs: ["EntityItem"],
        name: "南阳",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__商丘",
        schema_defs: ["EntityItem"],
        name: "商丘",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "商丘",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__周口",
        schema_defs: ["EntityItem"],
        name: "周口",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "周口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__驻马店",
        schema_defs: ["EntityItem"],
        name: "驻马店",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "驻马店",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__信阳",
        schema_defs: ["EntityItem"],
        name: "信阳",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "信阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__荥阳",
        schema_defs: ["EntityItem"],
        name: "荥阳",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "荥阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新郑",
        schema_defs: ["EntityItem"],
        name: "新郑",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新郑",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__登封",
        schema_defs: ["EntityItem"],
        name: "登封",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "登封",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新密",
        schema_defs: ["EntityItem"],
        name: "新密",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新密",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__偃师",
        schema_defs: ["EntityItem"],
        name: "偃师",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "偃师",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__孟州",
        schema_defs: ["EntityItem"],
        name: "孟州",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "孟州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__沁阳",
        schema_defs: ["EntityItem"],
        name: "沁阳",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "沁阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__卫辉",
        schema_defs: ["EntityItem"],
        name: "卫辉",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "卫辉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__辉县",
        schema_defs: ["EntityItem"],
        name: "辉县",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "辉县",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__林州",
        schema_defs: ["EntityItem"],
        name: "林州",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "林州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__禹州",
        schema_defs: ["EntityItem"],
        name: "禹州",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "禹州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长葛",
        schema_defs: ["EntityItem"],
        name: "长葛",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长葛",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__舞钢",
        schema_defs: ["EntityItem"],
        name: "舞钢",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "舞钢",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__义马",
        schema_defs: ["EntityItem"],
        name: "义马",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "义马",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__灵宝",
        schema_defs: ["EntityItem"],
        name: "灵宝",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "灵宝",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__项城",
        schema_defs: ["EntityItem"],
        name: "项城",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "项城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__巩义",
        schema_defs: ["EntityItem"],
        name: "巩义",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "巩义",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邓州",
        schema_defs: ["EntityItem"],
        name: "邓州",
        css_style: "plain",
        order_index: 35,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邓州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__永城",
        schema_defs: ["EntityItem"],
        name: "永城",
        css_style: "plain",
        order_index: 36,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "永城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汝州",
        schema_defs: ["EntityItem"],
        name: "汝州",
        css_style: "plain",
        order_index: 37,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汝州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__济源",
        schema_defs: ["EntityItem"],
        name: "济源",
        css_style: "plain",
        order_index: 38,
        parent: {
          id: "location_code_list__河南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "济源",
        leaf: false,
      },
    ],
    has_children: true,
    code: "河南省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__湖北省",
    schema_defs: ["EntityItem"],
    name: "湖北省",
    css_style: "plain",
    order_index: 15,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武汉",
        schema_defs: ["EntityItem"],
        name: "武汉",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武汉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__十堰",
        schema_defs: ["EntityItem"],
        name: "十堰",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "十堰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__襄阳",
        schema_defs: ["EntityItem"],
        name: "襄阳",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "襄阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__荆门",
        schema_defs: ["EntityItem"],
        name: "荆门",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "荆门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__孝感",
        schema_defs: ["EntityItem"],
        name: "孝感",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "孝感",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黄冈",
        schema_defs: ["EntityItem"],
        name: "黄冈",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黄冈",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鄂州",
        schema_defs: ["EntityItem"],
        name: "鄂州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鄂州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黄石",
        schema_defs: ["EntityItem"],
        name: "黄石",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黄石",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__咸宁",
        schema_defs: ["EntityItem"],
        name: "咸宁",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "咸宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__荆州",
        schema_defs: ["EntityItem"],
        name: "荆州",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "荆州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜昌",
        schema_defs: ["EntityItem"],
        name: "宜昌",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__随州",
        schema_defs: ["EntityItem"],
        name: "随州",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "随州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丹江口",
        schema_defs: ["EntityItem"],
        name: "丹江口",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丹江口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__老河口",
        schema_defs: ["EntityItem"],
        name: "老河口",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "老河口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__枣阳",
        schema_defs: ["EntityItem"],
        name: "枣阳",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "枣阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜城",
        schema_defs: ["EntityItem"],
        name: "宜城",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__钟祥",
        schema_defs: ["EntityItem"],
        name: "钟祥",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "钟祥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汉川",
        schema_defs: ["EntityItem"],
        name: "汉川",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汉川",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__应城",
        schema_defs: ["EntityItem"],
        name: "应城",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "应城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安陆",
        schema_defs: ["EntityItem"],
        name: "安陆",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安陆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广水",
        schema_defs: ["EntityItem"],
        name: "广水",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__麻城",
        schema_defs: ["EntityItem"],
        name: "麻城",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "麻城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武穴",
        schema_defs: ["EntityItem"],
        name: "武穴",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武穴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大冶",
        schema_defs: ["EntityItem"],
        name: "大冶",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大冶",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__赤壁",
        schema_defs: ["EntityItem"],
        name: "赤壁",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "赤壁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__石首",
        schema_defs: ["EntityItem"],
        name: "石首",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "石首",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__洪湖",
        schema_defs: ["EntityItem"],
        name: "洪湖",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "洪湖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__松滋",
        schema_defs: ["EntityItem"],
        name: "松滋",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "松滋",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜都",
        schema_defs: ["EntityItem"],
        name: "宜都",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜都",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__枝江",
        schema_defs: ["EntityItem"],
        name: "枝江",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "枝江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__当阳",
        schema_defs: ["EntityItem"],
        name: "当阳",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "当阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__恩施",
        schema_defs: ["EntityItem"],
        name: "恩施",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "恩施",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__利川",
        schema_defs: ["EntityItem"],
        name: "利川",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "利川",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__仙桃",
        schema_defs: ["EntityItem"],
        name: "仙桃",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "仙桃",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__天门",
        schema_defs: ["EntityItem"],
        name: "天门",
        css_style: "plain",
        order_index: 35,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "天门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__潜江",
        schema_defs: ["EntityItem"],
        name: "潜江",
        css_style: "plain",
        order_index: 36,
        parent: {
          id: "location_code_list__湖北省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "潜江",
        leaf: false,
      },
    ],
    has_children: true,
    code: "湖北省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__湖南省",
    schema_defs: ["EntityItem"],
    name: "湖南省",
    css_style: "plain",
    order_index: 16,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__长沙",
        schema_defs: ["EntityItem"],
        name: "长沙",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "长沙",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__衡阳",
        schema_defs: ["EntityItem"],
        name: "衡阳",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "衡阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__张家界",
        schema_defs: ["EntityItem"],
        name: "张家界",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "张家界",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__常德",
        schema_defs: ["EntityItem"],
        name: "常德",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "常德",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__益阳",
        schema_defs: ["EntityItem"],
        name: "益阳",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "益阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__岳阳",
        schema_defs: ["EntityItem"],
        name: "岳阳",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "岳阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__株洲",
        schema_defs: ["EntityItem"],
        name: "株洲",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "株洲",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__湘潭",
        schema_defs: ["EntityItem"],
        name: "湘潭",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "湘潭",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__郴州",
        schema_defs: ["EntityItem"],
        name: "郴州",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "郴州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__永州",
        schema_defs: ["EntityItem"],
        name: "永州",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "永州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邵阳",
        schema_defs: ["EntityItem"],
        name: "邵阳",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邵阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__怀化",
        schema_defs: ["EntityItem"],
        name: "怀化",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "怀化",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__娄底",
        schema_defs: ["EntityItem"],
        name: "娄底",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "娄底",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__耒阳",
        schema_defs: ["EntityItem"],
        name: "耒阳",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "耒阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__常宁",
        schema_defs: ["EntityItem"],
        name: "常宁",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "常宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__浏阳",
        schema_defs: ["EntityItem"],
        name: "浏阳",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "浏阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__津市",
        schema_defs: ["EntityItem"],
        name: "津市",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "津市",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__沅江",
        schema_defs: ["EntityItem"],
        name: "沅江",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "沅江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汨罗",
        schema_defs: ["EntityItem"],
        name: "汨罗",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汨罗",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临湘",
        schema_defs: ["EntityItem"],
        name: "临湘",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临湘",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__醴陵",
        schema_defs: ["EntityItem"],
        name: "醴陵",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "醴陵",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__湘乡",
        schema_defs: ["EntityItem"],
        name: "湘乡",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "湘乡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__韶山",
        schema_defs: ["EntityItem"],
        name: "韶山",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "韶山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__资兴",
        schema_defs: ["EntityItem"],
        name: "资兴",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "资兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武冈",
        schema_defs: ["EntityItem"],
        name: "武冈",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武冈",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__洪江",
        schema_defs: ["EntityItem"],
        name: "洪江",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "洪江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__冷水江",
        schema_defs: ["EntityItem"],
        name: "冷水江",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "冷水江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__涟源",
        schema_defs: ["EntityItem"],
        name: "涟源",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "涟源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吉首",
        schema_defs: ["EntityItem"],
        name: "吉首",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__湖南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吉首",
        leaf: false,
      },
    ],
    has_children: true,
    code: "湖南省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__广东省",
    schema_defs: ["EntityItem"],
    name: "广东省",
    css_style: "plain",
    order_index: 17,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广州",
        schema_defs: ["EntityItem"],
        name: "广州",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__深圳",
        schema_defs: ["EntityItem"],
        name: "深圳",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "深圳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__清远",
        schema_defs: ["EntityItem"],
        name: "清远",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "清远",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__韶关",
        schema_defs: ["EntityItem"],
        name: "韶关",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "韶关",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__河源",
        schema_defs: ["EntityItem"],
        name: "河源",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "河源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__梅州",
        schema_defs: ["EntityItem"],
        name: "梅州",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "梅州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__潮州",
        schema_defs: ["EntityItem"],
        name: "潮州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "潮州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汕头",
        schema_defs: ["EntityItem"],
        name: "汕头",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汕头",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__揭阳",
        schema_defs: ["EntityItem"],
        name: "揭阳",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "揭阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汕尾",
        schema_defs: ["EntityItem"],
        name: "汕尾",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汕尾",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__惠州",
        schema_defs: ["EntityItem"],
        name: "惠州",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "惠州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东莞",
        schema_defs: ["EntityItem"],
        name: "东莞",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东莞",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__珠海",
        schema_defs: ["EntityItem"],
        name: "珠海",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "珠海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__中山",
        schema_defs: ["EntityItem"],
        name: "中山",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "中山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__江门",
        schema_defs: ["EntityItem"],
        name: "江门",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "江门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__佛山",
        schema_defs: ["EntityItem"],
        name: "佛山",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "佛山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__肇庆",
        schema_defs: ["EntityItem"],
        name: "肇庆",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "肇庆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__云浮",
        schema_defs: ["EntityItem"],
        name: "云浮",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "云浮",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阳江",
        schema_defs: ["EntityItem"],
        name: "阳江",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阳江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__茂名",
        schema_defs: ["EntityItem"],
        name: "茂名",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "茂名",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__湛江",
        schema_defs: ["EntityItem"],
        name: "湛江",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "湛江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__英德",
        schema_defs: ["EntityItem"],
        name: "英德",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "英德",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__连州",
        schema_defs: ["EntityItem"],
        name: "连州",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "连州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乐昌",
        schema_defs: ["EntityItem"],
        name: "乐昌",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乐昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南雄",
        schema_defs: ["EntityItem"],
        name: "南雄",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南雄",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兴宁",
        schema_defs: ["EntityItem"],
        name: "兴宁",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兴宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__普宁",
        schema_defs: ["EntityItem"],
        name: "普宁",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "普宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__陆丰",
        schema_defs: ["EntityItem"],
        name: "陆丰",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "陆丰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__恩平",
        schema_defs: ["EntityItem"],
        name: "恩平",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "恩平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__台山",
        schema_defs: ["EntityItem"],
        name: "台山",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "台山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__开平",
        schema_defs: ["EntityItem"],
        name: "开平",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "开平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__鹤山",
        schema_defs: ["EntityItem"],
        name: "鹤山",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "鹤山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__四会",
        schema_defs: ["EntityItem"],
        name: "四会",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "四会",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__罗定",
        schema_defs: ["EntityItem"],
        name: "罗定",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "罗定",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阳春",
        schema_defs: ["EntityItem"],
        name: "阳春",
        css_style: "plain",
        order_index: 35,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阳春",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__化州",
        schema_defs: ["EntityItem"],
        name: "化州",
        css_style: "plain",
        order_index: 36,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "化州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__信宜",
        schema_defs: ["EntityItem"],
        name: "信宜",
        css_style: "plain",
        order_index: 37,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "信宜",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高州",
        schema_defs: ["EntityItem"],
        name: "高州",
        css_style: "plain",
        order_index: 38,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吴川",
        schema_defs: ["EntityItem"],
        name: "吴川",
        css_style: "plain",
        order_index: 39,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吴川",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__廉江",
        schema_defs: ["EntityItem"],
        name: "廉江",
        css_style: "plain",
        order_index: 40,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "廉江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__雷州",
        schema_defs: ["EntityItem"],
        name: "雷州",
        css_style: "plain",
        order_index: 41,
        parent: {
          id: "location_code_list__广东省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "雷州",
        leaf: false,
      },
    ],
    has_children: true,
    code: "广东省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__广西壮族自治区",
    schema_defs: ["EntityItem"],
    name: "广西壮族自治区",
    css_style: "plain",
    order_index: 18,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南宁",
        schema_defs: ["EntityItem"],
        name: "南宁",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桂林",
        schema_defs: ["EntityItem"],
        name: "桂林",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桂林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__柳州",
        schema_defs: ["EntityItem"],
        name: "柳州",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "柳州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__梧州",
        schema_defs: ["EntityItem"],
        name: "梧州",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "梧州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__贵港",
        schema_defs: ["EntityItem"],
        name: "贵港",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "贵港",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__玉林",
        schema_defs: ["EntityItem"],
        name: "玉林",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "玉林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__钦州",
        schema_defs: ["EntityItem"],
        name: "钦州",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "钦州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北海",
        schema_defs: ["EntityItem"],
        name: "北海",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__防城港",
        schema_defs: ["EntityItem"],
        name: "防城港",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "防城港",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__崇左",
        schema_defs: ["EntityItem"],
        name: "崇左",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "崇左",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__百色",
        schema_defs: ["EntityItem"],
        name: "百色",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "百色",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__河池",
        schema_defs: ["EntityItem"],
        name: "河池",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "河池",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__来宾",
        schema_defs: ["EntityItem"],
        name: "来宾",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "来宾",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__贺州",
        schema_defs: ["EntityItem"],
        name: "贺州",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "贺州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__岑溪",
        schema_defs: ["EntityItem"],
        name: "岑溪",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "岑溪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桂平",
        schema_defs: ["EntityItem"],
        name: "桂平",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桂平",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北流",
        schema_defs: ["EntityItem"],
        name: "北流",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北流",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东兴",
        schema_defs: ["EntityItem"],
        name: "东兴",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东兴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__凭祥",
        schema_defs: ["EntityItem"],
        name: "凭祥",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "凭祥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜州",
        schema_defs: ["EntityItem"],
        name: "宜州",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__合山",
        schema_defs: ["EntityItem"],
        name: "合山",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "合山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__靖西",
        schema_defs: ["EntityItem"],
        name: "靖西",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__广西壮族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "靖西",
        leaf: false,
      },
    ],
    has_children: true,
    code: "广西壮族自治区",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__海南省",
    schema_defs: ["EntityItem"],
    name: "海南省",
    css_style: "plain",
    order_index: 19,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海口",
        schema_defs: ["EntityItem"],
        name: "海口",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__三亚",
        schema_defs: ["EntityItem"],
        name: "三亚",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "三亚",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__三沙",
        schema_defs: ["EntityItem"],
        name: "三沙",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "三沙",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__儋州",
        schema_defs: ["EntityItem"],
        name: "儋州",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "儋州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__文昌",
        schema_defs: ["EntityItem"],
        name: "文昌",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "文昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__琼海",
        schema_defs: ["EntityItem"],
        name: "琼海",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "琼海",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__万宁",
        schema_defs: ["EntityItem"],
        name: "万宁",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "万宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__东方",
        schema_defs: ["EntityItem"],
        name: "东方",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "东方",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__五指山",
        schema_defs: ["EntityItem"],
        name: "五指山",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__海南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "五指山",
        leaf: false,
      },
    ],
    has_children: true,
    code: "海南省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__四川省",
    schema_defs: ["EntityItem"],
    name: "四川省",
    css_style: "plain",
    order_index: 20,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__成都",
        schema_defs: ["EntityItem"],
        name: "成都",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "成都",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广元",
        schema_defs: ["EntityItem"],
        name: "广元",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广元",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__绵阳",
        schema_defs: ["EntityItem"],
        name: "绵阳",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "绵阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德阳",
        schema_defs: ["EntityItem"],
        name: "德阳",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__南充",
        schema_defs: ["EntityItem"],
        name: "南充",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "南充",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广安",
        schema_defs: ["EntityItem"],
        name: "广安",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__遂宁",
        schema_defs: ["EntityItem"],
        name: "遂宁",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "遂宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__内江",
        schema_defs: ["EntityItem"],
        name: "内江",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "内江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乐山",
        schema_defs: ["EntityItem"],
        name: "乐山",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乐山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__自贡",
        schema_defs: ["EntityItem"],
        name: "自贡",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "自贡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泸州",
        schema_defs: ["EntityItem"],
        name: "泸州",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泸州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宜宾",
        schema_defs: ["EntityItem"],
        name: "宜宾",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宜宾",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__攀枝花",
        schema_defs: ["EntityItem"],
        name: "攀枝花",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "攀枝花",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__巴中",
        schema_defs: ["EntityItem"],
        name: "巴中",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "巴中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__达州",
        schema_defs: ["EntityItem"],
        name: "达州",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "达州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__资阳",
        schema_defs: ["EntityItem"],
        name: "资阳",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "资阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__眉山",
        schema_defs: ["EntityItem"],
        name: "眉山",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "眉山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__雅安",
        schema_defs: ["EntityItem"],
        name: "雅安",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "雅安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__崇州",
        schema_defs: ["EntityItem"],
        name: "崇州",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "崇州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__邛崃",
        schema_defs: ["EntityItem"],
        name: "邛崃",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "邛崃",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__都江堰",
        schema_defs: ["EntityItem"],
        name: "都江堰",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "都江堰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__彭州",
        schema_defs: ["EntityItem"],
        name: "彭州",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "彭州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__江油",
        schema_defs: ["EntityItem"],
        name: "江油",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "江油",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__什邡",
        schema_defs: ["EntityItem"],
        name: "什邡",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "什邡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__广汉",
        schema_defs: ["EntityItem"],
        name: "广汉",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "广汉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__绵竹",
        schema_defs: ["EntityItem"],
        name: "绵竹",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "绵竹",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阆中",
        schema_defs: ["EntityItem"],
        name: "阆中",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阆中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__华蓥",
        schema_defs: ["EntityItem"],
        name: "华蓥",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "华蓥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__峨眉山",
        schema_defs: ["EntityItem"],
        name: "峨眉山",
        css_style: "plain",
        order_index: 29,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "峨眉山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__万源",
        schema_defs: ["EntityItem"],
        name: "万源",
        css_style: "plain",
        order_index: 30,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "万源",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__简阳",
        schema_defs: ["EntityItem"],
        name: "简阳",
        css_style: "plain",
        order_index: 31,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "简阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__西昌",
        schema_defs: ["EntityItem"],
        name: "西昌",
        css_style: "plain",
        order_index: 32,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "西昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__康定",
        schema_defs: ["EntityItem"],
        name: "康定",
        css_style: "plain",
        order_index: 33,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "康定",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__马尔康",
        schema_defs: ["EntityItem"],
        name: "马尔康",
        css_style: "plain",
        order_index: 34,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "马尔康",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿坝",
        schema_defs: ["EntityItem"],
        name: "阿坝",
        css_style: "plain",
        order_index: 35,
        parent: {
          id: "location_code_list__四川省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿坝",
        leaf: false,
      },
    ],
    has_children: true,
    code: "四川省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__贵州省",
    schema_defs: ["EntityItem"],
    name: "贵州省",
    css_style: "plain",
    order_index: 21,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__贵阳",
        schema_defs: ["EntityItem"],
        name: "贵阳",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "贵阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__六盘水",
        schema_defs: ["EntityItem"],
        name: "六盘水",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "六盘水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__遵义",
        schema_defs: ["EntityItem"],
        name: "遵义",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "遵义",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安顺",
        schema_defs: ["EntityItem"],
        name: "安顺",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安顺",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__毕节",
        schema_defs: ["EntityItem"],
        name: "毕节",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "毕节",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铜仁",
        schema_defs: ["EntityItem"],
        name: "铜仁",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铜仁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__清镇",
        schema_defs: ["EntityItem"],
        name: "清镇",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "清镇",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__赤水",
        schema_defs: ["EntityItem"],
        name: "赤水",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "赤水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__仁怀",
        schema_defs: ["EntityItem"],
        name: "仁怀",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "仁怀",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__凯里",
        schema_defs: ["EntityItem"],
        name: "凯里",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "凯里",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__都匀",
        schema_defs: ["EntityItem"],
        name: "都匀",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "都匀",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兴义",
        schema_defs: ["EntityItem"],
        name: "兴义",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兴义",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__福泉",
        schema_defs: ["EntityItem"],
        name: "福泉",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "福泉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__黔南",
        schema_defs: ["EntityItem"],
        name: "黔南",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__贵州省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "黔南",
        leaf: false,
      },
    ],
    has_children: true,
    code: "贵州省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__云南省",
    schema_defs: ["EntityItem"],
    name: "云南省",
    css_style: "plain",
    order_index: 22,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昆明",
        schema_defs: ["EntityItem"],
        name: "昆明",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昆明",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__曲靖",
        schema_defs: ["EntityItem"],
        name: "曲靖",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "曲靖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__玉溪",
        schema_defs: ["EntityItem"],
        name: "玉溪",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "玉溪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__丽江",
        schema_defs: ["EntityItem"],
        name: "丽江",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "丽江",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昭通",
        schema_defs: ["EntityItem"],
        name: "昭通",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昭通",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__普洱",
        schema_defs: ["EntityItem"],
        name: "普洱",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "普洱",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临沧",
        schema_defs: ["EntityItem"],
        name: "临沧",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临沧",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__保山",
        schema_defs: ["EntityItem"],
        name: "保山",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "保山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安宁",
        schema_defs: ["EntityItem"],
        name: "安宁",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宣威",
        schema_defs: ["EntityItem"],
        name: "宣威",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宣威",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__芒",
        schema_defs: ["EntityItem"],
        name: "芒",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "芒",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__瑞丽",
        schema_defs: ["EntityItem"],
        name: "瑞丽",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "瑞丽",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__大理",
        schema_defs: ["EntityItem"],
        name: "大理",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "大理",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__楚雄",
        schema_defs: ["EntityItem"],
        name: "楚雄",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "楚雄",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__个旧",
        schema_defs: ["EntityItem"],
        name: "个旧",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "个旧",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__开远",
        schema_defs: ["EntityItem"],
        name: "开远",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "开远",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__蒙自",
        schema_defs: ["EntityItem"],
        name: "蒙自",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "蒙自",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__弥勒",
        schema_defs: ["EntityItem"],
        name: "弥勒",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "弥勒",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__景洪",
        schema_defs: ["EntityItem"],
        name: "景洪",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "景洪",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__文山",
        schema_defs: ["EntityItem"],
        name: "文山",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "文山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__香格里拉",
        schema_defs: ["EntityItem"],
        name: "香格里拉",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "香格里拉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__腾冲",
        schema_defs: ["EntityItem"],
        name: "腾冲",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__云南省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "腾冲",
        leaf: false,
      },
    ],
    has_children: true,
    code: "云南省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__陕西省",
    schema_defs: ["EntityItem"],
    name: "陕西省",
    css_style: "plain",
    order_index: 23,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__西安",
        schema_defs: ["EntityItem"],
        name: "西安",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "西安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__延安",
        schema_defs: ["EntityItem"],
        name: "延安",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "延安",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铜川",
        schema_defs: ["EntityItem"],
        name: "铜川",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铜川",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__渭南",
        schema_defs: ["EntityItem"],
        name: "渭南",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "渭南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__咸阳",
        schema_defs: ["EntityItem"],
        name: "咸阳",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "咸阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__宝鸡",
        schema_defs: ["EntityItem"],
        name: "宝鸡",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "宝鸡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__汉中",
        schema_defs: ["EntityItem"],
        name: "汉中",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "汉中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__榆林",
        schema_defs: ["EntityItem"],
        name: "榆林",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "榆林",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__商洛",
        schema_defs: ["EntityItem"],
        name: "商洛",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "商洛",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__安康",
        schema_defs: ["EntityItem"],
        name: "安康",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "安康",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__韩城",
        schema_defs: ["EntityItem"],
        name: "韩城",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "韩城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__华阴",
        schema_defs: ["EntityItem"],
        name: "华阴",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "华阴",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兴平",
        schema_defs: ["EntityItem"],
        name: "兴平",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__陕西省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兴平",
        leaf: false,
      },
    ],
    has_children: true,
    code: "陕西省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__甘肃省",
    schema_defs: ["EntityItem"],
    name: "甘肃省",
    css_style: "plain",
    order_index: 24,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__兰州",
        schema_defs: ["EntityItem"],
        name: "兰州",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "兰州",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__嘉峪关",
        schema_defs: ["EntityItem"],
        name: "嘉峪关",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "嘉峪关",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__金昌",
        schema_defs: ["EntityItem"],
        name: "金昌",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "金昌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__白银",
        schema_defs: ["EntityItem"],
        name: "白银",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "白银",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__天水",
        schema_defs: ["EntityItem"],
        name: "天水",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "天水",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__酒泉",
        schema_defs: ["EntityItem"],
        name: "酒泉",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "酒泉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__张掖",
        schema_defs: ["EntityItem"],
        name: "张掖",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "张掖",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__武威",
        schema_defs: ["EntityItem"],
        name: "武威",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "武威",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__庆阳",
        schema_defs: ["EntityItem"],
        name: "庆阳",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "庆阳",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__平凉",
        schema_defs: ["EntityItem"],
        name: "平凉",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "平凉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__定西",
        schema_defs: ["EntityItem"],
        name: "定西",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "定西",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__陇南",
        schema_defs: ["EntityItem"],
        name: "陇南",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "陇南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__玉门",
        schema_defs: ["EntityItem"],
        name: "玉门",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "玉门",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__敦煌",
        schema_defs: ["EntityItem"],
        name: "敦煌",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "敦煌",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__临夏",
        schema_defs: ["EntityItem"],
        name: "临夏",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "临夏",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__合作",
        schema_defs: ["EntityItem"],
        name: "合作",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__甘肃省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "合作",
        leaf: false,
      },
    ],
    has_children: true,
    code: "甘肃省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__青海省",
    schema_defs: ["EntityItem"],
    name: "青海省",
    css_style: "plain",
    order_index: 25,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__西宁",
        schema_defs: ["EntityItem"],
        name: "西宁",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__青海省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "西宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__海东",
        schema_defs: ["EntityItem"],
        name: "海东",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__青海省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "海东",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__格尔木",
        schema_defs: ["EntityItem"],
        name: "格尔木",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__青海省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "格尔木",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德令哈",
        schema_defs: ["EntityItem"],
        name: "德令哈",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__青海省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德令哈",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__玉树",
        schema_defs: ["EntityItem"],
        name: "玉树",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__青海省",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "玉树",
        leaf: false,
      },
    ],
    has_children: true,
    code: "青海省",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__西藏自治区",
    schema_defs: ["EntityItem"],
    name: "西藏自治区",
    css_style: "plain",
    order_index: 26,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__拉萨",
        schema_defs: ["EntityItem"],
        name: "拉萨",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__西藏自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "拉萨",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__日喀则",
        schema_defs: ["EntityItem"],
        name: "日喀则",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__西藏自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "日喀则",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昌都",
        schema_defs: ["EntityItem"],
        name: "昌都",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__西藏自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昌都",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__林芝",
        schema_defs: ["EntityItem"],
        name: "林芝",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__西藏自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "林芝",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__山南",
        schema_defs: ["EntityItem"],
        name: "山南",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__西藏自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "山南",
        leaf: false,
      },
    ],
    has_children: true,
    code: "西藏自治区",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__宁夏回族自治区",
    schema_defs: ["EntityItem"],
    name: "宁夏回族自治区",
    css_style: "plain",
    order_index: 27,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__银川",
        schema_defs: ["EntityItem"],
        name: "银川",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "银川",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__石嘴山",
        schema_defs: ["EntityItem"],
        name: "石嘴山",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "石嘴山",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吴忠",
        schema_defs: ["EntityItem"],
        name: "吴忠",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吴忠",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__中卫",
        schema_defs: ["EntityItem"],
        name: "中卫",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "中卫",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__固原",
        schema_defs: ["EntityItem"],
        name: "固原",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "固原",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__灵武",
        schema_defs: ["EntityItem"],
        name: "灵武",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "灵武",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__青铜峡",
        schema_defs: ["EntityItem"],
        name: "青铜峡",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__宁夏回族自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "青铜峡",
        leaf: false,
      },
    ],
    has_children: true,
    code: "宁夏回族自治区",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__新疆维吾尔自治区",
    schema_defs: ["EntityItem"],
    name: "新疆维吾尔自治区",
    css_style: "plain",
    order_index: 28,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乌鲁木齐",
        schema_defs: ["EntityItem"],
        name: "乌鲁木齐",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乌鲁木齐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__克拉玛依",
        schema_defs: ["EntityItem"],
        name: "克拉玛依",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "克拉玛依",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__吐鲁番",
        schema_defs: ["EntityItem"],
        name: "吐鲁番",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "吐鲁番",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__哈密",
        schema_defs: ["EntityItem"],
        name: "哈密",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "哈密",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__喀什",
        schema_defs: ["EntityItem"],
        name: "喀什",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "喀什",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿克苏",
        schema_defs: ["EntityItem"],
        name: "阿克苏",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿克苏",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__和田",
        schema_defs: ["EntityItem"],
        name: "和田",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "和田",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿图什",
        schema_defs: ["EntityItem"],
        name: "阿图什",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿图什",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿拉山口",
        schema_defs: ["EntityItem"],
        name: "阿拉山口",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿拉山口",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__博乐",
        schema_defs: ["EntityItem"],
        name: "博乐",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "博乐",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昌吉",
        schema_defs: ["EntityItem"],
        name: "昌吉",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昌吉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阜康",
        schema_defs: ["EntityItem"],
        name: "阜康",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阜康",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__库尔勒",
        schema_defs: ["EntityItem"],
        name: "库尔勒",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "库尔勒",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__伊宁",
        schema_defs: ["EntityItem"],
        name: "伊宁",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "伊宁",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__奎屯",
        schema_defs: ["EntityItem"],
        name: "奎屯",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "奎屯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__塔城",
        schema_defs: ["EntityItem"],
        name: "塔城",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "塔城",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__乌苏",
        schema_defs: ["EntityItem"],
        name: "乌苏",
        css_style: "plain",
        order_index: 17,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "乌苏",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿勒泰",
        schema_defs: ["EntityItem"],
        name: "阿勒泰",
        css_style: "plain",
        order_index: 18,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿勒泰",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__霍尔果斯",
        schema_defs: ["EntityItem"],
        name: "霍尔果斯",
        css_style: "plain",
        order_index: 19,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "霍尔果斯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__石河子",
        schema_defs: ["EntityItem"],
        name: "石河子",
        css_style: "plain",
        order_index: 20,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "石河子",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__阿拉尔",
        schema_defs: ["EntityItem"],
        name: "阿拉尔",
        css_style: "plain",
        order_index: 21,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "阿拉尔",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__图木舒克",
        schema_defs: ["EntityItem"],
        name: "图木舒克",
        css_style: "plain",
        order_index: 22,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "图木舒克",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__五家渠",
        schema_defs: ["EntityItem"],
        name: "五家渠",
        css_style: "plain",
        order_index: 23,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "五家渠",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__北屯",
        schema_defs: ["EntityItem"],
        name: "北屯",
        css_style: "plain",
        order_index: 24,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "北屯",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__铁门关",
        schema_defs: ["EntityItem"],
        name: "铁门关",
        css_style: "plain",
        order_index: 25,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "铁门关",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__双河",
        schema_defs: ["EntityItem"],
        name: "双河",
        css_style: "plain",
        order_index: 26,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "双河",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__可克达拉",
        schema_defs: ["EntityItem"],
        name: "可克达拉",
        css_style: "plain",
        order_index: 27,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "可克达拉",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__昆玉",
        schema_defs: ["EntityItem"],
        name: "昆玉",
        css_style: "plain",
        order_index: 28,
        parent: {
          id: "location_code_list__新疆维吾尔自治区",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "昆玉",
        leaf: false,
      },
    ],
    has_children: true,
    code: "新疆维吾尔自治区",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__台湾",
    schema_defs: ["EntityItem"],
    name: "台湾",
    css_style: "plain",
    order_index: 29,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__台北",
        schema_defs: ["EntityItem"],
        name: "台北",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "台北",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新北",
        schema_defs: ["EntityItem"],
        name: "新北",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新北",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__桃园",
        schema_defs: ["EntityItem"],
        name: "桃园",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "桃园",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__台中",
        schema_defs: ["EntityItem"],
        name: "台中",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "台中",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__台南",
        schema_defs: ["EntityItem"],
        name: "台南",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "台南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__高雄",
        schema_defs: ["EntityItem"],
        name: "高雄",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "高雄",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__基隆",
        schema_defs: ["EntityItem"],
        name: "基隆",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "基隆",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新竹",
        schema_defs: ["EntityItem"],
        name: "新竹",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新竹",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__嘉义",
        schema_defs: ["EntityItem"],
        name: "嘉义",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__台湾",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "嘉义",
        leaf: false,
      },
    ],
    has_children: true,
    code: "台湾",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__香港",
    schema_defs: ["EntityItem"],
    name: "香港",
    css_style: "plain",
    order_index: 30,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__香港特别行政区",
        schema_defs: ["EntityItem"],
        name: "香港特别行政区",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__香港",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "香港特别行政区",
        leaf: false,
      },
    ],
    has_children: true,
    code: "香港",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__澳门",
    schema_defs: ["EntityItem"],
    name: "澳门",
    css_style: "plain",
    order_index: 31,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__澳门特别行政区",
        schema_defs: ["EntityItem"],
        name: "澳门特别行政区",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__澳门",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "澳门特别行政区",
        leaf: false,
      },
    ],
    has_children: true,
    code: "澳门",
    leaf: true,
  },
  {
    disabled: false,
    builtin: true,
    id: "location_code_list__海外",
    schema_defs: ["EntityItem"],
    name: "海外",
    css_style: "plain",
    order_index: 32,
    entity: {
      id: "location_code_list",
    },
    children: [
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__美国",
        schema_defs: ["EntityItem"],
        name: "美国",
        css_style: "plain",
        order_index: 1,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "美国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__英国",
        schema_defs: ["EntityItem"],
        name: "英国",
        css_style: "plain",
        order_index: 2,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "英国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__法国",
        schema_defs: ["EntityItem"],
        name: "法国",
        css_style: "plain",
        order_index: 3,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "法国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__德国",
        schema_defs: ["EntityItem"],
        name: "德国",
        css_style: "plain",
        order_index: 4,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "德国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__意大利",
        schema_defs: ["EntityItem"],
        name: "意大利",
        css_style: "plain",
        order_index: 5,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "意大利",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__澳大利亚",
        schema_defs: ["EntityItem"],
        name: "澳大利亚",
        css_style: "plain",
        order_index: 6,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "澳大利亚",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__新加坡",
        schema_defs: ["EntityItem"],
        name: "新加坡",
        css_style: "plain",
        order_index: 7,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "新加坡",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__马来西亚",
        schema_defs: ["EntityItem"],
        name: "马来西亚",
        css_style: "plain",
        order_index: 8,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "马来西亚",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__日本",
        schema_defs: ["EntityItem"],
        name: "日本",
        css_style: "plain",
        order_index: 9,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "日本",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__韩国",
        schema_defs: ["EntityItem"],
        name: "韩国",
        css_style: "plain",
        order_index: 10,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "韩国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__巴西",
        schema_defs: ["EntityItem"],
        name: "巴西",
        css_style: "plain",
        order_index: 11,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "巴西",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__墨西哥",
        schema_defs: ["EntityItem"],
        name: "墨西哥",
        css_style: "plain",
        order_index: 12,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "墨西哥",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__印度",
        schema_defs: ["EntityItem"],
        name: "印度",
        css_style: "plain",
        order_index: 13,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "印度",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__越南",
        schema_defs: ["EntityItem"],
        name: "越南",
        css_style: "plain",
        order_index: 14,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "越南",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__泰国",
        schema_defs: ["EntityItem"],
        name: "泰国",
        css_style: "plain",
        order_index: 15,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "泰国",
        leaf: false,
      },
      {
        disabled: false,
        builtin: true,
        id: "location_code_list__俄罗斯",
        schema_defs: ["EntityItem"],
        name: "俄罗斯",
        css_style: "plain",
        order_index: 16,
        parent: {
          id: "location_code_list__海外",
        },
        entity: {
          id: "location_code_list",
        },
        has_children: false,
        code: "俄罗斯",
        leaf: false,
      },
    ],
    has_children: true,
    code: "海外",
    leaf: true,
  },
];
