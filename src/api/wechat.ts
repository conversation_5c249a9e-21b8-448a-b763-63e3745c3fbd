/*
 * @Author: sx <EMAIL>
 * @Date: 2022-10-26 12:29:49
 * @LastEditors: sx <EMAIL>
 * @LastEditTime: 2022-10-28 15:20:22
 * @FilePath: \itp-operation-web\src\api\wechat.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { useUserStore } from '@/store/user.store'

/**
 * 添加文章
 * */ 
export function setArticle(params = {}) {
  const userStore = useUserStore()

  return request.post(`/company/${userStore.companyId}/article`, { 
    ...params,
    companyUserId: userStore.id
  })
}

/**
 * 文章列表
 * */ 
export function getArticleList(params = {}) {
  const userStore = useUserStore()

  return request.get(`/company/${userStore.companyId}/article/list`, { params })
}

/**
 * 删除文章
 * */ 
 export function delArticle(articleId : number,params = {}) {
  const userStore = useUserStore()

  return request.delete(`/company/${userStore.companyId}/article/${articleId}`)
}

/**
 * 获取文章详情
 * */ 
 export function getArticle(articleId : any, params = {}) {
  const userStore = useUserStore()

  return request.get(`/company/${userStore.companyId}/article/${articleId}`)
}

/**
 * 更新文章
 * */ 
 export function editArticle(articleId : any, params = {}) {
  const userStore = useUserStore()

  return request.put(`/company/${userStore.companyId}/article/${articleId}`, {
    ...params,
    companyUserId: userStore.id
  })
}

/**
 * 显示文章
 * */ 
 export function showArticle(articleId : any, params = {}) {
  const userStore = useUserStore()

  return request.post(`/company/${userStore.companyId}/article/${articleId}/enable`)
}

/**
 * 隐藏文章
 * */ 
 export function hideArticle(articleId : any, params = {}) {
  const userStore = useUserStore()

  return request.post(`/company/${userStore.companyId}/article/${articleId}/disable`)
}