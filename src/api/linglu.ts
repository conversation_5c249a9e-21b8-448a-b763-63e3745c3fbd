import { useUserStore } from '@/store/user.store'
import request from '@/utils/request'

const lingluApi = {
  LingLuTuiRecommenderList: '/match/third/linglutui/recommenderList',
  LingLuTuiDeliverList: '/match/third/linglutui/deliverList',
  LingLuTuiCompanyList: '/customer/linglutui/list',
  LingLuTuiPositionList: '/position/linglutui/list',
  LingLuTuiMatchPosition: '/match/third/linglutui',
  LingLuPinDeliverList: '',
  LingLuPinCompanyList: '',
  LingLuPinPositionList: '',
  LingLuPinMatchPosition: ''
}

export function getLingLuTuiRecommenders (keyword:string) {
  return request({
    url: lingluApi.LingLuTuiRecommenderList,
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

export function getLingLuTuiDelivers (keyword:string) {
  return request({
    url: lingluApi.LingLuTuiDeliverList,
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

export function getLingLuTuiCompanies (keyword:string) {
  const userStore = useUserStore()
  return request({
    url: `/company/${userStore.companyId}/customer/linglutui/list`,
    method: 'get',
    params: {
      keyword: keyword
    }
  })
}

export function getLingLuTuiPositions (companyId:number, keyword:string) {
  const userStore = useUserStore()
  return request({
    url: `/company/${userStore.companyId}/position/linglutui/list`,
    method: 'get',
    params: {
      customerId: companyId,
      keyword: keyword
    }
  })
}

export function matchLingLuTuiPosition (recommenderId:number, talentId:number, positionId:number) {
  return request({
    url: lingluApi.LingLuTuiMatchPosition,
    method: 'post',
    data: {
      recommenderId: recommenderId,
      talentId: talentId,
      positionId: positionId
    }
  })
}
