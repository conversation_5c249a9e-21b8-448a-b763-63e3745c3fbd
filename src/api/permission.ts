import { useUserStore } from "@/store/user.store";
import request from '@/utils/request'

export function getCompanyPermissions() {
  const userStore = useUserStore()
  const companyId = userStore.companyId
  return request.get(
    `/company/${companyId}/permission/list`
  )
}

export function getCompanyRolePermissions(roleId: number) {
  const userStore = useUserStore()
  const companyId = userStore.companyId
  return request.get(
    `/company/${companyId}/permission/role/${roleId}`
  )
}

export function updateRolePermission(roleId: number, permissionIds: number[]) {
  const userStore = useUserStore()
  const companyId = userStore.companyId
  const params = {
    companyPermissionIds: permissionIds
  }
  return request.post(
    `/company/${companyId}/permission/role/${roleId}`,
    params
  )
}
