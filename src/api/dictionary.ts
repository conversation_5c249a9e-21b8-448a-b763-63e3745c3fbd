import { useMemoStore } from "@/store/memo.store";
import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";
import exp from 'constants'

const dictionaryApi = {
  DegreeList: "/talent/degree",
  WorkYears: "/talent/work/year",
  AllAreaList: "/area/allCity",
  AreaList: "/area/query",
  IndustryList: "/dict/industry/type/query",
  IndustryAllList: "/dict/industryNew/allIndustry",
  SchoolList: "/dict/school/search",
  FunctionList: "/dict/function/all",
  CompanyScaleList: "/dict/company/config/allScales",
  CompanyTypeList: "/dict/company/config/allTypes",
  CompanyFundingStageList: "/customer/invest/status",
  EmployeeStatusList: "/talent/dict/employeeStatus",
};

const momoStore = useMemoStore();

export async function getDegreeList() {
  const res = await request.get(dictionaryApi.DegreeList);
  return res;
}

export function getWorkYearList() {
  return request({
    url: dictionaryApi.WorkYears,
    method: "get",
  });
}

export function getAreaList() {
  return request({
    url: dictionaryApi.AreaList,
    method: "get",
    params: { parentId: 0 },
  });
}

export function getAllAreaList() {
  return request({
    url: dictionaryApi.AllAreaList,
    method: "get",
  });
}

export function getIndustryList() {
  return request({
    url: dictionaryApi.IndustryList,
    method: "get",
    params: { parentId: 0 },
  });
}

export function getAllIndustryList() {
  return request({
    url: dictionaryApi.IndustryAllList,
    method: "get",
  });
}

export function getAllFunctionList() {
  return request({
    url: dictionaryApi.FunctionList,
    method: "get",
  });
}

export function getCompanyScaleList() {
  return request({
    url: dictionaryApi.CompanyScaleList,
    method: "get",
  });
}

export function getCompanyTypeList() {
  return request({
    url: dictionaryApi.CompanyTypeList,
    method: "get",
  });
}

export function getEmployeeStatusList() {
  return request({
    url: dictionaryApi.EmployeeStatusList,
    method: "get",
  });
}

export function getCompanyFundingStageList() {
  const userStore = useUserStore();
  return request({
    url: `company/${userStore.companyId}/customer/invest/status`,
    method: "GET",
  });
}

export function getSchoolList(keyword: string, page: number, size: number) {
  return request({
    url: dictionaryApi.SchoolList,
    method: "get",
    params: {
      name: keyword,
      page: page,
      size: size,
    },
  });
}

export function getGenderList() {
  return Promise.resolve({
    data: [
      { title: "男", key: 1, title_en: "male" },
      { title: "女", key: 2, title_en: "female" },
      { title: "未知", key: 3, title_en: "unknown" },
    ],
  });
}

export function getSchoolLevel() {
  return Promise.resolve({
    data: [
      { title: "中学", id: 1 },
      { title: "大专", id: 2 },
      { title: "本科", id: 3 },
      { title: "研究生", id: 4 },
      { title: "继续教育", id: 5 },
    ],
  });
}

interface getChannelListParams {
  size: number;
  current: number;
}
export function getChannelList(params: getChannelListParams) {
  return Promise.resolve({
    data: [{ name: "CA推岗渠道", id: 1 }],
  });
}

const kinds = [
  { value: 1, label: "211" },
  { value: 2, label: "985" },
  { value: 4, label: "QS200" },
  { value: 8, label: "双一流" },
  { value: 16, label: "C9" },
];

const kindBitMap = new Map();
kinds.forEach((item, index) => {
  kindBitMap.set(index, item);
});

export function decodeSchoolKind(value: number) {
  const bits = value.toString(2).split("").reverse();
  const list: { value: number; label: string }[] = [];
  bits.forEach((item, index) => {
    if (item) list.push(kindBitMap.get(index));
  });
  return list;
}

export function getSchoolKind() {
  return Promise.resolve({ data: kinds });
}

interface addSchoolParam {
  areaId: number | null;
  ename: string | null;
  name: string | null;
  kind: number | null;
  level: number | null;
}

export function createSchool(params: addSchoolParam) {
  return request.post("/dict/school", params);
}

export function getJobStatus() {
  return Promise.resolve({
    data: [
      { value: 1, label: "进展中" },
      { value: 2, label: "已交付" },
      { value: 3, label: "职位暂停" },
      { value: 4, label: "职位调整" },
      { value: 5, label: "竞对交付" },
    ],
  });
}

export function getTimeQuantum() {
  return Promise.resolve({
    data: [
      { value: 1, label: "昨天" },
      { value: 2, label: "本周" },
      { value: 3, label: "上周" },
      { value: 4, label: "本月" },
      { value: 5, label: "上月" },
    ],
  });
}

export function getTalentSource() {
  return request.get("/talent/talentSource/list");
}

const memoStore = useMemoStore();

function ApiCache(key: string) {
  return (
    target: Object,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) => {
    const original = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      let result = memoStore.get(key);
      if (!result) {
        result = await original.apply(this, args);
        memoStore.set(key, result);
      }
      return result;
    };
  };
}

class Dictionary {
  @ApiCache("dictionary.employee_status")
  async getEmployeeStatus() {
    return await request.get(dictionaryApi.EmployeeStatusList);
  }

  @ApiCache("dictionary.all_area_dict")
  async getAllAreaList() {
    return await request(dictionaryApi.AllAreaList);
  }

  @ApiCache("dictionary.degree")
  async getDegree() {
    return await request.get(dictionaryApi.DegreeList);
  }
}

export const dictionary = new Dictionary();

// 获取货币列表
export function getCurrencyList() {
  return request({
    url: `finance/common/currency/list`,
    method: "GET",
  });
}

// 获取支付方式
export function getPayMethodList() {
  return request({
    url: `finance/common/payMethod/list`,
    method: "GET",
  });
}

export function getPerformanceRoleList() {
  return request({
    url: `finance/common/performance/role/list`,
    method: "GET",
  })
}

export function getInvoiceNameList() {
  return request({
    url: `finance/common/invoice/name/list`,
    method: "GET",
  })
}

export function getContractTypeList() {
  return request({
    url: `finance/common/contract/type/list`,
    method: "GET",
  })
}

export function getContractStatusList() {
  return request({
    url: `finance/common/contract/verifyStatus/list`,
    method: "GET",
  })
}

export function getPaymentMethodList() {
  return request({
    url: `finance/common/payMethod/list`,
    method: "GET",
  })
}