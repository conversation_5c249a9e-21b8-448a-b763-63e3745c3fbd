// import axios from "axios";
import request from "@/utils/request";

type TSearchBury = {
  action?: number; // 1-从搜索结果列表页点击进入profile详情页； 2-推荐给PM；
  track_id?: string; // 每一个搜索结果UniteSearchItem都有一个唯一的track_id
};

/**
 * 全网搜埋点上报
 * */
export function setSearchBury(params: TSearchBury) {
  if (import.meta.env.VITE_VUE_APP_BUILD_ENV === "production") {
    return request.post(`talent/third/unionSearch/bury`, params);
  }

  return () => { };
}

type TSearchShowBury = {
  action: 3,
  site: 1 | 6,
  items: {
    trackId: string,
    talentId: string,
    rank: number,
  }[]
}
export function setSearchShowBury(params: TSearchShowBury) {
  if (import.meta.env.VITE_VUE_APP_BUILD_ENV === "production") {
    return request.post(`talent/third/unionSearchShow/bury`, params);
  }

  return () => { };
}

export function trackEvent(params: [ICB.TrackEvent]) {
  if (import.meta.env.VITE_VUE_APP_BUILD_ENV !== "production") return
  return request.post('/common/log', params)
}
