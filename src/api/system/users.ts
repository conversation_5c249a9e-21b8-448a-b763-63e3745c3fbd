import { useUserStore } from "@/store/user.store"
import request from '@/utils/request'

const BASE_URL = import.meta.env.VITE_VUE_APP_API_BASE_URL;
export const API_URL = {
  UPLOAD_IMAGE: BASE_URL + "/common/file/uploadImage",
  UPLOAD_FILE: BASE_URL + "/common/file/uploadFile",
};

interface PaginationParams {
  current: number,
  size: number
}

export function getDepartmentUserList(deptId: number, params: PaginationParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/department/${deptId}/users`, { params })
}

/**
 * 
 */
interface getCompanyUserListParams {
  // 是否在职, null表示全部
  onboard?: boolean | null,

  // 0 表示全职，1 表示兼职, null表示全部
  employeeType?: number | null,
}
export function getCompanyUserList(params?: getCompanyUserListParams) {
  const userStore = useUserStore()
  let filter = {}
  if (params?.onboard !== null) {
    filter = { ...filter, onboard: params?.onboard ? 1 : 0 }
  }
  if (params?.employeeType !== null) {
    filter = { ...filter, employeeType: params?.employeeType }
  }
  return request.get(`/company/${userStore.companyId}/users`, {
    params: filter
  })
}

/**
 * 更新用户信息
 */
interface updateCompanyUserParams {
  id: number,
  gender: number,
  // positionTitle: string,
  realName: string,
}

export function updateCompanyUser(params: updateCompanyUserParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/user/${params.id}`, params)
}

interface newCompanyUserParams {
  realName: string,
  workMobile: string,
  gender: null | number,
  isAdministrator: number,
  // positionTitle: string,
  deptId: number | null,
}
export function newCompanyUser(params: newCompanyUserParams[]) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/allnew/users`, params)
}

export function getCompanyUserDetail(companyUserId: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/user/${companyUserId}`)
}

export function departCompanyUser(companyUserId: number) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/depart/${companyUserId}`)
}

export function reOnboardCompanyUser(companyUserId: number) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/reOnboard/${companyUserId}`)
}

export function getUserScoreFieldTypesByRole(roleName: string) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/user/score/${roleName}`)
}

/**
 * 获取运营对接人列表
 */
export function getOperationContacts() {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/operation/contacts`)
}