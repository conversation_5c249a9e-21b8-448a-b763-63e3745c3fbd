import { useUserStore } from '@/store/user.store'
import request from '@/utils/request'

interface RoleParams {
  permissions: string[],
  roleCnName: string,
  roleName: string
}

/**
 * 获取公司角色列表
 */
export function getCompanyRoles() {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/roles`)
}

/**
 * 新增角色信息
 */
export function addCompanyRole(params: RoleParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/roles`, params)
}

/**
 * 更新角色信息
 */
export function updateCompanyRole(roleId: number, params: RoleParams) {
  const userStore = useUserStore()
  return request.put(`/company/${userStore.companyId}/role/${roleId}`, params)
}

/**
 * 删除公司角色
 */
export function deleteCompanyRole(roleId: number) {
  const userStore = useUserStore()
  return request.delete(`/company/${userStore.companyId}/role/${roleId}`)
}

interface getCompanyUserByRoleIdParams {
  size: number
  current: number
}

/**
 * 获取公司指定角色下的员工列表
 */
export function getCompanyUserByRoleId(roleId: number, params: getCompanyUserByRoleIdParams = { size: 20, current: 1 }) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/role/${roleId}/users`, { params })
}

/**
 * 在公司的角色中增加员工
 */
export function addUserToCompanyRole(roleId: number, userIds: number[]) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/role/${roleId}/users`, {
    userIds
  })
}

/**
 * 
 */
export function getCompanyUsersByRoleName(roleName: string) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/role/name/${roleName.toLowerCase()}/users`)
}

/**
 * 获取全部在职员工列表
 */
export function getCompanyOnboardUsers(roleName: string, comapnyId?:number) {
  const userStore = useUserStore()
  const cid = comapnyId || userStore.companyId
  return request.get(`/company/${cid}/role/name/${roleName}/users/onboard`)
}

/**
 * 
 */
export function removeUserFromCompanyRole(roleId: number, userIds: number[]) {
  const userStore = useUserStore()
  return request.delete(`/company/${userStore.companyId}/role/${roleId}/users`, { data: { userIds } })
}

export function getCompanyRule(roleId: number) {
  const userStore = useUserStore()
  return request.get(
    `/company/${userStore.companyId}/role/${roleId}`
  )
}