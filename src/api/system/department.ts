import { useUserStore } from "@/store/user.store"
import request from "@/utils/request"

export function getCompanyDepartment(companyId?:number) {
  const userStore = useUserStore()
  const cid = companyId || userStore.companyId
  return request.get(`/company/${cid}/departments`)
}

export interface addCompanyDepartmentParams {
  name: string,
  parentId?: number | null,
  userId?: number | null
}


export function addCompanyDepartment(params: addCompanyDepartmentParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/department`, params)
}

interface addCompanyDepartmentUsersParams {
  departmentId: number | null,
  users: number[],
}

export function addCompanyDepartmentUsers(params: addCompanyDepartmentUsersParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/department/${params.departmentId}/users`, {
    userIds: params.users
  })
}

export function moveUserToDepartment(userIds: number[], departmentIds: number[]) {
  const userStore = useUserStore()
  return request.put(`/company/${userStore.companyId}/department/users`, { companyUserIds: userIds, departmentIds })
}

export function deleteCompanyDepartment(departmentId: number) {
  const userStore = useUserStore()
  return request.delete(`/company/${userStore.companyId}/department/${departmentId}`)
}

export function updateDepartmentLeader(departmentId:number, companyUserId: number) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/department/${departmentId}/leader/user/${companyUserId}`)
}