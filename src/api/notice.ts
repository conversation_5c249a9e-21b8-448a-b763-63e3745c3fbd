import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

interface TNoticeList {
  current: number;
  size: number;
  type: number
}
/**
 * 获取站内消息列表
 * */
export function getNoticeList(params: TNoticeList) {
  const userStore = useUserStore();
  return request.get(`/company/${userStore.companyId}/notice/list`, { params });
}

/**
 * 获取站内未发送消息
 * */
export function getNoticeUnread() {
  const userStore = useUserStore();
  return request.get(`/company/${userStore.companyId}/notice/unread`);
}

export function getLatestAllNotice() {
  const companyId = 1;
  return request.get(`/company/${companyId}/notice/all/list`, {
    params: {
      size: 10
    }
  });
}

/**
 * 已读消息
 * */
export function setNoticeRead(noticeId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/notice/read/${noticeId}`
  );
}

export function sendFeishuNotice(token: string, data: any) {
  return request.post(`https://open.feishu.cn/open-apis/bot/v2/hook/${token}`, data);
}


// v2 首页-推荐职位tabs
export function getRecommendationsTabs(limit: number = 10) {
  const userStore = useUserStore();
  const companyId = userStore.companyId;
  return request.get(`/company/${companyId}/ai/jobRequirement/my/withRecommendations`, {
    params: {
      limit
    }
  });
}


// GET /api/company/${companyId}/ai/aiTopTalent/list/{jobRequirementId}
// ?current=xxx&size=xxx
export function getRecommendationsList(jobRequirementId: number, current: number = 1, size: number = 10) {
  const userStore = useUserStore();
  const companyId = userStore.companyId;
  return request.get(`company/${companyId}/ai/aiTopTalent/list/${jobRequirementId}`, {
    params: {
      current,
      size
    }
  });
}

// POST /api/company/{companyId}/ai/aiTopTalent/recommend/status
// {
//   "contactStatus": 1,
//   "processStatus": 2,
//   "rejectReason": "不合适的原因"
// }
export function postRecommendStatus(params: any) {
  const userStore = useUserStore();
  const companyId = userStore.companyId;
  return request.post(`/company/${companyId}/ai/aiTopTalent/recommend/status/${params?.id}`, params);
}
