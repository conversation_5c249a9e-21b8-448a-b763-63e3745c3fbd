import { useUserStore } from '@/store/user.store';
import request from '@/utils/request';
import exp from 'constants'
import { v4 } from 'uuid'

export interface GetTalentParams {
  keyword?: string;
  areaId?: number | null;
  degree?: number | null;
  workYears?: string | null;
  mobileNumber: string;


  // gender 1-男, 2-女
  gender?: 1 | 2 | null;

  // 0: 'ITP', 1: '谷露', 2: '灵鹿聘', 3: '灵鹿推'
  sourceType?: 0 | 1 | 2 | 3 | null;
  sourceId?: string | null;

  isFromRec?: boolean,
}

export function getAllTalentList(params: GetTalentParams, pagination: any) {
  return request.request({
    url: '/talent/list',
    method: 'POST',
    params: {
      current: pagination.current,
      size: pagination.pageSize,
    },
    data: params,
  });
}

export function getMyTalentList(params: GetTalentParams, pagination: any) {
  const userStore = useUserStore()
  const searchParams = Object.assign({}, params, {
    companyUserId: userStore.id,
  })
  return request.request({
    url: '/talent/list',
    method: 'POST',
    params: {
      current: pagination.current,
      size: pagination.pageSize,
    },
    data: searchParams,
  });
}


export function getCompanyTalentList(params: GetTalentParams, pagination: any) {
  const userStore = useUserStore();
  return request.request({
    url: `/talent/myCompany/list`,
    method: 'POST',
    params: {
      current: pagination.current,
      size: pagination.pageSize,
      // ...params
    },
    data: params,
  });
}

export function getTalentDetail(talentId: number) {
  return request.get(`/talent/detail`, { params: { talentId: talentId } });
}

export function getTalentBlackList(talentId: number) {
  return request.get(`/talent/in/black/list/${talentId}`)
}

export function updateTalentBlackListStatus(talentId:number, blockStatus:number) {
  const params = {
    talentId: talentId,
    blockStatus: blockStatus
  }
  return request.post(`/talent/black/list/update`, params);
}

export function createOrUpdateTalent(standardTalent: any) {
  return request.post('/talent/createOrUpdate', standardTalent);
}

export function updateTalentBase(talent: any) {
  return request.post(`/talent/updateTalentBase`, talent);
}

export function deleteTalent(talentId: number) {
  return request.post(`/talent/delete/${talentId}`);
}

const BASE_URL = import.meta.env.VITE_VUE_APP_API_BASE_URL;

export const API_URL = {
  RESUME_UPLOAD: BASE_URL + '/resume/manage/uploadResume',
  AVATAR_UPLOAD: BASE_URL + '/common/file/uploadImage',
  MULTI_RESUME_UPLOAD: BASE_URL + '/resume/manage/company/1/uploadZipResume',
};

interface getTalentTaskPositionProps {
  current: number;
  size: number;
  talentId: number;
}

export function getTalentTaskPosition(params: getTalentTaskPositionProps) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/pipeline/by/talent/${params.talentId}`,
    {
      params: {
        current: params.current,
        size: params.size,
      },
    }
  );
}

interface createCompanyTalentFollowUpParams {
  comment: string;
}

export function createCompanyTalentFollowUp(
  talentId: number,
  params: createCompanyTalentFollowUpParams
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/talent/${talentId}/followup`,
    params
  );
}

interface GetCompanyTalentFollowUpParams {
  size: number;
  current: number;
  assignee: number | null;
  jobRequirementId: number;
}

export function getCompanyTalentFollowUp(
  talentId: number,
  params: GetCompanyTalentFollowUpParams
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/talent/${talentId}/followups`,
    { params }
  );
}

interface CheckTalentDuplicateParams {
  email: string;
  idCardNumber: string;
  mobileNumber: string;
  realName: string;
}

export function checkTalentDuplicate(params: CheckTalentDuplicateParams) {
  return request.get('/talent/checkDuplicate', { params });
}

export function getMergedTalent(targetTalentId: number, talentInfo: any) {
  return request.post(`/talent/mergeToTalent/${targetTalentId}`, talentInfo);
}

// 更新查看次数
export function updateTalentViewCount(
  jobRequirementId: string,
  talentId: number,
  params: {} = {}
) {
  const userStore = useUserStore();

  return request.post(
    `/company/${userStore.companyId}/pipeline/updateTalentViewCount/${jobRequirementId}/${talentId}`,
    params
  );
}

// 全网搜转换人才列表信息
export function getMultiSearchTalentList(params: {} = {}) {
  return request.post(`/talent/third/transWebsiteTalent`, params);
}

// 全网搜搜索参数转换
export function getMultiSearchTalentParams(jobRequirementId: number) {
  return request.get(
    `/talent/third/unionSearch/params/jobRequirement/${jobRequirementId}`
  );
}

export function saveSmartDeerAppTalent(accountId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/smartdeer/individual/profile/${accountId}`
  )
}

export function saveGlobalTalent(talent: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/`
  )
}

export function saveTalentReportToFile(html: string) {
  const userStore = useUserStore()
  return request.post('/talent/convertHtmlToWord', {
    html: html
  })
}

export function sendEmail(talentId: number, to: string, subject: string, content: string) {
  const uuid = v4()
  const userStore = useUserStore()
  const base = import.meta.env.VITE_VUE_APP_API_BASE_URL
  const emailContent = content + `<img height=1px width=1px src=${base}/talent/email/track.png?uuid=${uuid} />`

  return request.post(`/talent/${talentId}/send/email`, {
    emailBody: emailContent,
    subject,
    toEmailAddress: to,
    uuid: uuid
  })
}

export function getTalentReports(talentId: number) {
  const userStore = useUserStore()
  return request.get(`/talent/company/${userStore.companyId}/process/report/list`, {
    params: { talentId: talentId }
  })
}

interface AttachTalentReportProps {
  fileId: string;
  htmlContent: string;
  jobRequirementId: number;
  processInstanceId: string;
  jsonContent: string;
  talentId: number;
}

export function attachReportToTalentJob(props: AttachTalentReportProps) {
  const userStore = useUserStore()
  return request.post(`/talent/company/${userStore.companyId}/process/report/add`, {
    ...props,
    companyId: userStore.companyId,
    companyUserId: userStore.id,
  })
}

export function iptTalentUnionSearch(searchParams: any) {
  return request.post('/talent/third/unionSearch/itp', searchParams)
}

export function unionSearchTranslateParams(params: UnionSearchParams) {
  return request.post(`/talent/third/unionSearch/getParams`, params)
}

export function unionGlobalTalentSearch(params: any) {
  return request.post(`/global/talent/search`, params)
}

export function unionSearchParamsVocation(keyword: string) {
  return request.get(`/global/talent/vocation/${keyword}`)
}

export function unionSearchParamsSchool(keyword: string) {
  return request.get(`/global/talent/school/${keyword}`)
}

export function unionSearchParamsMajor(keyword: string) {
  return request.get(`/global/talent/major/${keyword}`)
}

export function unionSearchParamsArea() {
  return request.get(`/global/talent/area`)
}

export function unionSearchParamsCompany(keyword: string) {
  return request.get(`/global/talent/company/${keyword}`)
}

export function unionSearchTalentDetail(id: number) {
  return request.get(`/global/talent/detail/${id}`)
}

export function unionSearchAddGlobalTalent(talent: any) {
  return request.post(`/global/talent/addToMine`, talent)
}

export function getAIConsultantRecommendation(jobRequirementId: any, talentId: any) {
  return request.post(`/talent/aiConsultantComment/${jobRequirementId}/${talentId}`)
}