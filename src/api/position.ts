import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

export function getJobRequirementDetail(jobRequirementId: number | string) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}`
  );
}

export function getJobRequirementHeader(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/header/${jobRequirementId}/detail`
  );
}

interface CreateJobRequirementParams {
  areaId: any[];
  customerId: number | null;
  functionId: number | null;
  positionTitle: string;
  priority: null | number;
  quantityRequired: null | number;
  requireAgeFrom: null | number;
  requireAgeTo: null | number;
  requireDegree: null | number;
  requireEliteSchool: null | number;
  requireEnglishLevel: null | number;
  requireWorkYears: null | number;
  salaryTime: null | number;
  salaryTimeUnit: null | number;
  salaryFrom: null | number;
  salaryTo: null | number;
  salaryUnit: null | number;
  startDate: string;
  workDetail: string;
  workRequire?: string;
  positionContacts: any[];
  remoteWork?: number;
}

export function createJobRequirement(params: CreateJobRequirementParams) {
  const userStore = useUserStore();
  return request.post(`/company/${userStore.companyId}/position`, params);
}

export function getPositionDetail(positionId: number) {
  const userStore = useUserStore();
  return request.get(`/company/${userStore.companyId}/position/${positionId}`);
}

export function updatePositionTags(positionId: number, tags: string[]) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/position/${positionId}/tags`,
    {
      tags,
    }
  );
}

interface updatePositionDetailParams {
  id: number | null;
  areaId: any[];
  customerId: number | null;
  functionId: number | null;
  positionTitle: string;
  priority: null | number;
  quantityRequired: null | number;
  requireAgeFrom: null | number;
  requireAgeTo: null | number;
  requireDegree: null | number;
  requireEliteSchool: null | number;
  requireEnglishLevel: null | number;
  requireWorkYears: null | number;
  salaryTime: null | number;
  salaryTimeUnit: null | number;
  salaryFrom: null | number;
  salaryTo: null | number;
  salaryUnit: null | number;
  teamScale: number | null;
  startDate: string;
  workDetail: string;
  workRequirement?: string;
  type: number;
  tags: string[];
}

export function updatePositionDetail(params: updatePositionDetailParams) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/position/${params.id}`,
    params
  );
}

interface getPositionListParams {
  positionTitle: string;
  areaId?: number;
  current: number;
  size: number;
  isManager?: boolean | null;
  function?: number | null;
  type?: number | null;
}

export function getPositionList(params: getPositionListParams) {
  const userStore = useUserStore();

  const searchItems = [];
  if (params.positionTitle)
    searchItems.push({
      action: "like",
      key: "positionTitle",
      value: params.positionTitle,
    });

  if (params.isManager)
    searchItems.push({
      action: "eq",
      key: "isManager",
      value: params.isManager === true ? 1 : 0,
    });

  if (params.function)
    searchItems.push({
      action: "eq",
      key: "functionId",
      value: params.function,
    });

  if (params.areaId)
    searchItems.push({
      action: "eq",
      key: "areaId",
      value: params.areaId,
    });

  if (params.type !== null)
    searchItems.push({
      action: "eq",
      key: "type",
      value: params.type,
    });

  return request({
    method: "POST",
    url: `/company/${userStore.companyId}/position/search`,
    params: { current: params.current, size: params.size },
    data: {
      searchItems,
    },
  });
}

interface getPositionsByCustomerParams {
  current: number;
  size: number;
}

export function getPositionsByCustomer(
  customerId: number,
  params: getPositionsByCustomerParams
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/position/by/${customerId}`,
    { params }
  );
}

export function getProcessTaskList(processInstanceId: string) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/process/${processInstanceId}`
  );
}

export function getPositionCode(id:number) {
  const userStore = useUserStore();
  return request.get(`/company/${userStore.companyId}/public/position/code/${id}`)
}
