/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2023-02-10 11:55:30
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-03-17 10:52:45
 * @FilePath: /itp-operation-web/src/api/stat.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";
import { pipeline } from 'stream'

interface StatParams {
  endTime: string;
  startTime: string;
  ids: number[];
  statsTalentCount?: boolean;
}

/**
 * 根据表格名称查询操作统计结果
 * */
export function getStat(reportName: string, params: StatParams, companyId?:number) {
  const userStore = useUserStore();
  const userCompanyId = companyId || userStore.companyId;
  return request.post(
    `/company/${userCompanyId}/statics/report/${reportName}`,
    params
  );
}

export function getPipelineStats(params: any, companyId: number) {
  return request.post(
    `/company/${companyId}/dashboard/report/pipeline`,
    params
  )
}

export function getPipelineStatsDetail(params: any, companyId: number) {
  return request.post(
    `/company/${companyId}/dashboard/report/pipeline/detail`,
    params
  )
}

export function getJobRequirementStats(companyId: number, params: any) {
  return request.post(
    `/company/${companyId}/dashboard/report/job/require`,
    params
  )
}

interface StatDetailParams {
  queryStr: string;
  current: number;
  size: number;
}

/**
 * 根据表格名称查询操作统计结果：详情
 * */
export function getStatDetail(reportName: string, params: StatDetailParams, companyId?:number) {
  const userStore = useUserStore();
  const userCompanyId = companyId || useUserStore().companyId;

  return request({
    method: "POST",
    url: `/company/${userStore.companyId}/statics/report/${reportName}/detail`,
    data: {
      queryStr: params.queryStr,
    },
    params: {
      current: params.current,
      size: params.size,
    },
  });
}

export function getStatDetailHeader() {
  return Promise.resolve({
    data: [
      {
        title: "候选人姓名",
        dataIndex: "talentName",
        key: "talentName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "项目所属公司",
        dataIndex: "customerFullName",
        key: "customerFullName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "职位名称",
        dataIndex: "jobName",
        key: "jobName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "面试记录",
        dataIndex: "interviewRecord",
        key: "interviewRecord",
        customCell: (record: any) => {
          return { rowSpan: 1 }
        }
      },
      {
        title: "面试创建时间",
        dataIndex: "interviewCreateTime",
        key: "interviewCreateTime",
        customCell: (record: any) => {
          return { rowSpan: 1 }
        }
      },
      {
        title: "最新流程节点名称",
        dataIndex: "latestTaskName",
        key: "latestTaskName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "推荐CA的名称",
        dataIndex: "caName",
        key: "caName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "推荐PM的名称",
        dataIndex: "pmName",
        key: "pmName",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
      {
        title: "推荐PM的时间",
        dataIndex: "processStartTime",
        key: "processStartTime",
        customCell: (record: any) => {
          return { rowSpan: record.span }
        }
      },
    ],
  });
}

interface JobRequirementsParams {
  endTime: string;
  startTime: string;
}

/**
 * 获取项目统计 jobRequirementId 列表
 * */
export function getJobRequirements(params: JobRequirementsParams) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/statics/job/requirements`,
    params
  );
}

export function getOfferHeader() {
  return Promise.resolve({
    data: [
      {
        title: "最新流程节点名称",
        dataIndex: "latestTaskName",
        key: "latestTaskName",
      },
      {
        title: "人才姓名",
        dataIndex: "talentName",
        key: "talentName",
      },
      {
        title: "客户名称",
        dataIndex: "customerFullName",
        key: "customerFullName",
      },
      {
        title: "职位名称",
        dataIndex: "processName",
        key: "processName",
      },
      {
        title: "CA姓名",
        dataIndex: "caName",
        key: "caName",
      },
      {
        title: "PM姓名",
        dataIndex: "pmName",
        key: "pmName",
      },

      {
        title: "发Offer时间",
        dataIndex: "offerCreateTime",
        key: "offerCreateTime",
        sorter: (a: any, b: any) => a.offerCreateTime - b.offerCreateTime,
      },
      {
        title: "薪资",
        dataIndex: "salary",
        key: "salary",
        width: "110px",
        sorter: (a: any, b: any) => a.salary - b.salary,
      },
      // {
      //   title: "月薪",
      //   dataIndex: "salary",
      //   key: "salary",
      //   sorter: (a: any, b: any) => a.salary - b.salary,
      // },
      // {
      //   title: "发薪月数",
      //   dataIndex: "payMonth",
      //   key: "payMonth",
      //   sorter: (a: any, b: any) => a.payMonth - b.payMonth,
      // },
      {
        title: "预计佣金",
        dataIndex: "expectCommission",
        key: "expectCommission",
        sorter: (a: any, b: any) => a.expectCommission - b.expectCommission,
      },
      {
        title: "预计到岗时间",
        dataIndex: "expectOnboardingDate",
        key: "expectOnboardingDate",
        sorter: (a: any, b: any) =>
          a.expectOnboardingDate - b.expectOnboardingDate,
      },
      {
        title: "实际入职时间",
        dataIndex: "onboardingDate",
        key: "onboardingDate",
        sorter: (a: any, b: any) => a.onboardingDate - b.onboardingDate,
      },
      {
        title: "过保时间",
        dataIndex: "overInsuranceDate",
        key: "overInsuranceDate",
        sorter: (a: any, b: any) => a.overInsuranceDate - b.overInsuranceDate,
      },
      {
        title: "淘汰原因",
        dataIndex: "obsoleteReason",
        key: "obsoleteReason",
      },
    ],
  });
}

/**
 * Offer列表
 * */
export function getOffer(params: StatParams) {
  const userStore = useUserStore();
  return request.post(`/company/${userStore.companyId}/statics/offer`, params);
}

export function getCustomerCountHeader() {
  return Promise.resolve({
    data: [
      {
        title: "客户名称",
        dataIndex: "customerFullName",
        key: "customerFullName",
      },
      {
        title: "BD 姓名",
        dataIndex: "companyUserStr",
        key: "companyUserStr",
      },
      {
        title: "新增项目数量",
        dataIndex: "positionCount",
        key: "positionCount",
      },
      {
        title: "客户添加时间",
        dataIndex: "createDate",
        key: "createDate",
      },
    ],
  });
}

export function getPositionCountHeader() {
  return Promise.resolve({
    data: [
      {
        title: "项目（职位）名称",
        dataIndex: "processName",
        key: "processName",
      },
      {
        title: "客户名称",
        dataIndex: "customerFullName",
        key: "customerFullName",
      },
      {
        title: "BD 姓名",
        dataIndex: "companyUserStr",
        key: "companyUserStr",
      },
      {
        title: "PM 姓名",
        dataIndex: "pmName",
        key: "pmName",
      },
      {
        title: "添加时间",
        dataIndex: "createDate",
        key: "createDate",
      },
      {
        title: "推荐PM数量",
        dataIndex: "pmCount",
        key: "pmCount",
      },
      {
        title: "面试人数",
        dataIndex: "interviewCount",
        key: "interviewCount",
      },
      {
        title: "Offer 人数",
        dataIndex: "offerCount",
        key: "offerCount",
      },
      {
        title: "Offer 预计收入",
        dataIndex: "offer",
        key: "offer",
      },
      {
        title: "入职人数",
        dataIndex: "hiredCount",
        key: "hiredCount",
      },
      {
        title: "入职预计收入",
        dataIndex: "hired",
        key: "hired",
      },
      {
        title: "过保人数",
        dataIndex: "keepCount",
        key: "keepCount",
      },
      {
        title: "过保预计收入",
        dataIndex: "keep",
        key: "keep",
      },
    ],
  });
}

interface FinanceParams {
  endTime: string;
  startTime: string;
}

/**
 * 项目财务报表
 * */
export function getFinance(params: FinanceParams) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/jobRequirement/statistic`,
    { params }
  );
}

export function getFinanceHeader() {
  return Promise.resolve({
    data: [
      {
        title: "开始日期",
        dataIndex: "startTime",
        key: "startTime",
      },
      {
        title: "结束日期",
        dataIndex: "endTime",
        key: "endTime",
      },
      {
        title: "客户名称",
        dataIndex: "customerName",
        key: "customerName",
      },
      {
        title: "项目名称",
        dataIndex: "positionTitle",
        key: "positionTitle",
      },
      {
        title: "候选人姓名",
        dataIndex: "realName",
        key: "realName",
      },
      {
        title: "Offer 时间",
        dataIndex: "offerTime",
        key: "offerTime",
      },
      {
        title: "入职时间",
        dataIndex: "onboardTime",
        key: "onboardTime",
      },
      {
        title: "过保时间",
        dataIndex: "finishTime",
        key: "finishTime",
      },
      {
        title: "开票张数",
        dataIndex: "invoiceIds",
        key: "invoiceIds",
      },
      {
        title: "申请开票总额",
        dataIndex: "applyInvoiceAmount",
        key: "applyInvoiceAmount",
      },
      {
        title: "实际开票总额",
        dataIndex: "confirmedInvoiceAmount",
        key: "confirmedInvoiceAmount",
      },
      {
        title: "回款笔数",
        dataIndex: "statementIds",
        key: "statementIds",
      },
      {
        title: "申请回款总额",
        dataIndex: "applyStatementAmount",
        key: "applyStatementAmount",
      },
      {
        title: "实际回款总额",
        dataIndex: "confirmedStatementAmount",
        key: "confirmedStatementAmount",
      },
    ],
  });
}

type GetOfferReceivableStatParams = {
  current: number,
  size: number,
  customerIds?: number[],
  talentRealName?: string,
  offerEndDate?: string,
  offerStartDate?: string,
  hireEndDate?: string,
  hireStartDate?: string,
  overInsureEndDate?: string,
  overInsureStartDate?: string,
}

export function getOfferReceivableStat(params: GetOfferReceivableStatParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/report/offer/Receivable`, params)
}

export function downloadOfferReceivableStat(params: GetOfferReceivableStatParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/report/offer/receivable/download`, params)
}