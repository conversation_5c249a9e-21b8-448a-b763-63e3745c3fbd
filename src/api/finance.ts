import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

// 获取一个企业的流程表头
export function getEntityList() {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/finance/config/entity/list`,
    method: "get",
  });
}

// 申请开票
export function setCreateInvoice(customerId: string, params: {}) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/finance/customer/${customerId}/createInvoice`,
    params
  );
}

type StatementDetail = {
  id?: number,
  // 对应的票据中的OFFER
  invoiceDetailId: number,
  // 票据ID
  invoiceId: number,
  // 
  statementId?: number,
  // 实收金额
  totalAmount: number
}

type CreateStatementParams = {
  // 回款货币类型
  currencyType: number | null,
  // 客户ID
  customerId: number | null,
  // 我司的财务账户ID
  financeAccountId: number | null,
  // 付款账户
  payAccountName: string,
  // 付款账户是否需要填写？
  payAccountNumber?: string,
  // 付款方式
  payMethod: number | null,
  // 交易流水号
  paySerialNumber: string,

  // 付款时间没有

  // 备注，后续使用
  remarks?: string,
  statementDetails: StatementDetail[],
  statementReason?: string,
  statementType?: number,
  totalAmount: number | null
}

export function createStatement(params: CreateStatementParams) {
  const userStore = useUserStore();
  return request.post(`/company/${userStore.companyId}/finance/statement/add`, params);
}


type CreatePaymentClaimParams = {
  currencyType: number | null,
  dueDate: string,
  financeAccountId: number | null,
  payAccountName: string,
  payMethod: number,
  paySerialNumber: string,
  totalAmount: number | null
}

export function createPaymentClaim(params:CreatePaymentClaimParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/statement/claim/add`, params)
}

export function getPaymentClaimDetail(claimId:number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/finance/statement/claim/${claimId}`)
}

type GetPaymentClaimListParams ={
  current?: number,
  size?: number,
  currencyType?: number,
  endDueDate?: string,
  financeAccountId?: number,
  isCompleted?: boolean,
  payAccountName?: string,
  startDueDate?: string
}

export function getPaymentClaimList(params:GetPaymentClaimListParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/statement/claim/search`, params)
}


type ClaimPaymentItem = {
  claimAmount: number,
  claimId: number,
  invoiceDetailId: number,
  invoiceId: number
}

export function claimPayment(statementClaimId:number, params:ClaimPaymentItem[]) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/statement/claim/confirm/${statementClaimId}`, {
    invoiceDetailItems: params,
    statementClaimId: statementClaimId
  })
}

export function getInvoiceHeader() {
  return Promise.resolve({
    data: [
      {
        title: "发起人",
        dataIndex: "applyUserRealName",
        key: "applyUserRealName",
      },
      {
        title: "项目名称",
        dataIndex: "jobRequirementNames",
        key: "jobRequirementNames",
      },
      {
        title: "开票公司",
        dataIndex: "vendorName",
        key: "vendorName",
      },
      {
        title: "发票抬头",
        dataIndex: "customerName",
        key: "customerName",
      },
      {
        title: "收款时间",
        dataIndex: "dueDate",
        key: "dueDate",
      },
      {
        title: "货币类型",
        dataIndex: "currencyTypeKey",
        key: "currencyTypeKey",
      },
      {
        title: "含税总金额",
        dataIndex: "grandTotal",
        key: "grandTotal",
      },
      {
        title: "审核状态",
        dataIndex: "invoiceStatusStr",
        key: "invoiceStatusStr",
      },
      {
        title: "操作",
        dataIndex: "operation",
        key: "operation",
      },
    ],
  });
}

type GetInvoiceListParams = {
  size: number,
  current: number,
  companyUserIds?: number[],
  customerName?: string,
  invoiceStatus?: number | null,
  invoiceType?: number | null,
  positionTitle?: string,
  endDate?: string,
  startDate?: string,
  talentRealName?: string,
  verifyEndDate?: string,
  verifyStartDate?: string,
  finaceAccountId?: number,
}

export function getInvoiceList(params: GetInvoiceListParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/invoice/search`, params)
}

// 确认开票
// export function setConfirmInvoice(invoiceId: number, params: {}) {
//   const userStore = useUserStore();
//   return request.post(
//     `/company/${userStore.companyId}/finance/invoice/${invoiceId}/confirmInvoice`,
//     params
//   )
// }

interface UpdateInvoiceParams {
  fileId: string,
  invoiceDate?: string,
  invoiceNumber: string,
  invoiceStatus?: number
}

export function updateInvoice(invoiceId: number, params: UpdateInvoiceParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/invoice/update/${invoiceId}`, params)
}

export function createDiscardInvoice(invoiceId: number, params: any) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/finance/invoice/${invoiceId}/discard`, params)
}

// 获取开票详情
export function getInvoiceDetails(invoiceId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/invoice/detail/${invoiceId}`
  );
}

// 申请回款
export function setReceivePayment(customerId: string, params: {}) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/finance/customer/${customerId}/receivePayment`,
    params
  );
}

export function getStatementHeader() {
  return Promise.resolve({
    data: [
      {
        title: "发起人",
        dataIndex: "applyUserRealName",
        key: "applyUserRealName",
      },
      {
        title: "项目名称",
        dataIndex: "jobRequirementNames",
        key: "jobRequirementNames",
      },
      {
        title: "收款公司",
        dataIndex: "financeAccount",
        key: "financeAccount",
      },
      {
        title: "付款公司",
        dataIndex: "customerFullName",
        key: "customerFullName",
      },
      {
        title: "货币类型",
        dataIndex: "currencyKey",
        key: "currencyKey",
      },
      {
        title: "总金额",
        dataIndex: "totalAmount",
        key: "totalAmount",
      },
      {
        title: "实收金额",
        dataIndex: "actualAmount",
        key: "actualAmount",
      },
      {
        title: "审核状态",
        dataIndex: "paymentStatusStr",
        key: "paymentStatusStr",
      },
      {
        title: "操作",
        dataIndex: "operation",
        key: "operation",
      },
    ],
  });
}

interface GetStatementListParams {
  current: number,
  size: number,
  customerId?: number[],
  customerName?: string,
  endDate?: string,
  payAccountName?: string,
  positionTitle?: string,
  startDate?: string,
  talentRealName?: string
}

// 回款申请列表
export function getStatementList(params: GetStatementListParams) {
  const userStore = useUserStore();
  return request.post(`/company/${userStore.companyId}/finance/statement/search`, params);
}

// 确认回款
export function setConfirmPayment(statementId: number, params: {}) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/finance/statement/${statementId}/confirmPayment`,
    params
  );
}

// 获取回款详情
export function getStatementDetails(statementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/statement/detail/${statementId}`
  );
}

export function createDeliveryContract(customerId: number, params: {}) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}/contract/add`,
    {
      ...params,
      companyId: userStore.companyId,
    }
  )
}

export function updateDeliveryContract(
  customerId: number,
  contractId: number,
  params: {}
) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}/contract/${contractId}`,
    {
      ...params,
      companyId: userStore.companyId,
    }
  );
}

export function getDeliveryContractDetail(
  customerId: number,
  contractId: number
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}/contract/${contractId}`
  );
}

/**
 * 取客户合同列表
 * */
export function getCustomerContractList(customerId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}/contracts`
  );
}

/**
 * 计算 Offer 金额
 */
export function getCalculatedOfferPrice(jobRequirementId: number, processInstanceId: string, salary: number, salaryTimeUnit: number, payMonth: number, currencyType: string, commissionCurrencyType: string) {
  const userStore = useUserStore();
  const params = {
    salary: salary,
    salaryTimeUnit: salaryTimeUnit,
    payMonth: payMonth,
    currencyType: currencyType,
    commissionCurrencyType: commissionCurrencyType
  }
  return request.get(
    `/company/${userStore.companyId}/finance/cal/offer/${jobRequirementId}/process/${processInstanceId}`,
    { params }
  )
}

/**
 * 计算开票金额
 */
export function getCalculatedInvoiceAmount(processInstanceId: string, currencyType: string | number) {
  const userStore = useUserStore();
  const params = {
    currencyType: currencyType
  }
  return request.get(
    `/company/${userStore.companyId}/finance/cal/process/${processInstanceId}`,
    { params }
  )
}

/**
 * 获取项目可用的财务配置
 */
export function getJobRequirementFinanceConfig(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/contract/jobRequirement/${jobRequirementId}`
  )
}

export function getCustomerLatestContractDetail(customerId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}/contract/ongoing/delivery`
  )
}

export function getCustomerFinanceConfig(customerId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}`
  )
}

export function updateCustomerFinanceConfig(customerId: number, params: {}) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/finance/config/customer/${customerId}`,
    params
  );
}