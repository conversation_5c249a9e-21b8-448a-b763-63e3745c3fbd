import { useUserStore } from "@/store/user.store"
import request from "@/utils/request"

export async function getCompanyPerformanceConfig() {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/performance/allocate/config/detail`)
}

type UpdateCompanyPerformanceConfigItem = {
  companyId: number,
  id?: number,
  pointId: number,
  pointRate: number
}

export async function updateCompanyPerformanceConfig(params: UpdateCompanyPerformanceConfigItem[]) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/performance/allocate/config/update`, {
    configs: params
  })
}

export async function getTaskContributors(processInstanceId: string) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/performance/allocate/process/${processInstanceId}/detail`)
}

export async function initTaskAllocation(processInstanceId:string) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/performance/allocate/process/${processInstanceId}/init`)
}

interface TaskPerformanceAllocateItem {
  companyUserId: number,
  id: number,
  jobRequirementId: number,
  pointId: number,
  pointRate: number,
  processInstanceId: string
}
export async function saveTaskPerformanceAllocate(processInstanceId: string, config: TaskPerformanceAllocateItem[]) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/performance/allocate/process/${processInstanceId}/update`, {
    configs: config
  })
}

export async function getStaffPerformanceStatistics(startDate: string, endDate: string) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/performance/statistic`, {
    params: { endDate, startDate }
  })
}

export async function getStaffPerformanceList(startDate: string, endDate: string, type: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/performance/statistic/processes`, {
    params: { endDate, startDate, performanceType: type }
  })
}

export async function getJobPerformanceDetail(processInstanceId:string, type:number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/performance/statistic/process/${processInstanceId}`, {
    params: { performanceType: type }
  })
}