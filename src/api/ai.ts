import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

export function getSearchParams(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/searchParams/jobRequirement/${jobRequirementId}`
  );
}

export function getTalentMatchScore(jobRequirementId: number, talentId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/matchScore/jobRequirement/${jobRequirementId}/talent/${talentId}`
  );
}

export function createConversation(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/conversation/create`,
    params
  );
}

export function getConversationMessageList(conversationId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/conversation/${conversationId}/message/list`
  );
}

export function getCozeAppConfig() {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/coze/config`
  );
}

export function getTalentMatchingParams (jobRequirementId: number, talentId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/matchParams/jobRequirement/${jobRequirementId}/talent/${talentId}`
  );
}

export function getThirdPartyTalentMatchScore(jobRequirementId: number, talentJson: string) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/matchScore/jobRequirement/${jobRequirementId}/thirdPartyTalent`,
    {
      talentInfo: talentJson
    }
  );
}

export function runWorkflow(appId: string, workflowId: string, params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/coze/appId/${appId}/workflowId/${workflowId}`,
    params
  );
}

export function runWorkflowStream(appId: string, workflowId: string, params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/coze/stream/appId/${appId}/workflowId/${workflowId}`,
    params
  );
}

export function saveTopTalent(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/aiTopTalent/create`,
    params.talent
  );
}

export function getTopTalentList(jobRequirementId: number, params: any) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/aiTopTalent/list/${jobRequirementId}`,
    {
      params: {
        "current": params.current,
        "size": params.size
      }
    }
  );
}

export function getPositionMapParsedAreas(jobId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/area/mapping/${jobId}`
  );
}

/**
 * 获取小推设置
 * @returns 
 */
export function getPushSetting() {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/ai/aiTopTalent/contact/config`
  );
}

/**
 * 保存小推设置
 * @param params 
 * @returns 
 */
export function savePushSetting(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/aiTopTalent/contact/config`,
    params
  );
}

export function createAiScoreQueue(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/aiTopTalent/queue/create`,
    params
  );
}

export function createAiScoreQueueBatch(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/aiTopTalent/queue/batch/create`,
    params
  );
}

export function updateAiScoreQueueStatus(params: any) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/ai/aiTopTalent/queue/update/status`,
    params
  );
}

export function getAllAiConfigAreaList() {
  return request({
    url: `/area/allCity`,
    method: "get",
  });
}
