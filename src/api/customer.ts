import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

const BASE_URL = import.meta.env.VITE_VUE_APP_API_BASE_URL;
export const API_URL = {
  LOGO_UPLOAD: BASE_URL + "/common/file/uploadImage",
  UPLOAD_FILE: BASE_URL + "/common/file/uploadFile",
};

interface GetCustomerListParams {
  areaId?: number;
  size?: number;
  current?: number;
  industryId?: number;
  investStatus?: number;
  keyWord?: string;
}

export function getCustomerList(queryParams: GetCustomerListParams) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/customer/search`,
    queryParams
  );
}

export interface CustomerContact {
  // 姓名
  // name:string
  areaId: number | null;
  department?: string;
  gender?: number | null;
  isKeyMan: boolean | null;
  mail?: string;
  phone: string;
}

export interface createCustomerProps {
  areaId: number | null;
  companyScale: number | null;
  companyType: number | null;
  customerContacts: CustomerContact[];
  customerExternalName?: string;
  customerFullName: string;
  customerIntroduction?: string;
  customerLogo?: string;
  customerWebsite?: string;
  customerAddress: string;
  source?: number;
  industryId: number | null;
  investStatus: number | null;
  licenseNumber?: string;
  licensePhoto?: string;
  // 上传合同
  // 备注信息
  // 负责BD
  //
}

export function createCustomer(props: createCustomerProps) {
  const userStore = useUserStore();
  return request.post(`/company/${userStore.companyId}/customer`, props);
}

export function getCustomerDetail(customerId: number) {
  const userStore = useUserStore();
  return request.get(`/company/${userStore.companyId}/customer/${customerId}`);
}

interface updateCustomerDetailParams {
  id: number;
  customerFullName: string;
  areaId: number;
  investStatus: number;
  companyType: number;
  companyScale: number;
  industryId: number;
  customerAddress: string;
  customerIntroduction: string;
  customerLogo: string;
}
export function updateCustomerDetail(params: updateCustomerDetailParams) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/customer/${params.id}`,
    params
  );
}

export function getMyCustomerList(queryParams: GetCustomerListParams) {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/customer/mine/search`,
    method: 'post',
    data: queryParams
  })
}
