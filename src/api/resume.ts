import request from '@/utils/request'

const resumeApi = {
  LatestFullResume: '/resume/manage/getLatestResume'
}

export function getTalentLatestResume(talentId: number) {
  return request.get(resumeApi.LatestFullResume, {params: { talentId: talentId }})
}

export function getTalentResumeAttachments(talentId: number) {
  return request.get(`/resume/manage/getAttachments`, { params: { talentId } })
}

export function getResumeAttachmentsByFileIds(fileIds: string[]) {
  return request.post(`/resume/manage/attachments/fileIds`, {
    fileIds: fileIds
  })
}