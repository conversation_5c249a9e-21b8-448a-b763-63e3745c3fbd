import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

interface TextMessage {
  sendType: 1
  text: string,
  toImUserId: string,
  fromImUser: string,
}

export function sendMessage(message: TextMessage) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/smartdeer/im/chat/send`, message)
}

export async function getUnReadMessageByImUserId(fromImUserId:string, pagination: {current:number, pageSize: number}){
  const {current, pageSize} = pagination
  const userStore = useUserStore()
  const {data} = await request.get(`/company/${userStore.companyId}/smartdeer/im/unread`, {
    params:{
    current,
    size: pageSize,
    fromImUser: fromImUserId
  }})
  const result =  JSON.parse(data)
  return result
}

export function clearUnReadCount({from, to}:{from:string, to:string}) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/smartdeer/im/message/read`, {
    fromImUser: from,
    toImUser: to
  })
}

export async function getChatHistoryList(fromImUserId:string, toImUserId:string, pageNum:number, pageSize:number = 50) {
  const userStore = useUserStore()
   const {data} = await request.get(`/company/${userStore.companyId}/smartdeer/im/chat/info`, {
    params: {
      current: pageNum,
      size: pageSize,
      fromImUser: fromImUserId,
      toImUser: toImUserId
    }
  })
  const result =  JSON.parse(data)
  return result
}

export default {
  sendMessage,
  getUnReadMessageByImUserId,
  clearUnReadCount,
  getChatHistoryList
}