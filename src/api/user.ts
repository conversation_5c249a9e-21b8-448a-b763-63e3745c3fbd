import request from '@/utils/request'
import { useUserStore } from '@/store/user.store'

const accountApi = {
  Login: '/user/login',
  Logout: '/user/logout',
  UserInfo: '/user/info',
}

export interface LoginParams {
  username?: string
  password: string
  captcha?: string
  remember_me: boolean,
  userMobile?: string,
}
/**
 * login
 * @param LoginParams
 * @returns {*}
 */
export function login(params: LoginParams): Promise<any> {
  return request({
    url: '/user/login',
    method: 'POST',
    data: params
  })
}

export function getUserPermission(companyId: number) {
  return request({
    url: `/company/${companyId}/permission/user`,
    method: 'GET'
  })
}

export function getInfo() {
  return request({
    url: '/user/info',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function securityCheck() {
  return request.get('/user/security')
}

export function updatePassword(newPassword: string, oldPassword: string) {
  return request.put('/user/password', {
    newPassword,
    oldPassword
  })
}

export function getMySubordinates() {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/my/subordinate`)
}
