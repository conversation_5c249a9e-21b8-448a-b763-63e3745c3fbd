import { useUserStore } from '@/store/user.store'
import request from '@/utils/request'

interface getCandidateListParams {
  current: null | number,
  size: null | number,
}

export function getCandidateList(params: getCandidateListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/candidate`, { params })
}

export function getCandidateListByStaffId(staffId: number, params: getCandidateListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/candidate/by/user/${staffId}`, { params })
}

interface getCandidateFollowupsParams {
  current: number,
  size: number,
}

export function getCandidateFollowups(candidateId: number, params: getCandidateFollowupsParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/candidate/${candidateId}/followups`, { params })
}

export function addCandidateFollowup(candidateId: number, comment: string) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/candidate/${candidateId}/followup`, { comment })
}

export function getCandidateDetail(candidateId: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/candidate/${candidateId}`)
}

export function getIntentionListByCandidateId(candidateId: number, params: getCandidateListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/intention/list/by/candidate/${candidateId}`, { params })
}

export function getCandidateTalentInfo(candidateId: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/candidate/${candidateId}/talents/latest/by/me`)
}

export function updateCandidateOwner(candidateId: number, currentCompanyUserId: number) {
  const userStore = useUserStore()
  return request.put(`/company/${userStore.companyId}/candidate/${candidateId}/owner`, {
    currentCompanyUserId
  })
}

interface createCandidateTalentResumeBindParams {
  candidateId: number,
  resumeOriginParsedId?: number,
  talentId: number,
}

export function createCandidateTalentResumeBind(params: createCandidateTalentResumeBindParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/candidate/${params.candidateId}/talent`, {
    resumeOriginParsedId: params.resumeOriginParsedId,
    talentId: params.talentId
  })
}

export function removeCandidateTalentResumeBind(candidateId:number) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/candidate/${candidateId}/delTalent`)
}

interface getPositionPushListParams {
  current: null | number,
  size: null | number
}

export function getPositionAggregationListByStaffId(staffId: number, params: getPositionPushListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/analyse/list/by/${staffId}`, { params })
}

export function getPositionAggregationList(params: getPositionPushListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/analyse/list/by/all`, { params })
}

export function getPositionAggregationDetail(aggregationId: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/${aggregationId}`)
}

interface positionPush {
  comment: string,
  positionId: number,
  kind: number,
  sort: number
}

interface PositionPushDTO {
  title: string,
  comment: string,
  positions: positionPush[]
}

export function createPositionPush(params: PositionPushDTO) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/position/aggregation`, params)
}

export function getPositionPushDetail(positionPushId: number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/${positionPushId}`)
}

export function getPositionPushListByStaffId(staffId: number, params: getPositionPushListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/list/by/${staffId}`, { params })
}

export function getAllPositionPushList(params: getPositionPushListParams) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/position/aggregation/list/by/all`, { params })
}

export function updatePositionPush(positionPushId: number, params: PositionPushDTO) {
  const userStore = useUserStore()
  return request.put(`/company/${userStore.companyId}/position/aggregation/${positionPushId}`,)
}

// interface createShareTokenParams {
//   channelId: number, 
//   channelType?: number, 
//   description?: string,
//   extra?: object,
//   miniAppUrl?: string,
//   qrCodeUrl?: string,
//   title?: string,
//   url?: string,
// }

// export function createShareToken(params: createShareTokenParams) {
//   const userStore = useUserStore()
//   return request.post(`/company/${userStore.companyId}/share/token`, {
//     ...params,
//     extra: JSON.stringify(params.extra)
//   })
// }

interface createWechatMiniprogramCodeParams {
  page: string,
  scene: string,
}

export function createWechatMiniprogramCode(params: createWechatMiniprogramCodeParams) {
  if (params.scene.length > 32) throw new Error('小程序码，scene参数不能超过32个字符')
  const envVersion = import.meta.env.VITE_VUE_WECHAT_MINIPROGRAMM_ENV
  return request.post(`/wx/miniapp/qr`, {
    envVersion,
    // 先打开，看看是否可以
    checkPath: true,
    page: params.page,
    scene: params.scene,
  })
}