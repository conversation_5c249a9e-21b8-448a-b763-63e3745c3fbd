<!--
 * @Author: xu.sun <EMAIL>
 * @Date: 2023-02-08 14:47:05
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-02-17 11:25:36
 * @FilePath: /itp-operation-web/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template lang="pug">
a-config-provider(:locale="locale" :theme="theme")
  template(v-if="!isReady")
    a-spin(:spinning="true" tip="Loading..." size="large")
      div(style="width: 100%; height: 100vh;")
  router-view(v-else)
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import zhCN from "ant-design-vue/es/locale/zh_CN"
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
import { useWinHeightStore } from '@/store/winHeight.store'
import { useUserStore } from './store/user.store'
import { onMounted } from 'vue'

dayjs.locale("zh-cn")

const userStore = useUserStore()
const winHeightStore = useWinHeightStore()
const isReady = ref(false)

window.addEventListener('resize', () => {
  winHeightStore.dispatch(window.innerHeight)
})

const locale = ref(zhCN)

const theme = {
  token: {
    colorPrimary: '#ff9111',
    colorLinkHover: '#FF9111',
    colorLinkActive: '#FF9111',
    colorLink: '#ff9111',
    successColor: '#03E3B0',
    warningColor: '#FF9111',
    errorColor: '#F9470D',
  },
}

onMounted(async () => {
  try {
    await userStore.getPermission()
  } catch (error) {
    console.log(error)
  }
  isReady.value = true
})
</script>

<style lang="sass" scoped>
</style>
