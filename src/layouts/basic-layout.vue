<template lang="pug">
a-layout.layout-main
  a-layout-header.layout-header
    app-header
  a-layout
    a-layout-sider.layout-sider(v-model:collapsed="appStore.collapsed")
      app-sider
    a-layout-content
      .layout-container(:class="{collapsed: appStore.collapsed}")
        .notification
          .notification-icon
            SoundOutlined
          .notification-list
            a-carousel(dot-position="right" autoplay :dots="false" :slidesToShow="2")
              .notification-item(v-for="item in latestNotice" :key="item.id")
                div {{ item.noticeContent }} [{{ formatDate(item.createTime) }}]
        a-alert(style="margin-bottom:15px;" :banner="true" type="info" showIcon message="请注意：", description="所有业绩的财务核算都以系统数据为准，具体是指以系统推荐到项目中人选的所属顾问分得该人选成单的交付业绩。")
        router-view(v-slot="{ Component }")
          keep-alive(:max="10")
            component(:is="Component")


  //- 意见反馈表单
  a-modal(title="意见反馈" v-model:open="status.showSuggestForm" :maskClosable="false" :destroyOnClose="true" @ok="submitSuggest")
    a-form(:model="form" ref="formInstance")
      a-form-item(
        name="suggestType" 
        :rules="[{required: true, type:'string', message: '请选择意见类型'}]" 
        label="意见类型"
      )
        a-select(v-model:value="form.suggestType")
          a-select-option(value="功能建议") 功能建议
          a-select-option(value="BUG") BUG
          a-select-option(value="其他") 其他
      a-form-item(
        name="suggestContent"
        :rules="[{required: true,  type:'string', message: '请输入建议内容'}]"
        label="意见内容"
      )
        a-textarea(v-model:value="form.suggestContent"  placeholder="请输入建议内容" rows="4")
</template>

<script lang="ts" setup>
import { onMounted, onUpdated, ref } from 'vue'
import appHeader from "@/components/app/app-header.vue"
import appSider from "@/components/app/app-sider.vue"
import { SoundOutlined } from '@ant-design/icons-vue'
import { getLatestAllNotice } from '@/api/notice'
import { useAppStore } from '@/store/app.store'
import { reactive } from 'vue';
import { useUserStore } from '@/store/user.store';
import request from '@/utils/request';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs'

const collapsed = ref(true)
const appStore = useAppStore()
const userStore = useUserStore()

const formInstance = ref()
const form = reactive({
  suggestType: '功能建议',
  suggestContent: '',
  user: `${userStore.name}(${userStore.id})`,
  page: `${window.location.href}`
})

const status = reactive({
  showSuggestForm: false
})

const latestNotice = ref<any>([])

function handleSuggest() {
  form.user = `${userStore.name}(${userStore.id})`
  form.page = `${window.location.href}`
  status.showSuggestForm = true
}

async function fetchAllNotification () {
  try {
    const res = await getLatestAllNotice();
    latestNotice.value = res.data.companyNotices
  } catch (err: any) {
    message.error(err.message)
  }
}

async function submitSuggest() {
  try {
    await formInstance.value.validate()
    request.post('https://open.feishu.cn/open-apis/bot/v2/hook/bc947ff1-4fea-4243-8109-0c8ab2687228', {
      msg_type: 'post', content: {
        "post": {
          "zh_cn": {
            "title": `Biu!, ${form.user} 提了一个建议！`,
            "content": [
              [{ "tag": "text", "text": `意见类型: ${form.suggestType}` }],
              [{ "tag": "text", "text": `意见内容: ${form.suggestContent}`, }],
              [{ "tag": "text", "text": `所在页面: ${form.page}`, }]
            ]
          }
        }
      }
    })
    message.success('您的意见我们已经收到啦！感谢您的反馈！')
    status.showSuggestForm = false
  } catch (error) {
    console.log(error)
  }
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}


onMounted(() => {
  fetchAllNotification()
})

onUpdated(() => {
  fetchAllNotification()
})
</script>

<style lang="less" scoped>
.layout-main {
  min-height: 100vh;

  .layout-header {
    background-color: #fff;
    padding: 0;
    position: fixed;
    width: 100%;
    z-index: 100;
    border-bottom: 1px solid #f0f0f0;
  }

  .layout-sider {
    background-color: transparent;
    position: fixed;
    height: 100%;
    padding-top: 84px;
    user-select: none;

    :deep(.ant-menu-item-selected) {
      background-color: transparent;

      &::after {
        display: none;
      }
    }
  }

  .layout-container {
    transition: all .2s;
    padding: 84px 16px 0px 216px;
    height: 100%;
  }

  .collapsed {
    transition: all 0.2s;
    padding: 84px 16px 20px 96px;
  }
}
.notification {
  min-width: 90vw;
  margin-bottom: 10px;
  background: #FFF;
  padding: 10px 0 10px 27px;
}
.notification-icon {
  width: 35px;
  color: #7cb305;
  display: inline-block;
  vertical-align: top;
  font-size: 25px;
}
.notification-list {
  vertical-align: top;
  width: 850px;
  height: 50px;
  line-height: 25px;
  display: inline-block;
}
.notification-item {
  min-width: 100vw;
  height: 25px;
}
.ant-carousel .slick-slide {
  height: 50px;
  line-height: 25px;
  overflow: hidden;
}
.slick-slide {
  height: 50px;
}
</style>
