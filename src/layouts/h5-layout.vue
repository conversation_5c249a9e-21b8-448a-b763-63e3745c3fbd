<template lang="pug">
ConfigProvider(:theme="theme")
  router-view
</template>

<script lang="ts" setup>
import { ConfigProvider } from 'ant-design-vue';

const theme = {
  token: {
    colorPrimary: '#ff9111',
    colorLinkHover: '#FF9111',
    colorLinkActive: '#FF9111',
    colorLink: '#ff9111',
    successColor: '#03E3B0',
    warningColor: '#FF9111',
    errorColor: '#F9470D',
  },
}
</script>
  