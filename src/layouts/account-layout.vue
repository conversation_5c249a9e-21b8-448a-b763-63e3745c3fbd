<template lang="pug">
a-layout.layout-main
  a-layout-header.layout-header
    AccountHeader
  a-layout-content.layout-content
    a-row(:gutter="16")
      a-col(:span="6")
        AccountSider
      a-col(:span="18")
        router-view
  </template>
  
<script lang="ts" setup>
import { ref } from 'vue'
import AccountHeader from "@/components/app/account-header.vue"
import AccountSider from "@/components/app/account-sider.vue"

</script>
  
<style lang="less" scoped>
.layout-header {
  background-color: #fff;
  border-bottom: 1px solid #f9f9f9;
  position: fixed;
  width: 100%;
}

.layout-content {
  padding: 24px;
  max-width: 1024px;
  width: 100%;
  margin: 0 auto;
  min-height: 100vh;
  padding-top: 88px;
}
</style>
  