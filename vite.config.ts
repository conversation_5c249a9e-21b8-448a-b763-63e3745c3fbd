/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-07-19 13:35:22
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2022-07-25 11:20:53
 * @FilePath: /itp-operation-web/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

// 定义vite编译vue时的参数
const pluginVueOptions = {
  template: {
    transformAssetUrls: {
      video: ["src", "poster"],
      source: ["src"],
      img: ["src"],
    },
  },
};

export default defineConfig({

  plugins: [vue(pluginVueOptions)],

  build: {
    rollupOptions: {
      // plugins: [visualizer()],
      manualChunks: {
        vendor: ["vue", "ant-design-vue"],
      },
      input: {
        main: path.resolve(__dirname, "./index.html"),
        h5: path.resolve(__dirname, "./h5/index.html"),
      }
    },
    sourcemap: process.env.NODE_ENV==='production'? 'hidden' : 'inline'
  },
  
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
