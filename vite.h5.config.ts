import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import path from "path"

// 定义vite编译vue时的参数
const pluginVueOptions = {
  template: {
    transformAssetUrls: {
      video: ["src", "poster"],
      source: ["src"],
      img: ["src"],
    },
  },
}

export default defineConfig({
  plugins: [vue(pluginVueOptions)],
  build: {
    rollupOptions: {
      manualChunks: {
        vendor: ["vue"],
      },
      input: {
        main: path.resolve(__dirname, "/h5/position.html"),
      }
    },
  },

  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
})
