{"name": "itp-operation-web", "version": "1.2.0", "description": "ICB - ITP系统", "scripts": {"dev": "vite --config vite.config.ts --host", "dev:h5": "vite --config vite.h5.config.ts --host", "build:test": "vite build --mode test", "build:production": "vite build --mode production", "preview": "vite preview"}, "repository": {"type": "git", "url": "****************:linglu/itp/itp-operation-web.git"}, "author": "ICB", "license": "ISC", "dependencies": {"@ant-design/icons-vue": "6.1.0", "@tailwindcss/postcss": "^4.1.5", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.3", "ant-design-vue": "^4.0.3", "ant-design-x-vue": "^1.0.5", "async-validator": "^4.2.5", "autoprefixer": "^9", "axios": "0.27.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "docx-preview": "^0.1.14", "echarts": "^5.5.1", "handlebars": "^4.7.8", "js-cookie": "^3.0.1", "markdown-it": "^14.1.0", "mhtml2html": "^3.0.0", "pinia": "2.0.14", "postcss": "^7", "qrcode": "^1.5.1", "query-string": "^7.1.1", "quill": "^1.3.6", "rtf.js": "3.0.9", "store": "2.0.12", "tailwindcss": "^4.1.5", "uuid": "^9.0.1", "vant": "^4.6.6", "vue": "3.5.13", "vue-i18n": "9.1.10", "vue-pdf-embed": "1.1.4", "vue-router": "4.0.16", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@types/node": "18.0.1", "@types/qrcode": "^1.5.0", "@types/store": "2.0.2", "@types/uuid": "^9.0.5", "@vitejs/plugin-vue": "4.2.3", "less": "4.1.3", "pug": "3.0.2", "rollup-plugin-visualizer": "5.6.0", "sass": "1.53.0", "typescript": "4.7.4", "vite": "4.3.9", "vue-tsc": "0.35.0"}}